import type { Config, Context } from "@netlify/edge-functions";

export default async (request: Request, context: Context) => {
  const req_url = request.url;
  const res = await fetch("https://mwg.atlantixdigital.com/storefront/api/redirects/edgefunctions?path=" + req_url);
  if (res.status === 200) {
    const content = await res.json()
    if (content && content["redirect_to"]) {
      console.log("redirecting " + req_url + " to " + content["redirect_to"]);
      return new URL(content["redirect_to"], req_url);
    }
  }
};

export const config: Config = {
  path: "/*",
  excludedPath: ["/media/*", "/favicon*", "/icons/*", "/static/*", "/assets/*", "/*.css", "/*.js", "/*.js", "/page-data/*", "/*.json", "/manifest.webmanifest", "/*.txt", "/*.map"]
};
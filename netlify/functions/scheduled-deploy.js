import fetch from "node-fetch";
import { schedule } from "@netlify/functions";

const BUILD_HOOK = `${process.env.GATSBY_BUILD_HOOK_URL}`;

const handler = schedule("0 14-23 * * *", async () => {
  await fetch(BUILD_HOOK, {
    method: "POST",
  })
    .then((response) => {
      console.log("Build hook response:", response);
    })
    .catch((error) => console.error(error));

  return {
    statusCode: 200,
  };
});

export { handler };
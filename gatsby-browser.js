import React from "react";
import { SWRConfig } from "swr";
import { ToastContainer } from "react-toastify";
import { Provider } from "react-redux";
import store from "./src/redux/store";
import ChatApp from "./src/components/chatApp/ChatApp";
import AuthPollingProvider from "./src/components/auth/AuthPollingProvider";
import { navigate } from "gatsby";
import Api from './src/services/Api'

const swrConfig = {
  revalidateOnFocus: false,
  shouldRetryOnError: false,
  revalidateOnReconnect: true,
  fetcher: (url) => fetch(url).then((r) => r.json()),
};

export const wrapRootElement = ({ element }) => {
  return (
    <Provider store={store}>
      <SWRConfig value={swrConfig}>
        <AuthPollingProvider>
          <ChatApp>{element}</ChatApp>
          <ToastContainer
            position="top-right"
            className="toastify_container"
            toastClassName="toastify_root"
            bodyClassName="toastify_body"
            hideProgressBar
            closeButton={false}
            closeOnClick={false}
          />
        </AuthPollingProvider>
      </SWRConfig>
    </Provider>
  );
};

export function wrapPageElement({ element, props }) {
  const Layout = element.type.Layout ?? React.Fragment;
  return <Layout {...props}>{element}</Layout>;
}

const initListener = () => {
  const { user } = store.getState().auth;
  const eventMethod = window.addEventListener
    ? "addEventListener"
    : "attachEvent";
  const eventer = window[eventMethod];
  const messageEvent = eventMethod === "attachEvent" ? "onmessage" : "message";

  const listenerFunction = async function (e) {
    const key = e.message ? "message" : "data";
    const data = e[key];

    if (data.from === "bc") {
      switch (data.type) {
        case "loadCart":
          navigate("/cart");
          break;
        case "homePageRedirect":
          navigate("/");
          break;
        case "orderConfirmation":
          if (user?.admin_username && data?.data?.orderId) {
            const payload = {
              order_id: data?.data?.orderId || '',
              username: user?.admin_username || '',
              customer_id: user?.customer_id
            }
            try {
              await Api.post('/order-notes', payload)
            } catch (error) {
              console.error('Error calling order-notes API:', error);
            }
          }
          break;
        default:
          break;
      }
    }
  };

  eventer(messageEvent, listenerFunction, false);

  return () => {
    eventer(messageEvent, listenerFunction, false);
  };
};

const hardRefresh = () => {
  window.location.href = window.location.href;
  window.location.reload(true);
};

const checkTimeToHardRefresh = () => {
  const firstLoadTime = localStorage.getItem("mw_lastreload");
  if (!firstLoadTime) {
    localStorage.setItem("mw_lastreload", new Date().getTime());
  } else {
    const currentTime = new Date().getTime();
    const timeElapsed = currentTime - parseInt(firstLoadTime);

    const refreshThreshold = 2 * 60 * 60 * 1000;

    if (timeElapsed >= refreshThreshold) {
      localStorage.setItem("mw_lastreload", new Date().getTime());
      hardRefresh();
    }
  }
};

const checkRedirect = () => {
  const currentPath = window.location.pathname;

  const res = fetch(
    `${process.env.GATSBY_STOREFRONT_API_URL}/redirects/edgefunctions?path=${currentPath}`
  )
    .then((res) => res.json())
    .then((data) => {
      if (data.redirect) {
        // redirect to the new path...
        navigate(data.redirect);
      }
    });

  return res;
};

export const shouldUpdateScroll = ({ routerProps }) => {
  // Prevent scroll to top on navigation
  if (routerProps.location.pathname === '/cart') {
    return false;
  }
  return true; // Default behavior for other routes
};

export const onRouteUpdate = () => {
  // redirect check on every route change ...
  checkRedirect();

  // Hide iframe on every page...
  // iframe will only visible for after clicking on proceed to checkout...
  const checkoutIframe = document.querySelector("#checkout-iframe");

  if (checkoutIframe) {
    // The iframe should be always hidden initially.
    checkoutIframe.style.display = "none";
  }

  if (window.location.pathname.includes("/checkout")) {
    initListener();
  }

  checkTimeToHardRefresh();
};

export const onServiceWorkerUpdateReady = () => {
  hardRefresh();
};

// Utility function to send messages to BigCommerce
const sendMessageToBigCommerce = (type, data = {}) => {
  try {
    const message = {
      from: 'gatsby',
      type,
      data,
      timestamp: new Date().getTime()
    };

    // Send message to parent window (BigCommerce)
    if (window.parent && window.parent !== window) {
      window.parent.postMessage(message, '*');
      console.log('Message sent to BigCommerce:', message);
    } else {
      console.warn('Not in iframe context, cannot send message to BigCommerce');
    }
  } catch (error) {
    console.error('Error sending message to BigCommerce:', error);
  }
};

// Make the function available globally
if (typeof window !== 'undefined') {
  window.sendMessageToBigCommerce = sendMessageToBigCommerce;
}

export const onInitialClientRender = async () => {
  // Make the function available globally after initial render
  if (typeof window !== 'undefined') {
    window.sendMessageToBigCommerce = sendMessageToBigCommerce;
  }
};

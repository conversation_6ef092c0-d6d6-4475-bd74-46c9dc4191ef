{"_args": [["gatsby-source-bigcommerce@0.3.7", "C:\\Users\\<USER>\\Projects\\AtlantixDigital\\headless\\bitbucket\\bc-headless"]], "_from": "gatsby-source-bigcommerce@0.3.7", "_id": "gatsby-source-bigcommerce@0.3.7", "_inBundle": false, "_integrity": "sha512-A+hISgQz6rLcq8xTXV57f9kmcjFxUfxM6ExX8PqvohIuX/BXq3UzWSXWtSJ3y4hYN+HyPRFzvSwjGmaOGU2ofQ==", "_location": "/gatsby-source-bigcommerce", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "gatsby-source-bigcommerce@0.3.7", "name": "gatsby-source-bigcommerce", "escapedName": "gatsby-source-bigcommerce", "rawSpec": "0.3.7", "saveSpec": null, "fetchSpec": "0.3.7"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmjs.org/gatsby-source-bigcommerce/-/gatsby-source-bigcommerce-0.3.7.tgz", "_spec": "0.3.7", "_where": "C:\\Users\\<USER>\\Projects\\AtlantixDigital\\headless\\bitbucket\\bc-headless", "author": {"name": "Third and Grove", "email": "https://www.thirdandgrove.com"}, "bugs": {"url": "https://github.com/thirdandgrove/gatsby-source-bigcommerce/issues"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"babel-preset-gatsby-package": "^2.19.0", "micro": "^9.3.4", "node-fetch": "^9.4.0"}, "description": "gatsby plugin for Big Commerce api", "devDependencies": {"@babel/cli": "^7.0.0", "@babel/core": "^7.0.0", "cross-env": "^5.1.4"}, "homepage": "https://github.com/thirdandgrove/gatsby-source-bigcommerce/blob/master/README.md", "keywords": ["gatsby", "gatsby-plugin", "gatsby-source-bigcommerce", "plugin", "source", "big", "commerce", "bigcommerce", "api"], "license": "MIT", "name": "gatsby-source-bigcommerce", "peerDependencies": {"gatsby": "^4.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/thirdandgrove/gatsby-source-bigcommerce.git"}, "scripts": {"build": "babel src --out-dir .", "prepublishOnly": "npm run build"}, "version": "0.3.7"}
const fetch = require("node-fetch");

module.exports = async function ({ createNode, createContentDigest }) {
  let url = `https://api.bigcommerce.com/stores/${process.env.GATSBY_API_STORE_HASH}/v3/settings/storefront/seo`;

  const options = {
    method: 'GET',
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
      'X-Auth-Token': `${process.env.GATSBY_API_TOKEN}`
    }
  };

  try {
    const res = await fetch(url, options)
      .then(res => res.json())
      .catch(err => console.error('error:' + err));

    if (res?.['data']) {
      return createNode({
        ...res['data'],
        id: 'defaultSeoDetails',
        internal: {
          type: "defaultSeoDetails",
          contentDigest: createContentDigest(res['data']),
        },
      });
    }
  } catch (e) {
    console.error(e)
  }

};

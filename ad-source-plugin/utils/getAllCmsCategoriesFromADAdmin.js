const fetch = require("node-fetch");
const page_default_schema_categories = require("../config/page_default_schema_categories");

module.exports = async function ({ createNode, createContentDigest }) {
  let queryParams = new URLSearchParams({
    page: 1,
    limit: 1000,
    filter: "",
    sort_by: "date_created",
    sort_order: "desc",
  });
  let isFirstPage = true;
  while (true) {
    try {
      const categories = await fetch(
        `${process.env.GATSBY_ADMIN_API_URL}/products/categories?${queryParams}`,
        {
          method: "GET",
          headers: {
            origin: `${process.env.GATSBY_ADMIN_ORIGIN}`,
            "X-AUTH-TOKEN": `${process.env.GATSBY_X_AUTH_TOKEN}`,
            "x-store-id": `${process.env.GATSBY_API_STORE_ID}`,
            "X-CLIENT-ID": `${process.env.GATSBY_X_CLIENT_ID}`,
          },
        }
      ).then((i) => i.json());

      if (categories?.["data"] && categories["data"].length) {
        const nodesToCreate = isFirstPage
          ? [page_default_schema_categories, ...categories["data"]]
          : categories["data"];
        nodesToCreate.forEach((node) => {
          return createNode({
            ...node,
            id: node.id,
            internal: {
              type: "dynamicCategories",
              contentDigest: createContentDigest(node),
            },
          });
        });
      }

      if (
        Object.keys(categories["meta"]["pagination"]).length === 0 ||
        !categories["meta"]["pagination"]["next_page"]
      ) {
        break;
      } else {
        // Update URL parameter.
        queryParams.set("page", +queryParams.get("page") + 1);
        isFirstPage = false;
      }
    } catch (e) {
      console.error(e);
    }
  }
};

const fetch = require("node-fetch");
const blog_default_schema = require("../config/blog_default_schema");

module.exports = async function ({ createNode, createContentDigest }) {
  let queryParams = new URLSearchParams({
    page: 1,
    limit: 250,
    status: "",
    filter: "",
    sort_by: "name",
    sort_order: "desc",
  });
  let isFirstPage = true;
  while (true) {
    try {
      const blogs = await fetch(
        `${process.env.GATSBY_ADMIN_API_URL}/cms/blogs/${process.env.GATSBY_API_TENANT_ID}/${process.env.GATSBY_API_STORE_ID}/bloglist?${queryParams}`,
        {
          method: "GET",
          headers: {
            origin: `${process.env.GATSBY_ADMIN_ORIGIN}`,
            "X-AUTH-TOKEN": `${process.env.GATSBY_X_AUTH_TOKEN}`,
            "x-store-id": `${process.env.GATSBY_API_STORE_ID}`,
            "X-CLIENT-ID": `${process.env.GATSBY_X_CLIENT_ID}`,
          },
        }
      ).then((i) => i.json());

      if (blogs?.["data"] && blogs["data"]?.length) {
        const nodesToCreate = isFirstPage
          ? [blog_default_schema, ...blogs["data"]]
          : blogs["data"];
        nodesToCreate.forEach((node) => {
          return createNode({
            ...node,
            id: node.id,
            internal: {
              type: "dynamicBlogs",
              contentDigest: createContentDigest(node),
            },
          });
        });
      }

      if (
        Object.keys(blogs["meta"]["pagination"]).length === 0 ||
        !blogs["meta"]["pagination"]["next_page"]
      )
        break;
      // Update URL parameter.
      else queryParams.set("page", +queryParams.get("page") + 1);
      isFirstPage = false;
    } catch (e) {
      console.error(e);
    }
  }
};

const fetch = require("node-fetch");

module.exports = async function ({
  createNode,
  createContentDigest,
  short_code,
  short_code_key,
}) {
  const res = await fetch(
    `${process.env.GATSBY_ADMIN_API_URL}/storefronts/${process.env.GATSBY_API_TENANT_ID}/${process.env.GATSBY_API_STORE_ID}/navigation/${short_code}`,
    {
      method: "GET",
      headers: {
        origin: `${process.env.GATSBY_ADMIN_ORIGIN}`,
        "X-AUTH-TOKEN": `${process.env.GATSBY_X_AUTH_TOKEN}`,
        "x-store-id": `${process.env.GATSBY_API_STORE_ID}`,
        "X-CLIENT-ID": `${process.env.GATSBY_X_CLIENT_ID}`,
      },
    }
  )
    .then((i) => i.json())
    .catch((e) => console.error(e));

  if (res && res.navigations.length) {
    process.env[`GATSBY_${short_code_key}`] = JSON.stringify(res);
  }
};

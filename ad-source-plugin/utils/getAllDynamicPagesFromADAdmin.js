const fetch = require("node-fetch");
const page_default_schema = require("../config/page_default_schema");

module.exports = async function ({ createNode, createContentDigest }) {
  let queryParams = new URLSearchParams({
    page: 1,
    limit: 250,
    status: "",
    filter: "",
    sort_by: "date_created",
    sort_order: "desc",
  });
  let isFirstPage = true;
  while (true) {
    try {
      const pages = await fetch(
        `${process.env.GATSBY_ADMIN_API_URL}/cms/webpages/${process.env.GATSBY_API_TENANT_ID}/${process.env.GATSBY_API_STORE_ID}/storefront?${queryParams}`,
        {
          method: "GET",
          headers: {
            origin: `${process.env.GATSBY_ADMIN_ORIGIN}`,
            "X-AUTH-TOKEN": `${process.env.GATSBY_X_AUTH_TOKEN}`,
            "x-store-id": `${process.env.GATSBY_API_STORE_ID}`,
            "X-CLIENT-ID": `${process.env.GATSBY_X_CLIENT_ID}`,
          },
        }
      ).then((i) => i.json());

      if (pages?.["data"] && pages["data"].length) {
        const nodesToCreate = isFirstPage
          ? [page_default_schema, ...pages["data"]]
          : pages["data"];
        nodesToCreate.forEach((node) => {
          return createNode({
            ...node,
            id: node.id,
            internal: {
              type: "dynamicPages",
              contentDigest: createContentDigest(node),
            },
          });
        });
      }

      if (
        Object.keys(pages["meta"]["pagination"]).length === 0 ||
        !pages["meta"]["pagination"]["next_page"]
      ) {
        break;
      } else {
        // Update URL parameter.
        queryParams.set("page", +queryParams.get("page") + 1);
        isFirstPage = false;
      }
    } catch (e) {
      console.error(e);
    }
  }
};

const fetch = require("node-fetch");

module.exports = async function () {
  const res = await fetch(
    `${process.env.GATSBY_ADMIN_API_URL}/tenant/${process.env.GATSBY_API_TENANT_ID}/envcreds/${process.env.GATSBY_API_STORE_ID}`,
    {
      method: "GET",
      headers: {
        origin: `${process.env.GATSBY_ADMIN_ORIGIN}`,
        "X-AUTH-TOKEN": `${process.env.GATSBY_X_AUTH_TOKEN}`,
        "x-store-id": `${process.env.GATSBY_API_STORE_ID}`,
        "X-CLIENT-ID": `${process.env.GATSBY_X_CLIENT_ID}`,
      },
    }
  )
    .then((i) => i.json())
    .catch((e) => console.error(e));

  if (res && res.data) {
    const { env_variables, navigation_ids } = res.data;
    return {
      envs: env_variables,
      short_codes: navigation_ids,
    };
  } else {
    return {
      envs: {},
      short_codes: {},
    };
  }
};

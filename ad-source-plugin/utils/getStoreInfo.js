const fetch = require("node-fetch");

module.exports = async function () {
  const res = await fetch(
    `${process.env.GATSBY_ADMIN_API_URL}/storefronts/storeinfo`,
    {
      method: "GET",
      headers: {
        origin: `${process.env.GATSBY_ADMIN_ORIGIN}`,
        "X-AUTH-TOKEN": `${process.env.GATSBY_X_AUTH_TOKEN}`,
        "x-store-id": `${process.env.GATSBY_API_STORE_ID}`,
        "X-CLIENT-ID": `${process.env.GATSBY_X_CLIENT_ID}`,
      },
    }
  )
    .then((i) => i.json())
    .catch((e) => console.error(e));

  if (res && res.length) {
    process.env[`GATSBY_HEADER_INFO`] = JSON.stringify(
      res?.[0]?.["content"]?.["header_info"]
    );
    process.env[`GATSBY_FOOTER_INFO`] = JSON.stringify(
      res?.[0]?.["content"]?.["footer_info"]
    );
    process.env[`GATSBY_AGE_VERIFICATION_INFO`] = JSON.stringify(
      res?.[0]?.["content"]?.["age_verification_info"]
    );
  }
};

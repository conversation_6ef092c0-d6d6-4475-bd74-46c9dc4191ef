const fetch = require("node-fetch");
const pageUrls = [
  "/7-daze/",
  "/dragon-glass/",
  "/khalil-maamoon/",
  "/white-rhino/",
  "/stock-updates/042723/",
  "/stock-updates/02152022/",
  "/scorch-torch-swivel-head-quad-flame-lighters-display-of-9-61536-1-msrp-15-00-each/",
  "/ocb/",
  "/lost-vape-quest-ursa-2-900mah-pod-system-starter-kit-with-2-x-refillable-2-5ml-ursa-cartridge-pod-msrp-24-99/",
  "/bxaroma-s3000-plus-wifi-electric-essential-oil-scent-diffuser-with-bluetooth-1000ml-msrp-450-00/",
  "/mr-fog-max-air-ma8500-17ml-8500-puffs-600mah-prefilled-nicotine-salt-rechargeable-disposable-with-mesh-coil-technology-display-of-10-msrp-19-99-each/",
  "/elf-bar/",
];
module.exports = async function ({ createRedirect }) {
  let queryParams = new URLSearchParams({
    page: 1,
    limit: 10000,
    filter: "",
    sort_by: "to_type",
    sort_order: "asc",
  });

  while (true) {
    try {
      const redirects = await fetch(
        `${process.env.GATSBY_ADMIN_API_URL}/redirects/${process.env.GATSBY_API_TENANT_ID}/${process.env.GATSBY_API_STORE_ID}/data?${queryParams}`,
        {
          method: "GET",
          headers: {
            origin: `${process.env.GATSBY_ADMIN_ORIGIN}`,
            "X-AUTH-TOKEN": `${process.env.GATSBY_X_AUTH_TOKEN}`,
            "x-store-id": `${process.env.GATSBY_API_STORE_ID}`,
            "X-CLIENT-ID": `${process.env.GATSBY_X_CLIENT_ID}`,
          },
        }
      ).then((i) => i.json());

      if (redirects?.["data"] && redirects["data"].length) {
        redirects["data"].forEach((node) => {
          // Create redirect.
          if (node?.from_path && node?.to_path) {
            if (!pageUrls.includes(node.from_path)) {
              createRedirect({
                fromPath: node.from_path,
                toPath: node.to_path,
                statusCode: 301,
                isPermanent: true,
                force: true,
                redirectInBrowser: true,
              });
            }
          }
        });
      }

      if (
        Object.keys(redirects["meta"]["pagination"]).length === 0 ||
        !redirects["meta"]["pagination"]["next_page"]
      ) {
        break;
      } else {
        // Update URL parameter.
        queryParams.set("page", +queryParams.get("page") + 1);
      }
    } catch (e) {
      console.error(e);
    }
  }
};

const fetch = require("node-fetch");
const page_default_schema_brands = require("../config/page_default_schema_brands");

module.exports = async function ({ createNode, createContentDigest }) {
  let queryParams = new URLSearchParams({
    page: 1,
    limit: 1000,
    status: "",
    filter: "",
    type: "brand",
    sort_by: "name",
    sort_order: "asc",
  });
  let isFirstPage = true;
  while (true) {
    try {
      const brands = await fetch(
        `${process.env.GATSBY_ADMIN_API_URL}/products/brands/${process.env.GATSBY_API_TENANT_ID}/${process.env.GATSBY_API_STORE_ID}/data?${queryParams}`,
        {
          method: "GET",
          headers: {
            origin: `${process.env.GATSBY_ADMIN_ORIGIN}`,
            "X-AUTH-TOKEN": `${process.env.GATSBY_X_AUTH_TOKEN}`,
            "x-store-id": `${process.env.GATSBY_API_STORE_ID}`,
            "X-CLIENT-ID": `${process.env.GATSBY_X_CLIENT_ID}`,
          },
        }
      ).then((i) => i.json());
      if (brands?.["data"] && brands["data"].length) {
        const nodesToCreate = isFirstPage
          ? [page_default_schema_brands, ...brands["data"]]
          : brands["data"];
        nodesToCreate.forEach((node) => {
          return createNode({
            ...node,
            id: node.id,
            internal: {
              type: "dynamicBrands",
              contentDigest: createContentDigest(node),
            },
          });
        });
      }

      if (
        Object.keys(brands["meta"]["pagination"]).length === 0 ||
        !brands["meta"]["pagination"]["next_page"]
      ) {
        break;
      } else {
        // Update URL parameter.
        queryParams.set("page", +queryParams.get("page") + 1);
        isFirstPage = false;
      }
    } catch (e) {
      console.error(e);
    }
  }
};

"use strict";

const BigCommerce = require("./bigcommerce");
const getEnvCredentials = require("./utils/getEnvCredentials");
const setNavigations = require("./utils/setNavigations");
const getAllDynamicPagesFromADAdmin = require("./utils/getAllDynamicPagesFromADAdmin");
const getAllBlogsFromADAdmin = require("./utils/getAllBlogsFromADAdmin");
const getBCStorefrontSeoDetails = require("./utils/getBCStorefrontSeoDetails");
const getAllCmsCategoriesFromADAdmin = require("./utils/getAllCmsCategoriesFromADAdmin");
const getAllCmsBrandsFromADAdmin = require("./utils/getAllCmsBrandsFromADAdmin");
const getStoreInfo = require("./utils/getStoreInfo");

exports.sourceNodes = async (
  { actions, createNodeId, createContentDigest },
  configOptions
) => {
  const { createNode } = actions;
  const { endpoint, endpoints, nodeName, apiVersion } = configOptions;

  // STEP 1 - FETCH ENVIRONMENT CREDENTIALS ...
  const { envs, short_codes } = await getEnvCredentials();

  // STEP 2 - SET ENVIRONMENT CREDENTIALS INTO ENV VARIABLE ...
  for (const key in envs) {
    process.env[`${key}`] = envs[key];
  }

  // STEP 3 - SET SHORT_CODES CREDENTIALS INTO ENV VARIABLE ...
  for (const [key, value] of Object.entries(short_codes)) {
    if (value !== "") {
      await setNavigations({
        createNode: createNode,
        createContentDigest: createContentDigest,
        short_code: short_codes[key],
        short_code_key: key,
      });
    }
  }

  // STEP 4 - FETCH STORINFO DETAILS...
  await getStoreInfo();

  // STEP 5 - FETCH DEFAULT STOREFRONT SEO DETAILS...
  await getBCStorefrontSeoDetails({ createNode, createContentDigest });

  // STEP 6 - FETCH DYNAMIC PAGES DATA AND ADD INTO GRAPHQL's DATA LAYER...
  await getAllDynamicPagesFromADAdmin({ createNode, createContentDigest });

  // STEP 7 - FETCH DYNAMIC BLOGS DATA ADD INTO GRAPHQL's DATA LAYER...
  await getAllBlogsFromADAdmin({ createNode, createContentDigest });

  await getAllCmsCategoriesFromADAdmin({ createNode, createContentDigest });

  await getAllCmsBrandsFromADAdmin({ createNode, createContentDigest });

  const bigCommerce = new BigCommerce({
    clientId: process.env.GATSBY_API_CLIENT_ID,
    accessToken: process.env.GATSBY_API_TOKEN,
    secret: process.env.GATSBY_API_SECRET,
    storeHash: process.env.GATSBY_API_STORE_HASH,
    apiVersion,
    responseType: "json",
  });

  if (!endpoint && !endpoints) {
    console.log(
      "You have not provided a Big Commerce API endpoint, please add one to your gatsby-config.js"
    );
    return;
  }

  const handleGenerateNodes = (node, name) => {
    return {
      ...node,
      id: createNodeId(name + node.id),
      bigcommerce_id: node.id,
      parent: null,
      children: [],
      internal: {
        type: name,
        content: JSON.stringify(node),
        contentDigest: createContentDigest(node),
      },
    };
  };

  endpoint // Fetch and create nodes for a single endpoint.
    ? await bigCommerce.get(endpoint).then((res) => {
        // If the data object is not on the response, it could be v2 which returns an array as the root, so use that as a fallback
        const resData = res.data ? res.data : res;
        return resData.map((datum) =>
          createNode(handleGenerateNodes(datum, nodeName))
        );
      }) // Fetch and create nodes from multiple endpoints
    : await Promise.all(
        Object.entries(endpoints).map(([nodeName, endpoint]) => {
          //fetchBCData(endpoint);
          return bigCommerce.get(endpoint).then((res) => {
            // If the data object is not on the response, it could be v2 which returns an array as the root, so use that as a fallback
            const resData = res.data ? res.data : res;
            // console.log(
            //   "Data fetched for endpoint: ",
            //   endpoint,
            //   resData.length
            // );
            return resData.map((datum) =>
              createNode(handleGenerateNodes(datum, nodeName))
            );
          });
        })
      );
};

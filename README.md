# Gatsby Headless

## 🚀 Quick start

Note: This project uses Gatsby ^4.24.3

### Prerequisites

- Node `v18.16.0` Recommended.
- create `.env.development` file to store credentials for local environment.
- create `.env.production` file to store credentials for production environment.

### Start Project In Local

- Clone project from gitlab using command `git clone clone-url...`
- Install all the Dependencies using command `yarn install`
- Start locally run `yarn start`

To test the locally, you'll need run a production build of the site:

`$ yarn build`

`$ yarn serve`

### Global Configurations

- `config.js` file contains all the global default theme configurations.

### Airbnb React/JSX Style Guide

- Project follows Airbnb React/JSX Style Guide - https://github.com/airbnb/javascript/tree/master/react#naming

Happy coding! 🚀



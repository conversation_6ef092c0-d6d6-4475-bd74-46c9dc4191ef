const path = require("path");
const compression = require("compression");
const createAllRedirects = require("./ad-source-plugin/utils/createAllRedirects");

const parseCustomUrl = (url) => {
  return url.replace(/[/]/g, "");
};

exports.onCreateDevServer = ({ app }) => {
  app.use(compression());
};

exports.createPages = async ({ actions, graphql }) => {
  const { createPage, createRedirect } = actions;

  const result = await graphql(`
    {
      markdownRemark {
        id
        frontmatter {
          pages {
            description
            url
            templateKey
            title
          }
        }
      }
      allBigCommerceProducts {
        nodes {
          id
          name
          bigcommerce_id
          categories
          custom_url {
            url
          }
          page_title
          meta_description
          brand_id
        }
      }
      allDynamicPages(filter: { id: { ne: "000" } }) {
        edges {
          node {
            id
            url
            versions {
              status
              seo_details {
                meta_description
                meta_title
              }
            }
          }
        }
      }
      allDynamicCategories(filter: { id: { ne: "222" } }) {
        edges {
          node {
            id
            url
            name
            bc_id
            parent_id
            versions {
              status
              seo_details {
                meta_description
                meta_title
              }
            }
          }
        }
      }
      allDynamicBrands(filter: { id: { ne: "333" } }) {
        edges {
          node {
            id
            url
            versions {
              status
              seo_details {
                meta_description
                meta_title
              }
            }
          }
        }
      }
      allDynamicBlogs(filter: { id: { ne: "111" } }) {
        edges {
          node {
            id
            index
            url
            data {
              image_url
            }
          }
        }
      }
      defaultSeoDetails {
        id
        page_title
        meta_keywords
        meta_description
      }
    }
  `);

  if (result.errors) {
    result.errors.forEach((e) => console.error(e.toString()));
    return Promise.reject(result.errors);
  }

  const storefrontSeoDetails = result.data.defaultSeoDetails;

  const dynamicPages = result.data.allDynamicPages.edges;
  createDynamicPages({ createPage, data: dynamicPages, storefrontSeoDetails });

  const dynamicBlogs = result.data.allDynamicBlogs.edges;
  createDynamicBlogs({ createPage, data: dynamicBlogs, storefrontSeoDetails });
  createDynamicBlogListingPage({
    createPage,
    data: dynamicBlogs,
    storefrontSeoDetails,
  });

  const products = result.data.allBigCommerceProducts.nodes;
  createProductDetailsPage({
    createPage,
    data: products,
    storefrontSeoDetails,
  });

  const dynamicCategories = result.data.allDynamicCategories.edges;
  createDynamicCategories({
    createPage,
    data: dynamicCategories,
    storefrontSeoDetails,
  });

  const dynamicBrands = result.data.allDynamicBrands.edges;

  // const brands = result.data.allBigCommerceBrands.edges;
  createDynamicBrands({
    createPage,
    data: dynamicBrands,
    storefrontSeoDetails,
  });
  // createBrandDetailsPage({ createPage, data: brands, storefrontSeoDetails });
  createBrandListingPage({
    createPage,
    data: dynamicBrands,
    storefrontSeoDetails,
  });

  const markdownPages = result.data.markdownRemark.frontmatter;
  createMarkdownPages({
    createPage,
    data: markdownPages,
    storefrontSeoDetails,
  });
  createLoyaltyAppPages({ createPage });

  // Create Redirects
  // keep this at the end of the function.
  //await createAllRedirects({ createRedirect });
};

// create loyalty app pages
const createLoyaltyAppPages = ({ createPage }) => {
  const loyaltyTabs = ["redeem-coupon", "my-rewards", "history"];
  loyaltyTabs.forEach((tab, index) => {
    createPage({
      path: `/loyalty-and-rewards/${tab}/`,
      component: path.resolve(`src/templates/loyalty-and-rewards-page.jsx`), // Use the same component for all tabs
      context: {
        activeTab: tab,
        id: index,
      },
    });
  });
};

// webpages -----------------------------------------------------------
const createDynamicPages = ({ createPage, data, storefrontSeoDetails }) => {
  const { page_title, meta_description } = storefrontSeoDetails;

  data.forEach((item) => {
    createPage({
      path: `${item.node.url || "/"}`,
      component: path.resolve(`src/templates/dynamic-page.jsx`),
      context: {
        id: item.node.id,
        // metaTitle: item.versions.seo_details[0].meta_title || page_title,
        // metaDescription: item.versions.seo_details[0].meta_description || meta_description,
      },
    });
  });
};

// Blog Detail Page. -----------------------------------------------------
const createDynamicBlogs = ({ createPage, data, storefrontSeoDetails }) => {
  data.forEach((item) => {
    createPage({
      path: `${item.node.url}`,
      component: path.resolve(`src/templates/dynamic-blog.jsx`),
      context: {
        id: item.node.id,
        storefrontSeoDetails: storefrontSeoDetails,
      },
    });
  });
};

// Blog Listing Page. - (route="/blogs") -----------------------------------------------------
const createDynamicBlogListingPage = ({
  createPage,
  data,
  storefrontSeoDetails,
}) => {
  const limitBlogPage = 6;
  const totalBlogsPages = Math.ceil(data.length / limitBlogPage);

  let blogsIndex = 0;

  createPage({
    path: `/blogs`,
    component: path.resolve(`src/templates/blogs-page.jsx`),
    context: {
      limit: 7,
      skip: 0,
      totalPages: totalBlogsPages,
      currentPage: 1,
      storefrontSeoDetails: storefrontSeoDetails,
    },
  });

  blogsIndex = 1;

  while (blogsIndex < totalBlogsPages) {
    createPage({
      path: `/blogs/${blogsIndex + 1}`,
      component: path.resolve(`src/templates/blogs-page.jsx`),
      context: {
        limit: 6,
        skip: blogsIndex * limitBlogPage + 1,
        totalPages: totalBlogsPages,
        currentPage: blogsIndex + 1,
        storefrontSeoDetails: storefrontSeoDetails,
      },
    });

    // Increment counter
    blogsIndex += 1;
  }
};

// Product details page. -----------------------------------------------------
const createProductDetailsPage = ({
  createPage,
  data,
  storefrontSeoDetails,
}) => {
  data.forEach(
    ({
      custom_url,
      id,
      brand_id,
      bigcommerce_id,
      page_title,
      meta_description,
      name,
    }) => {
      try {
        createPage({
          path: `/${parseCustomUrl(custom_url.url)}`,
          component: path.resolve(`src/templates/product-details.jsx`),
          context: {
            productId: id,
            brandId: brand_id,
            bigcommerceId: bigcommerce_id,
            pageTitle: page_title,
            metaDescription: meta_description,
            name: name,
            storefrontSeoDetails: storefrontSeoDetails,
          },
        });
      } catch (error) {
        console.error(error);
        console.log(
          "error product details page - ",
          custom_url,
          id,
          brand_id,
          bigcommerce_id,
          page_title,
          meta_description,
          name
        );
      }
    }
  );
};

//Categories ------------------------------------------------------------
const createDynamicCategories = ({
  createPage,
  data,
  storefrontSeoDetails,
}) => {
  const { page_title, meta_description } = storefrontSeoDetails;

  // Create bc_id -> node map
  const categoryMap = {};
  data.forEach(({ node }) => {
    if (node.bc_id !== null) categoryMap[node.bc_id] = node;
  });

  // Helper to compute name hierarchy
  const buildHierarchyName = (node) => {
    const names = [];
    let current = node;
    while (current && current.name) {
      names.unshift(current.name);
      const parentId = current.parent_id;
      current = categoryMap[`${parentId}`];
    }
    return names.join(">");
  };

  // Loop over each category to create a page
  data.forEach((item) => {
    const node = item.node;

    const hierarchyName = buildHierarchyName(node);
    const seo = node.versions?.[0]?.seo_details?.[0] || {};

    createPage({
      path: `${node.url || "/"}`,
      component: path.resolve(`src/templates/cms-category-page.jsx`),
      context: {
        id: node.id,
        hierarchyName: hierarchyName,
        metaTitle: seo.meta_title || page_title,
        metaDescription: seo.meta_description || meta_description,
      },
    });
  });
};

//Brands -----------------------------------------------------------------
const createDynamicBrands = ({ createPage, data, storefrontSeoDetails }) => {
  const { page_title, meta_description } = storefrontSeoDetails;

  data.forEach((item) => {
    createPage({
      path: `${item.node.url || "/"}`,
      component: path.resolve(`src/templates/cms-brand-page.jsx`),
      context: {
        id: item.node.id,
      },
    });
  });
};

// Brand detail page. -----------------------------------------------------
// const createBrandDetailsPage = ({ createPage, data, storefrontSeoDetails }) => {
//   data.forEach(
//     ({
//       node: { custom_url, bigcommerce_id, page_title, meta_description, name },
//     }) => {
//       try {
//         createPage({
//           path: `${custom_url.url}`,
//           component: path.resolve(`src/templates/brand-page.jsx`),
//           context: {
//             brandId: bigcommerce_id,
//             pageTitle: page_title,
//             metaDescription: meta_description,
//             name: name,
//             storefrontSeoDetails: storefrontSeoDetails,
//           },
//         });
//       } catch (error) {
//         console.error(error);
//         console.log(
//           "error brand details page - ",
//           custom_url,
//           bigcommerce_id,
//           page_title,
//           meta_description,
//           name
//         );
//       }
//     }
//   );
// };

// Brand Listing Page - (route="/brands") -----------------------------------------------------
const createBrandListingPage = ({ createPage, data, storefrontSeoDetails }) => {
  const limitBrandPage = 120;
  const totalBrandsPages = Math.ceil(data.length / limitBrandPage);
  // For Route - /brands
  createPage({
    path: `/brands`,
    component: path.resolve(`src/templates/brands-page.jsx`),
    context: {
      limit: limitBrandPage,
      skip: 0,
      totalPages: totalBrandsPages,
      currentPage: 1,
      storefrontSeoDetails: storefrontSeoDetails,
    },
  });

  let brandsIndex = 1;

  // For Routes - /brands, /brands/2, ...
  while (brandsIndex < totalBrandsPages) {
    createPage({
      path: `/brands/${brandsIndex + 1}`,
      component: path.resolve(`src/templates/brands-page.jsx`),
      context: {
        limit: limitBrandPage,
        skip: brandsIndex * limitBrandPage,
        totalPages: totalBrandsPages,
        currentPage: brandsIndex + 1,
        storefrontSeoDetails: storefrontSeoDetails,
      },
    });

    brandsIndex += 1;
  }
};

// Markdown pages - will create page from markdown folder. -------------------------------------------------
const createMarkdownPages = ({ createPage, data, storefrontSeoDetails }) => {
  data.pages.forEach((page) => {
    const { title, description, url, templateKey } = page;

    // For route=/products...
    if (url.includes("products")) {
      createPage({
        path: `/products`,
        component: path.resolve(`src/templates/products-page.jsx`),
      });
    } else {
      createPage({
        path: url,
        component: path.resolve(`src/templates/${String(templateKey)}.jsx`),
        // additional data can be passed via context...
        context: {
          pageTitle: title,
          metaDescription: description,
        },
      });
    }
  });
};

require("dotenv").config({
  path: `.env.${process.env.NODE_ENV}`,
});

module.exports = {
  siteMetadata: {
    title: "Wholesale Vape Shop, Smoke Shop,  & Headshop Supply",
    description:
      "Wholesale Vape, Smoke Shop & Headshop Supplies featuring E liquid, E juice, Vaporizers, Atomizers, Batteries, Vape Mods, Box mods, Coils, Glass Water Pipes, Bongs, Hookah, Rolling Papers and Trays & More",
    siteUrl: process.env.GATSBY_STOREFRONT_URL,
  },
  plugins: [
    {
      resolve: "gatsby-plugin-netlify",
      options: {
        headers: {
          "/public/**/*.html": [
            "cache-control: public",
            "cache-control:  max-age=86400",
            "cache-control: must-revalidate",
          ],
          "/sw.js": [
            "cache-control: public",
            "cache-control:  max-age=86400",
            "cache-control: must-revalidate",
          ],
          "/public/page-data/*": [
            "cache-control: public",
            "cache-control:  max-age=86400",
            "cache-control: must-revalidate",
          ],
        },
      },
    },
    {
      resolve: "../ad-source-plugin",
      options: {
        // REQUIRED
        endpoints: {
          BigCommerceProducts:
            "/catalog/products?include=images,bulk_pricing_rules,custom_fields,videos&limit=250&include_fields=id,price,name,bigcommerce_id,custom_url,related_products,sku,brand_id,page_title,meta_description,categories,upc,order_quantity_minimum,order_quantity_maximum,weight,condition,availability,description,warranty,reviews_count,reviews_rating_sum,reviews,categories&is_visible=true",
          BigCommerceBrands: "/catalog/brands?limit=250&sort=name",
        },
      },
    },
    "gatsby-plugin-sass",
    {
      resolve: "gatsby-plugin-sitemap",
      options: {
        entryLimit: 10000,
      },
    },
    {
      resolve: "gatsby-plugin-html-attributes",
      options: {
        lang: "en",
      },
    },
    {
      resolve: "gatsby-plugin-webpack-bundle-analyser-v2",
      options: {
        devMode: false,
      },
    },
    {
      resolve: "gatsby-source-filesystem",
      options: {
        path: `${__dirname}/src/markdown`,
        name: "markdown-pages",
      },
    },
    {
      resolve: `gatsby-plugin-manifest`,
      options: {
        name: "Midwest Goods",
        short_name: "Midwest Goods",
        start_url: "/",
        background_color: "#09254A",
        theme_color: "#09254A",
        // Enables "Add to Homescreen" prompt and disables browser UI (including back button)
        // see https://developers.google.com/web/fundamentals/web-app-manifest/#display
        display: "standalone",
        icon: "src/img/faviconMW.png", // This path is relative to the root of the site.
        // An optional attribute which provides support for CORS check.
        // If you do not provide a crossOrigin option, it will skip CORS for manifest.
        // Any invalid keyword or empty string defaults to `anonymous`
        crossOrigin: `use-credentials`,
      },
    },
    {
      resolve: "gatsby-source-filesystem",
      options: {
        path: `${__dirname}/src/img`,
        name: "img",
      },
    },
    "gatsby-plugin-image",
    "gatsby-plugin-sharp",
    "gatsby-transformer-sharp",
    {
      resolve: "gatsby-transformer-remark",
      options: {
        plugins: [
          {
            resolve: "gatsby-remark-relative-images",
            options: {
              name: "uploads",
            },
          },
          {
            resolve: "gatsby-remark-images",
            options: {
              // It's important to specify the maxWidth (in pixels) of
              // the content container as this plugin uses this as the
              // base for generating different widths of each image.
              maxWidth: 2048,
            },
          },
        ],
      },
    },
    {
      resolve: "gatsby-plugin-robots-txt",
      options: {
        host: process.env.GATSBY_STOREFRONT_URL,
        // policy: [{ userAgent: '*', disallow: ['/'] }],
      },
    },
    {
      resolve: `gatsby-plugin-lucky-orange`,
      options: {
        id: 331704,
      },
    },
  ],
  flags: {
    DEV_SSR: true,
  },
};

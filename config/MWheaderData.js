const MWProductionData = [
  {
    label: "New In Stock",
    url: "/in-stock-new-arrivals/",
    children: [],
  },
  {
    label: "New Dropship",
    url: "/drop-ship-new-arrivals/",
    children: [],
  },
  {
    label: "Devices",
    url: "/regulated-devices-box-mods/",
    children: [],
  },
  {
    label: "E-Liquids",
    url: "/e-liquids/",
    children: [],
  },
  {
    label: "Pod Systems",
    url: "/pod-systems-and-pods/",
    children: [],
  },
  {
    label: "Glass",
    url: "/glass-accessories/",
    children: [],
  },
  {
    label: "Disposables",
    url: "/disposables/",
    children: [],
  },
  {
    label: "CBD",
    url: "https://cbdtostore.com/",
    children: [],
  },
];

const MWDevelopmentData = [
  {
    label: "New In Stock",
    url: "/in-stock-new-arrivals-1/",
    children: [],
  },
  {
    label: "New Dropship",
    url: "/drop-ship-new-arrivals-1/",
    children: [],
  },
  {
    label: "Devices",
    url: "/regulated-devices-box-mods-starter-kits-1/",
    children: [],
  },
  {
    label: "E-Liquids",
    url: "/shop-all-e-liquids-1/",
    children: [],
  },
  {
    label: "Pod Systems",
    url: "/pod-systems-and-pods-1/",
    children: [],
  },
  {
    label: "Glass",
    url: "/glass-pipes-and-accessories-1/",
    children: [],
  },
  {
    label: "Disposables",
    url: "/disposable-e-cigarettes-vape-pens/",
    children: [],
  },
  {
    label: "CBD",
    url: "https://cbdtostore.com/",
    children: [],
  },
];

export function MWheaderData() {
  if (process.env.GATSBY_ENVIRONMENT === "production") {
    return MWProductionData;
  } else {
    return MWDevelopmentData;
  }
}

export default MWheaderData;

import useSWR from "swr";
import Api from '../services/Api'
import { useSelector } from "react-redux";

const useGetSubscriptionDetails = () => {
  const { user } = useSelector((state) => state.auth);

  const getKey = () => {
    return user ? `/customer/${user?.customer_id}/licence/expiry` : null;
  };

  const { data } = useSWR(() => getKey(), getSubscriptionDetails);

  return {
    data,
  };
};

export default useGetSubscriptionDetails;

async function getSubscriptionDetails(url) {
  const response = await Api.get(url);
  return response.data;
}

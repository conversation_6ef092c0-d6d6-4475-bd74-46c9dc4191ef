import useSWR from "swr";
import Api from "../../services/Api";
import { getorderLoyaltyPointsUrl } from "../../ApiEndpoints";
import { useSelector } from "react-redux";

function useGetCheckloyaltypointsVisibility() {
  const { user } = useSelector((state) => state.auth);

  const getKey = () => {
    return getorderLoyaltyPointsUrl(user?.customer_id);
  };

  const { data, error } = useSWR(() => getKey(), getLoyaltyPointsVisibilityUrl);
  const isChecking = (!data && !error) || false;

  return {
    loyalty_points_visible: data?.loyalty_point_visible,
    isChecking,
  };
}

async function getLoyaltyPointsVisibilityUrl(url) {
  const response = await Api.get(url);
  return response.data;
}

export default useGetCheckloyaltypointsVisibility;

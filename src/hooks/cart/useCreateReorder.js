import { useState, useEffect } from 'react'
import useToastify from '../ui/useToastify'
import Api from '../../services/Api'
import { reorderURL } from '../../ApiEndpoints'
import { navigate } from 'gatsby'
import { debounce } from '../../utils/functions'

let debouncedFunction = null;

function useCreateReorder() {
  const [isLoading, setLoading] = useState(false)
  const { toastMessage } = useToastify()

  useEffect(() => {
    debouncedFunction = debounce(async (url, data) => {
      try {
        setLoading(true)
        const res = await reorder(url, data)

        if (res.status !== 200) {
          toastMessage("error", res.error || res.data.error);
        }

        if (res.status === 200) {
          navigate('/cart')
        }

        setLoading(false)
      } catch (e) {
        console.error(e)
        setLoading(false)
      }
      // 1 second
    }, 1000);
  }, []);

  async function reorderProducts(data) {
    debouncedFunction(reorderURL, data);
  }

  return {
    reorderProducts: reorderProducts,
    isLoading: isLoading
  }
}

async function reorder(url, data) {
  const response = await Api.post(url, data)
  return response.data
}

export default useCreateReorder
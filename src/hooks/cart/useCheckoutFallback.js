import { useState } from 'react'
import { checkoutURL } from '../../ApiEndpoints';
import { useSelector } from 'react-redux';
import useSetCheckoutIframe from './useSetCheckoutIframe';
import Api from "../../services/Api";
import { mutate } from 'swr';

function useCheckoutFallback() {
  const { user } = useSelector((state) => state.auth);
  const initialCartId = user?.["cart_id"];

  const [isFallbackCheckoutLoading, setFallbackCheckoutLoading] =
    useState(false);

  const { setIframe } = useSetCheckoutIframe();

  const handleDelayedUpdate = (val) => {
    setTimeout(() => {
      setFallbackCheckoutLoading(val);
    }, 1000);
  };

  const checkoutCart = async () => {
    if (isFallbackCheckoutLoading) return; // Prevent multiple calls
    try {
      if (initialCartId) {
        setFallbackCheckoutLoading(true);
        const res = await getCheckoutUrl(checkoutURL(initialCartId));

        // when data is conflicting...
        if (res?.response?.status === 409) {
          // mutate cart...
          mutate("/cart");
        }

        if (res?.status === 200) {
          const checkout_url = res?.data?.checkout_url;
          setIframe(checkout_url);
          handleDelayedUpdate(false);
        }
      }
    } catch (e) {
      console.error(e);
      setFallbackCheckoutLoading(false);
    }
  };

  async function handleFallbackCheckout() {
    await checkoutCart();
  }

  return {
    isFallbackCheckoutLoading,
    setFallbackCheckoutLoading,
    handleFallbackCheckout,
  };
}

async function getCheckoutUrl(url) {
  const response = await Api.post(url);
  return response;
}

export default useCheckoutFallback

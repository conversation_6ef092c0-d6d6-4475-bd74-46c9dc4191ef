function useSetCheckoutIframe() {
  const setIframe = async (url) => {
    try {
      const gatsbyElement = document.getElementById('___gatsby')
      const existingIframeElement = gatsbyElement.querySelector('iframe')

      console.log(existingIframeElement, "existingIframeElement")
      console.log("loading checkout url in iframe", url)

      if (existingIframeElement) {
        gatsbyElement.removeChild(existingIframeElement)
      }

      const iframeElement = document.createElement('iframe')
      iframeElement.src = url
      iframeElement.id = 'checkout-iframe'
      iframeElement.style.display = 'none'

      iframeElement.addEventListener('load', () => { })

      gatsbyElement.appendChild(iframeElement)
    } catch (e) {
      console.error(e)
    }
  }

  return { setIframe }
}

export default useSetCheckoutIframe

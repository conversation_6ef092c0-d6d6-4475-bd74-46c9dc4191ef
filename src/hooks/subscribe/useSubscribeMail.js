import { navigate } from 'gatsby'
import Api from '../../services/Api'

export default function useSubscribeMail() {
  const getKey = () => {
    // Below url has type query params that is only for SWR to identify url uniquely.
    // That is not a valid query params for Bigcommerce.
    return `${bcApiURL}?type=subscribe`
  }

  const onCreateSubscriber = async (email) => {
    await subscribeMail(getKey(), {
      url: 'v3/customers/subscribers',
      query_params: {},
      method: 'post',
      body: email,
    })
      .then((res) => {
        if (res?.status === 200 && res?.data?.data?.id) {
          navigate('/success_subscribe', {
            state: {
              status: true,
            },
          })
        } else {
          navigate('/success_subscribe', {
            state: {
              status: false,
            },
          })
        }
      })
      .catch((err) => console.error(err))
  }

  return {
    onCreateSubscriber,
  }
}

async function subscribeMail(url, data) {
  return await Api.post(url, data)
}

import useSWR from 'swr'
import Api from '../../services/Api'
import { getProductReviewURL } from '../../ApiEndpoints'

export default function useGetProductReview(productId) {
  const getKey = () => {
    return productId ? getProductReviewURL(productId) : null
  }

  const { data, error } = useSWR(() => getKey(), getProductReviews)

  const isLoading = !data && !error

  return {
    data,
    error,
    isLoading,
  }
}

async function getProductReviews(url) {
  const response = await Api.get(url)
  return response.data.data
}

import { useState } from "react";
import Api from "../../services/Api";
import { useSelector } from "react-redux";
import useToastify from "../ui/useToastify";
import { productReviewURL } from "../../ApiEndpoints";

export default function useAddProductReview() {
  const { currentLoginUser } = useSelector((state) => state.customer);
  const [isLoading, setLoading] = useState(false);
  const { toastMessage } = useToastify();

  const addProductReview = async (data, id, mutate) => {
    try {
      setLoading(true);
      const res = await addReview(formateReveiw(data, id));
      setLoading(false);
      if (res?.data) {
        toastMessage("success", "Review submitted sucessfully.");
      } else {
        toastMessage("error", "Review not submitted sucessfully.");
      }
      mutate();
      return res;
    } catch (e) {
      console.error(e);
    }
  };

  return {
    addProductReview,
    isLoading,
  };
}

async function addReview(data) {
  const response = await Api.post(productReviewURL, data);
  return response.data.data;
}

function formateReveiw(data, id, user) {
  return {
    date_reviewed: new Date(),
    product_id: id,
    rating: data?.["rating"],
    name: data?.["name"],
    title: data?.["reviewSubject"],
    text: data?.["comments"],
    email: data?.["email"],
  };
}

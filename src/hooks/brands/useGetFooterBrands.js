import { useStaticQuery, graphql } from "gatsby";

export default function useGetFooterBrands() {
  const data = useStaticQuery(graphql`
    query GetAllBrands {
      allBigCommerceBrands(limit: 5) {
        edges {
          node {
            id
            name
            custom_url {
              url
            }
            image_url
          }
        }
      }
    }
  `);

  const allBrands = data.allBigCommerceBrands.edges.map((item) => {
    const {
      id,
      custom_url: { url },
      name,
      image_url: imageUrl,
    } = item.node;

    return {
      id,
      url,
      name,
      imageUrl,
    };
  });

  return {
    brands: allBrands || [],
  };
}

import { useEffect, useRef } from 'react'
import { useSelector } from 'react-redux'
import Api from '../services/Api'
import { currentCustomerURL } from '../ApiEndpoints'
import { authCleanup, getTokenFromStorage } from '../utils/cleanup'
import { navigate } from 'gatsby'

// Standalone authentication polling hook
export default function useAuthPolling() {
  const { user } = useSelector((state) => state.auth)
  const intervalRef = useRef(null)
  const storageCheckRef = useRef(null)
  const lastTokenRef = useRef(null)
  const isInitializedRef = useRef(false)
  const lastAuthActionRef = useRef(null)

  // Function to perform logout (for cross-tab sync only)
  const performLogout = () => {
    console.log('🚪 Performing cross-tab logout...')
    authCleanup({
      triggerCrossTab: false, // Don't trigger again to avoid loops
      redirectToLogin: true,
      clearSessionData: true
    })
  }

  // Function to check authentication
  const checkAuth = async () => {
    try {
      const tokenFromStorage = getTokenFromStorage()
      const tokenFromRedux = user?.accessToken
      const currentToken = tokenFromRedux || tokenFromStorage

      if (!currentToken) {
        return null
      }

      const response = await Api.get(currentCustomerURL)
      return response.data
    } catch (error) {
      // Let the API interceptor handle 401 errors
      // We just return null here to avoid conflicts
      return null
    }
  }

  // Start polling
  const startPolling = () => {
    if (intervalRef.current) return // Already polling

    // Poll every 5 seconds
    intervalRef.current = setInterval(async () => {
      await checkAuth()
    }, 5000)

    // Check for cross-tab token changes every 2 seconds
    storageCheckRef.current = setInterval(() => {
      const currentStorageToken = getTokenFromStorage()
      const lastToken = lastTokenRef.current

      if (currentStorageToken !== lastToken) {
        lastTokenRef.current = currentStorageToken

        // If token was removed (logout), trigger logout
        if (lastToken && !currentStorageToken) {
          console.log('🔄 Token removed detected, triggering logout')
          performLogout()
        }
        // If we got a new token and don't have one in Redux, trigger auth check
        else if (currentStorageToken && !user?.accessToken) {
          checkAuth()
        }
      }
    }, 2000)
  }

  // Stop polling
  const stopPolling = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
      intervalRef.current = null
    }

    if (storageCheckRef.current) {
      clearInterval(storageCheckRef.current)
      storageCheckRef.current = null
    }
  }

  // Listen for cross-tab authentication actions
  useEffect(() => {
    const handleStorageChange = (e) => {
      if (e.key === 'auth_action') {
        try {
          const authAction = JSON.parse(e.newValue || '{}')

          // Prevent processing the same action multiple times
          if (lastAuthActionRef.current === authAction.timestamp) return
          lastAuthActionRef.current = authAction.timestamp

          if (authAction.action === 'LOGOUT') {
            console.log('🔄 Cross-tab logout detected')
            performLogout()
          }
        } catch (error) {
          console.error('Error parsing auth action:', error)
        }
      }
    }

    window.addEventListener('storage', handleStorageChange)
    return () => {
      window.removeEventListener('storage', handleStorageChange)
    }
  }, [])

  // Initialize polling based on token presence
  useEffect(() => {
    const tokenFromStorage = getTokenFromStorage()
    const tokenFromRedux = user?.accessToken
    const hasToken = tokenFromRedux || tokenFromStorage

    // Initialize last token reference
    if (!isInitializedRef.current) {
      lastTokenRef.current = tokenFromStorage
      isInitializedRef.current = true
    }

    if (hasToken) {
      startPolling()
    } else {
      stopPolling()
    }

    return () => {
      stopPolling()
    }
  }, [user?.accessToken]) // Only depend on user.accessToken

  // Simple function to check if user is authenticated and redirect to home
  const redirectIfAuthenticated = async () => {
    try {
      const tokenFromStorage = getTokenFromStorage()
      const tokenFromRedux = user?.accessToken
      const currentToken = tokenFromRedux || tokenFromStorage

      if (currentToken) {
        // Validate token with API
        const response = await Api.get(currentCustomerURL)
        if (response.data?.id) {
          // Token is valid, redirect to home
          navigate('/')
          return true
        }
      }
      return false
    } catch (error) {
      // Token is invalid, stay on current page
      return false
    }
  }

  return {
    checkAuth,
    redirectIfAuthenticated
  }
}

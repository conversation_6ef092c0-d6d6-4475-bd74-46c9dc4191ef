import { useState } from 'react'
import Api from '../../services/Api'
import useToastify from '../ui/useToastify'
import { instockNotifyURL } from '../../ApiEndpoints'

export const useNotify = () => {
  const [, setLoading] = useState(false)
  const { toastMessage } = useToastify()

  const callInStockNotify = async (data) => {
    setLoading(true)
    const res = await setNotify(data)
    setLoading(false)

    if (res === 'success') {
      toastMessage(
        'success',
        `Thank you! we will email you when this item is back in stock.`
      )
    } else {
      toastMessage('error', res)
    }
  }

  return {
    callInStockNotify,
  }
}

const setNotify = async (data) => {
  // For bigcommerce Instock notify Plugin API
  const response = await Api.post(instockNotifyURL, data)
  
  // For admin Instock notify API
  const response2 = await Api.post(`/products/instock-notify`, {
    notify_email: data.email,
    product_id: data.productId,
    variant_id: data.variantId,
  }
)
  return response?.data || response2?.data
}

export default useNotify

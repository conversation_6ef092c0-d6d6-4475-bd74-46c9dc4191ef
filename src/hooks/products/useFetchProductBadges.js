import useSWR from 'swr'
import Api from '../../services/Api'
import { isUserLoggedIn } from '../../utils/auth'
import { productBadgesURL } from '../../ApiEndpoints'

export const useFetchProductBadges = (productIds) => {
  const isUser = isUserLoggedIn()

  const getKey = () => {
    // Below url has type query params that is only for SWR to identify url uniquely.
    // That is not a valid query params for Bigcommerce.
    return productIds?.length && isUser
      ? `${productBadgesURL}?product_ids=${productIds.join(',')}`
      : null
  }

  const { data, error, mutate } = useSWR(() => getKey(), getProducts)

  const isLoading = !data && !error
  const badges = data?.data || []

  async function getProducts(url) {
    const response = await Api.get(url)
    return response.data
  }

  return {
    badges,
    isLoading,
    mutate,
  }
}

export default useFetchProductBadges

import Api from "../../services/Api";
import { updateRecentlyViewedProducts } from "../../utils/product";

export const useManageRecentlyViewedProductIds = () => {
  const manageProductIds = async (productId, currentLoginUser) => {
    // Step 1: Update the product ID in local storage
    updateRecentlyViewedProducts(productId, 25, currentLoginUser);

    // Step 2: Get the updated list of product IDs from local storage
    let recentlyViewed = localStorage.getItem("recentlyViewProductIds");
    let recentlyViewedBeforeLogin = localStorage.getItem(
      "recentlyViewProductIdsBeforeLogin"
    );

    // If `recentlyViewed` is `null` or `undefined`, initialize it as an empty array
    if (!recentlyViewed) {
      recentlyViewed = [];
    } else {
      recentlyViewed = JSON.parse(recentlyViewed);
    }

    if (!recentlyViewedBeforeLogin) {
      recentlyViewedBeforeLogin = [];
    } else {
      recentlyViewedBeforeLogin = JSON.parse(recentlyViewedBeforeLogin);
    }

    // Step 3: POST the product IDs to the backend if the user is logged in
    if (currentLoginUser?.id && currentLoginUser !== false) {
      const url = "/recently-viewed/ids";

      if (recentlyViewedBeforeLogin?.length > 0) {
        await Api.post(url, { product_ids: recentlyViewedBeforeLogin });
        localStorage.setItem("recentlyViewProductIdsBeforeLogin", "[]");
      } else {
        // Send only the current product ID afterward
        if (productId && typeof productId !== "object") {
          await Api.post(url, { product_ids: [productId] });
        }
      }

      // Step 4: Fetch the updated list from the backend
      const response = await Api.get(url);
      const updatedProductIds = response?.data?.data || [];

      // Step 5: Update the local storage with the latest IDs
      localStorage.setItem(
        "recentlyViewProductIds",
        JSON.stringify(updatedProductIds)
      );

      return updatedProductIds;
    }

    // Return the updated list from local storage if the user is not logged in
    return recentlyViewed;
  };

  return manageProductIds;
};

export default useManageRecentlyViewedProductIds;

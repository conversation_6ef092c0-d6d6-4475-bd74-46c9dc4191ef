import React, { useEffect, useState } from 'react'
import useSWR from 'swr'
import Api from '../../services/Api'
import { isUserLoggedIn } from '../../utils/auth'
import Papa from 'papaparse'
import useCartOperations from '../cart/useCartOperations'
import { cartFormToData } from '../../utils/cart'
import { cartFormItemsURL } from '../../ApiEndpoints'
import { ceil } from '../../utils/functions'

export const useGetCartFormProduct = () => {
  const allowedExtensions = ['csv']
  const user = isUserLoggedIn()
  const { addToCart, clearCart } = useCartOperations()
  const [state, setState] = useState({
    isLoading: false,
    lineItems: [],
    itemError: false,
    file: '',
    fileError: '',
    skus: [],
  })
  const [fskus, setSKUs] = useState()

  const getKey = () => {
    return state['skus'].length && user ? `${cartFormItemsURL}` : null
  }

  const { data, error, mutate } = useSWR(() => getKey(), getProductBySku)

  useEffect(() => {
    getProductBySku(getKey())
  }, [state.skus])

  useEffect(() => {
    // get data from localstorage.
    const res = window.localStorage.getItem('cartFormItems')
    const cartFormItems = JSON.parse(res)

    if (cartFormItems && cartFormItems.length && data) {
      let flag = true

      cartFormItems.forEach((i) => {
        // when sku matched.
        if (data['sku'] === i['sku']) {
          flag = false
        }
      })

      if (flag) {
        // set data if no data found in localstorage.
        data.forEach((product) => {
          cartFormItems.push(product)
        })

        // set data if no data found in localstorage.
        window.localStorage.setItem(
          'cartFormItems',
          JSON.stringify(cartFormItems)
        )
      }
    } else {
      if (data) {
        // set data if no data found in localstorage.
        data.forEach((product) => {
          cartFormItems.push(product)
        })
        window.localStorage.setItem(
          'cartFormItems',
          JSON.stringify(cartFormItems)
        )
      }
    }
  }, [data])

  const isLoading = !data && !error

  async function getProductBySku(url) {
    const response = await Api.post(url, state['skus'])

    return response?.data
  }

  const calculateItemTotal = (updated_qnt) => {
    setTimeout(() => {
      let res = JSON.parse(window.localStorage.getItem('cartFormItems'))
      console.log('res', res)
    }, 1000)
  }

  const handleQuantityChange = async (value, sku) => {
    const res = JSON.parse(window.localStorage.getItem('cartFormItems'))
    res.forEach((i) => {
      if (i['sku'] === sku) {
        i['quantity'] = i['quantity'] + value
        i['totalPrice'] = i['quantity'] * i['price']
      }
    })
    setState((prevState) => ({ ...prevState, lineItems: res }))
    // localStorage.setItem("cartFormItems", JSON.stringify(res));
  }

  const removeItem = async (value) => {
    const res = JSON.parse(window.localStorage.getItem('cartFormItems'))
    const requiredIndex = res.findIndex((el) => {
      return el['id'] === value['id']
    })
    res.splice(requiredIndex, 1)
    window.localStorage.removeItem('cartFormItems')
    window.localStorage.setItem('cartFormItems', JSON.stringify(res))
    window.location.reload()
  }

  const handleFileChange = (e) => {
    setState((prevState) => ({ ...prevState, fileError: '' }))
    // Check if user has entered the file
    if (e.target.files.length) {
      const inputFile = e.target.files[0]

      const fileExtension = inputFile?.type.split('/')[1]
      if (!allowedExtensions.includes(fileExtension)) {
        // setError("Please input a csv file");
        setState((prevState) => ({
          ...prevState,
          fileError: 'Please input a csv file',
        }))
        return
      }

      setState((prevState) => ({ ...prevState, file: inputFile }))
    }
  }

  const handleParse = () => {
    if (!state['file'])
      return setState((prevState) => ({
        ...prevState,
        fileError: 'Enter a valid file',
      }))

    const reader = new FileReader()
    console.log('file import start')
    // Event listener on reader when the file
    // loads, we parse it and set the data.
    reader.onload = async ({ target }) => {
      const csv = Papa.parse(target.result, {
        header: true,
        skipEmptyLines: true,
        complete: function (results) {
          let tempSku = []
          results.data.map((d) => {
            if (Object.values(d)[0] !== '') {
              tempSku.push({
                product_id: parseInt(Object.values(d)[2]),
                variant_id: parseInt(Object.values(d)[3]),
                sku: Object.values(d)[0],
                quantity: parseInt(Object.values(d)[1]),
              })
            }
          })

          if (tempSku.length > 49) {
            const t1 = ceil(tempSku.length / 49)
            for (let i = 1; i <= t1; i++) {
              let chunkTempSku = tempSku.slice((i - 1) * 49, i * 49)
              console.log(chunkTempSku)
              const timeOutId = setTimeout(
                () =>
                  setState((prevState) => ({
                    ...prevState,
                    skus: chunkTempSku,
                  })),
                1000
              )
            }
            //return () => clearTimeout(timeOutId);
          } else {
            const timeOutId = setTimeout(
              () => setState((prevState) => ({ ...prevState, skus: tempSku })),
              1000
            )
            return () => clearTimeout(timeOutId)
          }
        },
      })
    }
    reader.readAsText(state['file'])
  }

  const onTriggerAddToCart = async () => {
    let cartArray = []
    let errorString = ''
    let res = JSON.parse(window.localStorage.getItem('cartFormItems'))
    res.map((item) => {
      if (item['inventory_level'] > 0) {
        if (
          (item['inventory_level'] == 0 && item['quantity'] > 0) ||
          item['inventory_level'] < item['quantity']
        ) {
          let flag = 1
          errorString = errorString + ',' + item['sku']
        }
        if (item['inventory_level'] < item['quantity']) {
          item['quantity'] = item['inventory_level']
        }
        cartArray.push({
          product_id: parseInt(item['product_id']),
          variant_id: parseInt(item['variant_id']),
          sku: item['sku'],
          quantity: parseInt(item['quantity']),
        })
      } else {
        errorString = errorString + ',' + item['sku']
      }
    })
  }

  return {
    error: state.error,
    lineItems: cartFormToData(state['lineItems']),
    isLoading,
    mutate,
    removeItem,
    handleParse,
    handleFileChange,
    onTriggerAddToCart,
    handleQuantityChange,
  }
}

export default useGetCartFormProduct

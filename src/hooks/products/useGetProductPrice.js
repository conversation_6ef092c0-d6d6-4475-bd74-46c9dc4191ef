import useSWR from "swr";
import Api from "../../services/Api";
import { useSelector } from "react-redux";
import { getProductPriceURL } from "../../ApiEndpoints";

export default function useGetProductPrice(productId) {
  const { user } = useSelector((state) => state.auth);

  const getKey = () => {
    const productPriceUrl = getProductPriceURL(user?.customer_id, productId);
    return user ? productPriceUrl : null;
  };

  const { data, isValidating } = useSWR(() => getKey(), getProductPrice);

  return {
    data,
    isValidating,
  };
}

async function getProductPrice(url) {
  const response = await Api.get(url);
  return response.data;
}

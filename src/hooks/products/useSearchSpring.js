import { useState, useEffect } from 'react'
import Api from '../../services/Api'
import {
  searchSpringAutoCompleteURL,
  searchSpringSuggestURL,
} from '../../ApiEndpoints'

function useSearchSpring() {
  const defaultValues = {
    suggestions: [],
    autocompletes: [],
  }

  const [isLoading, setLoading] = useState(false)
  const [data, setData] = useState(defaultValues)
  const [search, onSearch] = useState('')

  // Helper function to construct the autocomplete API endpoint
  const autocompleteKey = () => {
    return search ? `${searchSpringAutoCompleteURL}` : null
  }

  // Helper function to construct the suggestion API endpoint
  const suggetionKey = () => {
    return search ? `${searchSpringSuggestURL}` : null
  }

  // Use effect hook to fetch data from the API when search term changes
  useEffect(async () => {
    if (search !== '') {
      setLoading(true)
      const suggestions = await getsuggestions(suggetionKey())
      const autocompletes = await getAutocomplete(autocompleteKey())
      setLoading(false)

      if (suggestions?.['alternatives'] || autocompletes?.['results']) {
        setData(() => ({
          suggestions: suggestions?.['alternatives'],
          autocompletes: autocompletes?.['results'],
        }))
      }
    }
    if (search === '') {
      setData((prevState) => defaultValues)
    }
  }, [search])

  // Helper function to get suggestion data from the API
  async function getsuggestions(url) {
    const params = {
      language: 'en',
      suggestionCount: 5,
      q: search,
    }

    const response = await Api.post(url, params)
    return response.data.data
  }

  // Helper function to get autocomplete data from the API
  async function getAutocomplete(url) {
    const params = {
      redirectResponse: 'minimal',
      resultsPerPage: 5,
      q: search,
    }

    const response = await Api.post(url, params)
    return response.data.data
  }

  // Reset data to default values
  const resetData = () => {
    setData(() => defaultValues)
  }

  return {
    isLoading: isLoading,
    suggestions: data.suggestions,
    autocompletes: data.autocompletes,
    onSearch: onSearch,
    resetData: resetData,
  }
}

export default useSearchSpring

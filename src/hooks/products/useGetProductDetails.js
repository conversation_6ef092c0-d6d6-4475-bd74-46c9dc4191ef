import { navigate } from 'gatsby'
import useSWR from 'swr'
import Api from '../../services/Api'
import { isEmpty } from '../../utils/functions'

export const useGetProductDetails = (product_id, is_quick_view, user) => {
  const getKey = () => {
    return product_id
      ? `/bc/api/product?product_id=${product_id}` +
      `${is_quick_view ? `&is_quick_view=${is_quick_view}` : ''}` + `${user ? `&is_loggedin=true` : ''}`
      : null
  }

  const { data, error } = useSWR(() => getKey(), getProducts)

  const isLoading = !data && !error

  if (!isEmpty(data)) {
    data['bigcommerce_id'] = data.id
  }

  async function getProducts(url) {
    const response = await Api.get(url)
    return response?.data?.data
  }

  // condition for non-visible product...
  if (!isLoading && isEmpty(data) && data !== undefined) {
    navigate('/404', {
      state: {
        message: 'product not visible',
      },
    })
  }

  return {
    productDetails: data || {},
    isLoading,
  }
}

export default useGetProductDetails

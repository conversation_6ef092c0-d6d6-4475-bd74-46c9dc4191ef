import useSWR from 'swr'
import Api from '../../services/Api'
import { productListingURL } from '../../ApiEndpoints'
import { isUserLoggedIn } from "../../utils/auth";

function useGetProductListing(listingType) {
  const user = isUserLoggedIn();

  let recentlyViewedBeforeLogin = localStorage.getItem(
    "recentlyViewProductIdsBeforeLogin"
  );

  if (!recentlyViewedBeforeLogin) {
    recentlyViewedBeforeLogin = [];
  } else {
    recentlyViewedBeforeLogin = JSON.parse(recentlyViewedBeforeLogin);
  }

  const getKey = () => {
    if (
      listingType === null &&
      !user &&
      recentlyViewedBeforeLogin?.length > 0
    ) {
      return `/recently-viewed/products?product_ids=${recentlyViewedBeforeLogin}`;
    } else if (listingType !== null) {
      return `${productListingURL}?type=${listingType}${
        user ? `&is_loggedin=true` : ""
      }`;
    } else if (user) {
      return `/recently-viewed/user/products`;
    }
  };

  const { data, error } = useSWR(() => getKey(), getProductListing);
  
  const isLoading = !data && !error;
  const products =
    (data && listingType !== null
      ? data?.[listingType]?.map((item) => {
          const {
            id,
            image,
            price,
            name,
            retail_price,
            sale_price,
            custom_url,
            brand_name,
            badges,
          } = item;
          return {
            id,
            bigcommerce_id: id,
            image,
            price: price,
            name,
            retail_price: retail_price,
            sale_price: sale_price,
            custom_url,
            brand_name,
            badges,
          };
        })
      : data &&
        data?.reverse().map((item) => {
          const {
            id,
            image,
            price,
            name,
            retail_price,
            sale_price,
            custom_url,
            brand_name,
            badges,
          } = item;
          return {
            id,
            bigcommerce_id: id,
            image,
            price: price,
            name,
            retail_price: retail_price,
            sale_price: sale_price,
            custom_url,
            brand_name,
            badges,
          };
        })) || [];

  return {
    products,
    isLoading,
  }
}


async function getProductListing(url) {
  const response = await Api.get(url)
  return response.data.data
}

export default useGetProductListing

import useSWR from 'swr'
import Api from '../../services/Api'
import { createQueryParam } from '../../utils/components'
import { isEmpty } from '../../utils/functions'

export const useGetProductDetailsByCustomUrl = (product_id) => {
  const getKey = () => {
    return product_id
      ? `${
          process.env.NETLIFY_API_BASE_URL
        }?endpoint=catalog/products?id:in=${product_id}${createQueryParam(
          'include',
          'images,variants,custom_fields,options,modifiers,videos,bulk_pricing_rules'
        )}`
      : null
  }

  const { data, error } = useSWR(() => getKey(), getProductDetailsWithCustomUrl)

  const isLoading = !data && !error
  const product_details = data ? data?.data?.[0] : {}

  if (!isEmpty(data)) {
    product_details['bigcommerce_id'] = product_details.id
  }

  return {
    product_details,
    isLoading,
  }
}

async function getProductDetailsWithCustomUrl(url) {
  const response = await Api.get(url, {
    // db: {
    //   name: "tenent1",
    // },
  })
  return response.data
}

export default useGetProductDetailsByCustomUrl

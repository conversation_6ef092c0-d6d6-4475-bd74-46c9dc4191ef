import useSWR from 'swr'
import Api from '../../services/Api'

export const useGetProductOptions = (product_id, user) => {
  const getKey = () => {
    return product_id && user
      ? `/products/${product_id}/variants`
      : null
  }

  const { data, error } = useSWR(() => getKey(), getProducts)

  const isLoading = !data && !error
  const product_variants = data ? data : {}

  async function getProducts(url) {
    const response = await Api.get(url)
    return response?.data?.data
  }

  return {
    product_variants,
    isLoading,
  }
}

export default useGetProductOptions

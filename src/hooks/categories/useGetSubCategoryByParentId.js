const useGetSubCategoryByParentId = () => {

const fetchSubCategories = async (categoryId) => {
  const response = await fetch(
    `${process.env.GATSBY_ADMIN_API_URL}/products/categories/child/${categoryId}`,
    {
      method: "GET",
      headers: {
        origin: `${process.env.GATSBY_ADMIN_ORIGIN}`,
        "X-AUTH-TOKEN": `${process.env.GATSBY_X_AUTH_TOKEN}`,
        "x-store-id": `${process.env.GATSBY_API_STORE_ID}`,
        "X-CLIENT-ID": `${process.env.GATSBY_X_CLIENT_ID}`,
      },
    }
  );
  return response.json();
};

  return { fetchSubCategories };
};

export default useGetSubCategoryByParentId;

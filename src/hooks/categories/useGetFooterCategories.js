import { useStaticQuery, graphql } from 'gatsby'

export default function useGetFooterCategories() {
  const data = useStaticQuery(graphql`
    query GetAllCategories {
      allDynamicCategories(limit: 5, sort: { fields: is_visible }) {
        edges {
          node {
            id
            name           
            url            
          }
        }
      }
    }
  `)

  const allCategories = data.allDynamicCategories.edges.map((item) => {
    const {
      id,
      url:  url,
      name,
    } = item.node

    return {
      id,
      url: url || '',
      name,
    }
  })

  return {
    categories: allCategories || [],
  }
}

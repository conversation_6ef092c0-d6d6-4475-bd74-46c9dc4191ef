import useSWR from "swr";
import Api from "../../services/Api";
import { getorderInvoiceVisibilityUrl } from "../../ApiEndpoints";
import { useSelector } from "react-redux";

function useGetCheckInvoiceVisibility() {
  const { user } = useSelector((state) => state.auth);

  const getKey = () => {
    return getorderInvoiceVisibilityUrl(user?.customer_id);
  };

  const { data, error } = useSWR(() => getKey(), getInvoiceVisibilityUrl);
  const isChecking = (!data && !error) || false;

  return {
    invoice_visible: data?.invoice_visible,
    isChecking,
  };
}

async function getInvoiceVisibilityUrl(url) {
  const response = await Api.get(url);
  return response.data;
}

export default useGetCheckInvoiceVisibility;

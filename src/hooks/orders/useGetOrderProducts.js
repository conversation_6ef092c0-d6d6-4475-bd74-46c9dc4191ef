import useSWR from 'swr'
import Api from '../../services/Api'
import { getorderProductsUrl } from '../../ApiEndpoints'

function useGetOrderProducts(order_id){
    const getKey = () => {
        // Below url has type query params that is only for SWR to identify url uniquely.
        // That is not a valid query params for Bigcommerce.
        return getorderProductsUrl(order_id)
    }

    const { data, error } = useSWR(() => getKey(), getOrdersProducts)
    const isLoading = (!data && !error) || false

    return {
        order_products:data,
        isLoading
    };
}

async function getOrdersProducts(url) {
    const response = await Api.get(url)
    return response.data
}

export default useGetOrderProducts
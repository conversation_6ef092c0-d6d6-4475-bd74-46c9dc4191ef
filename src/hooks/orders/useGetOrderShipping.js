import useSWR from 'swr'
import Api from '../../services/Api'
import { getorderShippingUrl } from '../../ApiEndpoints'

function useGetOrderShipping(order_id){
    const getKey = () => {
        // Below url has type query params that is only for SWR to identify url uniquely.
        // That is not a valid query params for Bigcommerce.
        return getorderShippingUrl(order_id)
    }

    const { data, error } = useSWR(() => getKey(), getOrdersShipping)
    const isLoading = (!data && !error) || false
    
    return {
        order_shpping:data,
        isLoading
    };
}

async function getOrdersShipping(url) {
    const response = await Api.get(url)
    return response.data
}

export default useGetOrderShipping
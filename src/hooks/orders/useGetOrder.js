import React from "react"
import useSWR from 'swr'
import {orders} from '../../ApiEndpoints'
import Api from "../../services/Api"
import { getCustomerId } from '../../utils/auth'

function useGetOrder(order_id){
    const getKey = () => {
        // Below url has type query params that is only for SWR to identify url uniquely.
        // That is not a valid query params for Bigcommerce.
        return `${orders}/${order_id}`
    }

    const { data, error } = useSWR(() => getKey(), getOrder)
    const isLoading = (!data && !error) || false
    console.log(data)
    return {
        order:data,
        isLoading
    };
}

async function getOrder(url) {
    const response = await Api.get(url)
    return response.data
}

export default useGetOrder
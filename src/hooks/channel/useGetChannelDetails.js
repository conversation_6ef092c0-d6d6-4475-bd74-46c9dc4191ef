import useSWR from 'swr'
import Api from '../../services/Api'

function useGetChannelDetails() {
  const getKey = () => {
    return `${process.env.NETLIFY_API_BASE_URL}?endpoint=channels`
  }

  const { data, error } = useSWR(() => getKey(), getAllChannels)

  const isLoading = !data && !error

  const channel = data?.filter((item) => item.status === 'active') || ''

  const channelId = channel?.id || ''

  return {
    data,
    isLoading,
    channel,
    channelId,
  }
}

async function getAllChannels(url) {
  const response = await Api.get(url)
  return response.data.data
}

export default useGetChannelDetails

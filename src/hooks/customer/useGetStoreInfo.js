import { useEffect, useState } from 'react'

function useGetStoreInfo() {
  const [storeInfo, setStoreInfo] = useState({
    store_address: '-',
    store_phone: '-',
    store_email: '-',
    store_name: '-',
  })

  useEffect(() => {
    fetch(
      `${process.env.NETLIFY_API_BASE_URL}?endpoint=settings/store/profile`,
      {
        method: 'GET',
        credentials: 'same-origin',
        mode: 'same-origin',
      }
    )
      .then(async (res) => ({ response: await res.json(), status: res.status }))
      .then((response) => setStoreInfo(response.response.data))
      .catch((error) => console.error(error))
  }, [])

  const { store_address, store_phone, store_email, store_name } = storeInfo

  return {
    store_address,
    store_phone,
    store_email,
    store_name,
  }
}

export default useGetStoreInfo

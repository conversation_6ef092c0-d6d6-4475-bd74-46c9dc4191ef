import { useState, useEffect } from 'react'
import Api from '../../services/Api'
import { createQueryParam } from '../../utils/components'
import useSWR from 'swr'

function useGetOrders({ page, limit, hasMoreData }) {
  const [customer, setCustomer] = useState()

  useEffect(() => {
    setCustomer(JSON.parse(sessionStorage.getItem('customerId')))
  }, [])

  const getKey = (page, limit) => {
    const customer_id = customer && customer.response.customer_id
    return customer_id
      ? `${
          process.env.NETLIFY_API_BASE_URL
        }?endpoint=orders?customer_id=${customer_id}${createQueryParam(
          'limit',
          limit
        )}${createQueryParam('page', page)}`
      : null
  }

  const { data, error } = useSWR(() => getKey(page, limit), getOrderListing)

  const isLoading = !data && !error
  const orders = (data && data.length > 0 && data) || []

  hasMoreData(data, data?.length)

  return {
    orders,
    isLoading,
  }
}

export default useGetOrders

async function getOrderListing(url) {
  const response = await Api.get(url)
  if (response.status === 204 && response.statusText) {
    return false
  }
  return response.data
}

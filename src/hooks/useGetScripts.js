import { useState, useEffect } from "react";

const useGetScripts = () => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Function to parse and inject script content
  const injectScriptContent = (
    content,
    scriptId,
    scriptName,
    targetElement,
    loadMethod
  ) => {
    // Create a temporary div to parse the HTML content
    const tempDiv = document.createElement("div");
    tempDiv.innerHTML = content;

    // Extract and create script elements
    const scriptElements = tempDiv.querySelectorAll("script");
    scriptElements.forEach((scriptTag) => {
      const newScript = document.createElement("script");

      // Copy all attributes from the original script tag
      Array.from(scriptTag.attributes).forEach((attr) => {
        newScript.setAttribute(attr.name, attr.value);
      });
      
      // Copy the script content
      newScript.textContent = scriptTag.textContent;

      // Add data attributes for identification
      newScript.setAttribute("data-script-id", scriptId);
      newScript.setAttribute("data-script-name", scriptName);

      // Apply async/defer based on load_method
      if (loadMethod === "async") {
        newScript.setAttribute("async", "");
      } else if (loadMethod === "defer") {
        newScript.setAttribute("defer", "");
      }

      // Append the script to the target element
      targetElement.appendChild(newScript);
    });

    // Extract and create style elements
    const styleElements = tempDiv.querySelectorAll("style");
    styleElements.forEach((styleTag) => {
      const newStyle = document.createElement("style");

      // Copy all attributes from the original style tag
      Array.from(styleTag.attributes).forEach((attr) => {
        newStyle.setAttribute(attr.name, attr.value);
      });

      // Copy the style content
      newStyle.textContent = styleTag.textContent;

      // Add data attributes for identification
      newStyle.setAttribute("data-script-id", scriptId);
      newStyle.setAttribute("data-script-name", scriptName);

      // Append the style to the target element
      targetElement.appendChild(newStyle);
    });

    // Handle any other elements (like link, meta, etc.)
    const otherElements = tempDiv.querySelectorAll("*:not(script):not(style)");
    otherElements.forEach((element) => {
      const newElement = document.createElement(element.tagName.toLowerCase());

      // Copy all attributes
      Array.from(element.attributes).forEach((attr) => {
        newElement.setAttribute(attr.name, attr.value);
      });

      // Copy the content
      newElement.innerHTML = element.innerHTML;

      // Add data attributes for identification
      newElement.setAttribute("data-script-id", scriptId);
      newElement.setAttribute("data-script-name", scriptName);

      // Append to the target element
      targetElement.appendChild(newElement);
    });
  };

  // Function to inject all scripts
  const injectScripts = (scriptsData) => {
    // Inject header scripts
    if (scriptsData.header && scriptsData.header.length > 0) {
      scriptsData.header.forEach((script) => {
        if (script.status === "active" && script.content) {
          injectScriptContent(
            script.content,
            script.id,
            script.name,
            document.head,
            script.load_method
          );
        }
      });
    }

    // Inject footer scripts
    if (scriptsData.footer && scriptsData.footer.length > 0) {
      scriptsData.footer.forEach((script) => {
        if (script.status === "active" && script.content) {
          injectScriptContent(
            script.content,
            script.id,
            script.name,
            document.body,
            script.load_method
          );
        }
      });
    }
  };

  // Function to cleanup injected scripts
  const cleanupScripts = () => {
    const injectedElements = document.querySelectorAll("[data-script-id]");
    injectedElements.forEach((element) => {
      element.remove();
    });
  };

  useEffect(() => {
    const fetchScripts = async () => {
      setLoading(true);
      setError(null);
      try {
        const response = await fetch(
          `${process.env.GATSBY_STOREFRONT_API_URL}/script/manager/scripts/storefront`,
          {
            method: "GET",
            headers: {
              origin: `${process.env.GATSBY_ADMIN_ORIGIN}`,
              "x-store-id": `${process.env.GATSBY_API_STORE_ID}`,
            },
          }
        );
        if (!response.ok) {
          throw new Error(`Error ${response.status}: ${response.statusText}`);
        }
        const json = await response.json();
        setData(json?.data);

        // Inject scripts after successful API response
        if (json?.data) {
          injectScripts(json.data);
        }
      } catch (err) {
        setError(err.message || "Something went wrong");
        setData(null);
      } finally {
        setLoading(false);
      }
    };

    fetchScripts();

    // Cleanup function to remove scripts when component unmounts
    return () => {
      cleanupScripts();
    };
  }, []);

  return {
    data,
    loading,
    error,
    cleanupScripts, // Exported for manual use if needed
  };
};

export default useGetScripts;

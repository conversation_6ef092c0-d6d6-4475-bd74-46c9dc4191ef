import Api from "../services/Api";

function useSendIpInfoApi() {
  const fetchPingPostApi = async (userId) => {
    try {
      // 1. Get Public IP from ipify
      const ipResponse = await fetch("https://api64.ipify.org/?format=json");
      const ipData = await ipResponse.json();

      if (!ipData?.ip) {
        throw new Error("Unable to retrieve IP address");
      }

      // 2. Send the IP to your backend
      await Api.post(`/web-visitors/${userId}`, {
        ip_address: ipData.ip,
      });

    } catch (error) {
      console.error("Failed to send IP info:", error);
    }
  };

  return { fetchPingPostApi };
}

export default useSendIpInfoApi;

import { useEffect, useState } from "react";
import Api from "../services/Api";

function usePingApi(user) {
  const [data, setData] = useState(null);

  const fetchPingPostApi = async () => {
    try {
      const response = await Api.get("/ping");
      if (response.status === 204 && response.statusText) {
        setData(false);
      } else {
        setData(response.data);
      }
    } catch (error) {
      console.error("Failed to fetch data:", error);
    }
  };

  useEffect(() => {
    if (user) {
      const interval = setInterval(() => {
        fetchPingPostApi();
      }, 180000);
      return () => clearInterval(interval);
    }
  }, [user]);

  return { data };
}

export default usePingApi;

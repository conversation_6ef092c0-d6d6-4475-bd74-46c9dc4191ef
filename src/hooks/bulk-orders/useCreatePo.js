
import Api from "../../services/Api";
import { createPo } from "../../ApiEndpoints";
import useToastify from "../ui/useToastify";
import { navigate } from "gatsby";
import { useState } from "react";
import { removePOFromSessionStorage } from "../../utils/purchaseOrder";

function useCreatePo(handleCancel) {
  const [loading, setLoading] = useState(false);
  const { toastMessage } = useToastify();
  const createOrders = async (data) => {
    setLoading(true);
    const res = await purchaseOrders(createPo, data);
    if (res?.status === 200) {
      toastMessage("success", "PO Created Successfully.");
      removePOFromSessionStorage()
      setLoading(false);
      handleCancel();
      navigate("/purchase-orders/pending");
    } else {
      handleCancel();
      toastMessage("error", "PO Is Not Created, Please try again!");
      setLoading(false);
    }
  };

  return {
    createOrders,
    loading,
  };
}

async function purchaseOrders(url, data) {
  const response = await Api.post(url, data);
  return response || [];
}
export default useCreatePo;

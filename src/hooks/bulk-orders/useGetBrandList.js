import useSWR from "swr";
import Api from "../../services/Api";
import { brandList } from "../../ApiEndpoints";

const getKey = () => {
  return `${brandList}`;
};

function useGetBrandList() {
  const { data, error } = useSWR(() => getKey(), getBrandListing);
  const isLoading = (!data && !error) || false;

  return {
    brandList: data,
    isLoading,
    isNextPage: data?.next_page,
  };
}

async function getBrandListing(url) {
  const response = await Api.get(url);
  return response?.data || [];
}

export default useGetBrandList;

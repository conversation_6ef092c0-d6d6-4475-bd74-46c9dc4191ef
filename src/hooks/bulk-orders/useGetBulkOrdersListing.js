import useSWR from "swr";
import Api from "../../services/Api";
import { createPo } from "../../ApiEndpoints";
import { useState } from "react";

function useGetBulkOrdersListing() {
  const [loading, setLoading] = useState(false);

  const getKey = (status) => {
    return `${createPo}?status=${status}&sort_by=date_created%2F-1`;
  };

  const getListing = async (status) => {
    setLoading(true);
    try {
      const res = await getBulkOrdersListing(getKey(status));
      if (res?.data) {
        setLoading(false);
      }
      return { data: res?.data, isLoading: loading };
    } catch (error) {
      setLoading(false);
      return { data: [], isLoading: false };
    }
  };

  return getListing;
}

async function getBulkOrdersListing(url) {
  const response = await Api.get(url);
  return response || [];
}

export default useGetBulkOrdersListing;

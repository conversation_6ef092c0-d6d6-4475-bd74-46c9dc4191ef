import Api from "../../services/Api";
import { bulkOrders } from "../../ApiEndpoints";
import { useEffect, useState } from "react";

const getKey = (type, brandIds) => {
  return `${bulkOrders}?type=${type}&brands=${brandIds}`;
};

function useGetBulkOrders(type, step) {
  const [brandIds, setBrandIds] = useState("");
  const [data, setData] = useState([]);
  const [isLoading, setLoading] = useState(false);

  useEffect(async () => {
    if (type) {
      setLoading(true);
      const res = await getBulkOrders(getKey(type, brandIds));
      setData(res?.data || []);
      setLoading(false);
    }
  }, [brandIds, type]);

  const resetState = () => {
    setBrandIds("");
  };

  const selectMultipleBrandIds = (ids) => {
    setBrandIds(ids);
  };

  return {
    data: data,
    isLoading,
    isNextPage: data?.next_page,
    selectMultipleBrandIds,
    resetState,
  };
}

async function getBulkOrders(url) {
  const response = await Api.get(url);
  return response?.data || [];
}

export default useGetBulkOrders;

import useSWR from 'swr'
import Api from '../services/Api'
import { useSelector } from 'react-redux'
import { currentCustomerURL } from '../ApiEndpoints'
import { useEffect, useRef } from 'react'

export default function useRevalidateUserSession() {
  const { user } = useSelector((state) => state.auth)
  const prevUserRef = useRef(user)

  const getKey = () => {
    return user ? currentCustomerURL : null
  }

  const { data, isValidating, mutate } = useSWR(() => getKey(), getRevalidateSession, {
    refreshInterval: 300000,
    revalidateOnFocus: false,
    revalidateOnReconnect: false,
    dedupingInterval: 300000,
  })

  // mutate (revalidate) if user is changed
  useEffect(() => {
    if (JSON.stringify(prevUserRef.current) !== JSON.stringify(user)) {
      mutate()
    }
    prevUserRef.current = user
  }, [user])

  return {
    data,
    isValidating
  }
}

async function getRevalidateSession(url) {
  const response = await Api.get(url)
  return response.data
}

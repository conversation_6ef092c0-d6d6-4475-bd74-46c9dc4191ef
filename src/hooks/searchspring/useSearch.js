import { useEffect, useState } from "react";
import {
  dataToBreadcrumbsInfo,
  dataToFacets,
  updateParams,
} from "../../utils/search-spring";
import queryString from "query-string";
import Api from "../../services/Api";
import { stringifyURL, pushState } from "../../utils/url";
import { useLocation } from "@reach/router";
import { scrollTo } from "../../utils/common";
import { searchSpringSearchURL } from "../../ApiEndpoints";

// Function to parse the search params and return an object with processed values
const processParams = (search) => {
  const obj = {};
  const params = queryString.parse(search);

  for (const key in params) {
    if (key.includes("brand") || key.includes("category")) {
      const arr = params[key].split(",");
      obj[key] = [...arr];
    } else {
      obj[key] = params[key];
    }
  }

  return obj;
};

function useSearch(excludedFacets, pageFilter, pageWrapperRef) {
  const location = useLocation();
  const path = `${location.pathname}?`;

  const queryParams = new URLSearchParams(location.search);
  const searchQuery = queryParams.get("q");

  const [data, setData] = useState();
  const [loading, setLoading] = useState(false);

  // Initialize filters with processed values from search params and provided filters
  const [filters, setFilters] = useState({
    redirectResponse: "full",
    ...processParams(location.search),
    ...(excludedFacets && {
      excludedFacets: excludedFacets,
    }),
    ...(pageFilter ? pageFilter : {})
  });

  // Generate the key to call the API with the current filters
  const getKey = () => {
    return stringifyURL(searchSpringSearchURL, { ...filters });
  };

  // Call the API when filters are changed
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      const response = await Api.post(getKey(), filters);
      setData(response?.data?.data);
      setLoading(false);
    };
    fetchData();
  }, [filters]);

  useEffect(() => {
    if (searchQuery) {
      setFilters((prevState) => ({
        ...prevState,
        ...(searchQuery && {
          q: searchQuery,
        }),
        ...(location.pathname === "/search" && {
          "filter.categories_hierarchy": [],
          "filter.brand": [],
          "filter.calculated_price.low": null,
          "filter.calculated_price.high": null,
        }),
      }));
    }
  }, [searchQuery]);

  // Handle page change and update filters and URL
  const handlePageChange = (page) => {
    const updated_params = updateParams(filters, "page");
    setFilters((prevState) => ({ ...prevState, ["page"]: page }));
    pushState(path, { ...updated_params, ["page"]: page });
    scrollTo(pageWrapperRef);
  };

  // Handle filter selection and deselection
  const applyFilter = (filter_key, filter_value, isChecked) => {
    // while applying filter
    if (isChecked) {
      const prevArr = filters[`filter.${filter_key}`]?.length
        ? filters[`filter.${filter_key}`]
        : [];
      const updatedValue = filters[`filter.${filter_key}`]?.length
        ? filters[`filter.${filter_key}`].join(",")
        : "";
      const comma = updatedValue ? "," : "";
      pushState(path, {
        ...filters,
        [`filter.${filter_key}`]: `${updatedValue}${comma}${filter_value}`,
      });
      setFilters((prevState) => ({
        ...prevState,
        [`filter.${filter_key}`]: [...prevArr, filter_value],
        page: 1,
      }));
    }
    // while removing filter
    else {
      // when removing price range filter
      if (filter_key.includes("price")) {
        const minPrice = `filter.${filter_key}.low`;
        const maxPrice = `filter.${filter_key}.high`;

        pushState(path, {
          ...filters,
          [minPrice]: undefined,
          [maxPrice]: undefined,
        });
        setFilters((prevState) => ({
          ...prevState,
          [minPrice]: undefined,
          [maxPrice]: undefined,
        }));
      }
      // when other filters...
      else {
        const updatedArr = filters[`filter.${filter_key}`].filter(
          (item) => item !== filter_value
        );
        const updatedValue = updatedArr.join(",");

        pushState(path, {
          ...filters,
          [`filter.${filter_key}`]: `${updatedValue}`,
        });
        setFilters((prevState) => ({
          ...prevState,
          [`filter.${filter_key}`]: [...updatedArr],
          page: 1,
        }));
      }
    }
    scrollTo(pageWrapperRef);
  };

  const handlePriceChange = (prices) => {
    const { minPrice, maxPrice } = prices;
    setFilters((prevState) => ({
      ...prevState,
      [`filter.calculated_price.low`]: +minPrice,
      [`filter.calculated_price.high`]: +maxPrice,
    }));
    pushState(path, {
      ...filters,
      [`filter.calculated_price.low`]: minPrice,
      [`filter.calculated_price.high`]: maxPrice,
    });
    scrollTo(pageWrapperRef);
  };

  const onPriceReset = () => {
    setFilters((prevState) => ({
      ...prevState,
      [`filter.calculated_price.low`]: undefined,
      [`filter.calculated_price.high`]: undefined,
    }));
    pushState(path, {
      ...filters,
      [`filter.calculated_price.low`]: undefined,
      [`filter.calculated_price.high`]: undefined,
    });
    scrollTo(pageWrapperRef);
  };

  const onClearAllFilter = () => {
    setFilters({
      q: filters["q"],
      ...(excludedFacets && {
        excludedFacets: excludedFacets,
      }),
      ...(pageFilter ? pageFilter : {}),
    });
    pushState(path, {
      q: filters["q"],
    });
  };

  const handleSortingOptionChange = (sortBy) => {
    const arr = sortBy.split("=");

    // while user select default option in sorting.
    if (arr.length === 1 && arr[0] === "") {
      for (const key in filters) {
        if (key.includes("sort")) {
          const updated_params = updateParams(filters, key);

          setFilters(updated_params);
          pushState(path, updated_params);
        }
      }
    }

    // array length must be two, first is key and second is value of the filter.
    if (arr.length === 2) {
      for (const key in filters) {
        if (key.includes("sort")) {
          delete filters[key];
        }
      }
      setFilters((prevState) => ({
        ...prevState,
        [`sort.${arr[0]}`]: arr[1],
        page: 1,
      }));
      pushState(path, { ...filters, [`sort.${arr[0]}`]: arr[1] });
    }
  };

  // parse or convert data before sending to the components.
  const isLoading = loading;
  const products = data?.results || [];
  const sortingOptions = data?.sorting?.options || [];
  const pagination = data?.pagination || {};
  const breadcrumbs = dataToBreadcrumbsInfo(data?.breadcrumbs, pagination);
  const facets = dataToFacets(data?.facets);
  const filterSummary = data?.filterSummary || [];
  const redirectURL = data?.merchandising?.redirect || null;

  return {
    filters,
    products,
    sortingOptions,
    pagination,
    isLoading,
    breadcrumbs,
    facets,
    filterSummary,
    redirectURL,
    setFilters,
    handlePageChange,
    applyFilter,
    handlePriceChange,
    onPriceReset,
    onClearAllFilter,
    handleSortingOptionChange,
  };
}

export default useSearch;

import React from "react";
import { useFormik } from "formik";
import { object, string } from "yup";

import TextField from "../../components/form/TextField";
import Button from "../../components/form/button/Button";

import {
  emailRegExp,
  emailRegMsg,
  getFormError,
  requiredEmailMsg,
} from "../../utils/form";
import useSubscribeMail from "../../hooks/subscribe/useSubscribeMail";

const validationSchema = object().shape({
  email: string()
    .matches(emailRegExp, emailRegMsg)
    .email(requiredEmailMsg())
    .required(requiredEmailMsg("Email Address")),
});

const NewsLetter = () => {
  const { onCreateSubscriber } = useSubscribeMail();

  const formik = useFormik({
    initialValues: { email: "" },
    validationSchema: validationSchema,
    onSubmit: (values) => {
      onCreateSubscriber(values);
    },
  });

  return (
    <>
      <h3 className="footer-col-title">subscribe to our newsletter</h3>
      <p>Get the latest updates on new products and upcoming sales</p>

      <form onSubmit={formik.handleSubmit} className={"flex newsletter-form"}>
        <TextField
          id={"email"}
          width={250}
          placeholder={"Your email address"}
          onChange={formik.handleChange}
          value={formik.values.email}
          error={getFormError(formik.errors, "email")}
        />
        <Button type={"submit"}>Subscribe</Button>
      </form>
      <div className="footer-newsletter-summary">
        <p>It has survived not only five centuries.</p>
      </div>
    </>
  );
};

export default NewsLetter;

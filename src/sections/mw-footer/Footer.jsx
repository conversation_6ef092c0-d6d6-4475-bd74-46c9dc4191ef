import React from "react";
import { Link } from "gatsby";
import {
  default_footer_data,
  default_customer_service_links_footer,
  default_quick_links_footer,
  payment_icons,
  default_store_info,
} from "../../../config/footer";

import IconFacebook from "../../assets/icon_js/IconFacebook";
import IconInstagram from "../../assets/icon_js/IconInstagram";
import IconTwitter from "../../assets/icon_js/IconTwitter";
import Image from "../../components/common/Image";
import IconWhatsapp from "../../assets/icon_js/IconWhatsapp";

const data = process.env.GATSBY_footer_navigation1_id
  ? JSON.parse(process.env.GATSBY_footer_navigation1_id)["navigations"]
  : default_quick_links_footer;

const QuickLinks = () => {

  return data.map((key, index) => (
    <li key={index}>
      <Link to={key.url}> {key.label}</Link>
    </li>
  ));
};

const CustomerServiceLinks = () => {
  const data = process.env.GATSBY_footer_navigation2_id
    ? JSON.parse(process.env.GATSBY_footer_navigation2_id)["navigations"]
    : default_customer_service_links_footer;

  return data.map((key, index) => (
    <li key={(index, index)}>
      <Link to={key.url} key={index}>
        {key.label}
      </Link>
    </li>
  ));
};

const {
  logo,
  copy_right_section_right
} = default_footer_data;

const storeInfo = process.env.GATSBY_FOOTER_INFO
? JSON.parse(process.env.GATSBY_FOOTER_INFO)
: default_store_info;

const Footer = () => {
  return (
    <footer className="footer-section">
      <div className="footer-top-section">
        <div className="container">
          <div className="row flex flex-wrap">
            <div className="col footer-col footer-address-col">
              <div className="footer-logo">
                <Link to={"/"}>
                  <Image src={logo} alt={"header-logo"} />
                </Link>
              </div>
              <div className="footer-adress">
                <address>
                  <p>{storeInfo["store_address"]}</p>
                </address>
              </div>
              <div className="footer-contact-number">
                <p>Customer Service {storeInfo["customer_service"]}</p>

                <p>Sales Direct {storeInfo["sales_direct"]}</p>
                <p>FAX: {storeInfo["store_fax"]}</p>
              </div>
              <div className="footer-email-support">
                <p>
                  <Link to={`mailto:${storeInfo["support_email"]}`}>
                    {storeInfo["support_email"]}
                  </Link>
                </p>
                <p>
                  <Link to={`mailto:${storeInfo["sales_email"]}`}>
                    {storeInfo["sales_email"]}
                  </Link>
                </p>
              </div>
            </div>

            <div className="col flex flex-wrap footer-col footer-col-large">
              <div className="col footer-nav-col">
                <h4 className="footer-nav-title">Quick links</h4>
                <ul className="footer-nav-list">
                  <QuickLinks />
                </ul>
              </div>
              <div className="col footer-nav-col">
                <h4 className="footer-nav-title">Customer Service</h4>
                <ul className="footer-nav-list">
                  <CustomerServiceLinks />
                </ul>
              </div>
            </div>
            <div className="col footer-col footer-col-business">
              <h4 className="footer-nav-title">Business Hours</h4>
              <div className="footer-support-hours">
                <p>
                  <strong>Cash & Carry</strong>
                  <br />
                  <span>{storeInfo["business_hours"]}</span>
                </p>
                <p>
                  <strong>Customer Support</strong>
                  <br />
                  <span>{storeInfo["customer_support"]}</span>
                </p>
              </div>
              <div className="footer-social-logo">
                <ul>
                  <li>
                    <Link target="_blank" to={storeInfo["fb_link"]}>
                      <i className="icon">
                        <IconFacebook />
                      </i>
                    </Link>
                  </li>
                  <li>
                    <Link target="_blank" to={storeInfo["insta_link"]}>
                      <i className="icon">
                        <IconInstagram />
                      </i>
                    </Link>
                  </li>
                  <li>
                    <Link target="_blank" to={storeInfo["tweeter_link"]}>
                      <i className="icon">
                        <IconTwitter />
                      </i>
                    </Link>
                  </li>
                  <li>
                    <Link target="_blank" to={storeInfo["whatsapp_link"]}>
                      <i className="icon">
                        <IconWhatsapp />
                      </i>
                    </Link>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="footer-bottom-section">
        <div className="container">
          <div className="footer-payment-col">
            <ul className="row flex-wrap">
              {payment_icons.map((i, index) => (
                <li className="col" key={index}>
                  <div className="payment-img">
                    <Image src={i["src"]} alt="" />
                  </div>
                </li>
              ))}
            </ul>
          </div>

          <p className="footer-copyright-text text-center">
            {copy_right_section_right}
          </p>

          <div className="warning-text text-center">
            <p>
              NOT FOR SALE TO MINORS | CALIFORNIA PROPOSITION 65 -{" "}
              <strong>Warning:</strong> This product contains nicotine, a
              chemical known to the state of California to cause birth defects
              or other reproductive harm. Midwest Goods products are not smoking
              cessation products and have not been evaluated by the Food and
              Drug Administration, nor are they intended to treat, prevent or
              cure any disease or condition. KEEP OUT OF REACH OF CHILDREN AND
              PETS. All product names, trademarks and images are the property of
              their respective owners, which are in no way associated or
              affiliated with Midwest Goods Inc. Product names and images are
              used solely for the purpose of identifying the specific products.
              Use of these names does not imply any co-operation or endorsement.
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;

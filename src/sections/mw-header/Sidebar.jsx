import React, { useState } from "react";
import { Link } from "gatsby";
import {
  default_categories_sidebar_navigation,
  default_brands_sidebar_navigation,
  default_quicklinks_sidebar_navigation,
} from "../../../config/navigation";

import { useSelector } from "react-redux";
import { authCleanup } from "../../utils/cleanup";

const RecursiveNavItem = ({ item, level = 1, ToggleSidebar }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const hasChildren = item.children && item.children.length > 0;

  const handleExpand = (e) => {
    e.stopPropagation();
    setIsExpanded(!isExpanded);
  };

  return (
    <li>
      <div>
        <Link
          to={item.url}
          className="sd-link"
          onClick={(e) => {
            ToggleSidebar();
          }}
          style={{ paddingLeft: `${level * 20}px` }}
        >
          {item.label}
        </Link>
        {hasChildren && (
          <button
            onClick={handleExpand}
            className={isExpanded ? 'active' : ''}
          >
            {isExpanded ? '−' : '+'}
          </button>
        )}
      </div>
      {hasChildren && isExpanded && (
        <ul
        // style={{  marginLeft: `${level * 20}px`
        // }}
        >
          {item.children.map((child, index) => (
            <RecursiveNavItem
              key={index}
              item={child}
              level={level + 1}
              ToggleSidebar={ToggleSidebar}
            />
          ))}
        </ul>
      )}
    </li>
  );
};

// The MwSidebar component receives two props:
// - isOpen: a boolean indicating whether the sidebar is currently open
// - ToggleSidebar: a function to toggle the state of the sidebar
export default function MwSidebar({ isOpen, ToggleSidebar }) {
  const { user } = useSelector((state) => state.auth);

  // We get the default navigation items for each section
  // const account_nav = default_my_account_sidebar_navigation;
  let category_nav = default_categories_sidebar_navigation;
  let brands_nav = default_brands_sidebar_navigation;
  let quick_links = default_quicklinks_sidebar_navigation;

  // If there are environment variables set for the navigation items,
  // we parse them and override the default values
  if (process.env.GATSBY_sidebar_navigation1_id) {
    category_nav = JSON.parse(process.env.GATSBY_sidebar_navigation1_id)[
      "navigations"
    ];
  }
  if (process.env.GATSBY_sidebar_navigation2_id) {
    brands_nav = JSON.parse(process.env.GATSBY_sidebar_navigation2_id)[
      "navigations"
    ];
  }
  if (process.env.GATSBY_sidebar_navigation3_id) {
    quick_links = JSON.parse(process.env.GATSBY_sidebar_navigation3_id)[
      "navigations"
    ];
  }

  // The component renders a sidebar that contains four sections:
  // My Account, Categories, Brands, and Quick Links
  return (
    <>
      <div className="">
        <div
          className={`header-slider-navigation ${isOpen === true ? "active" : ""
            }`}
        >
          <>
            <div className="nav-section">
              <div className="nav-title">
                <h4>My Account</h4>
                {/* <div className="btn btn-primary" onClick={ToggleSidebar}>
                  <i className="fa fa-times"></i>
                </div> */}
              </div>
              <div className="nav-list-bar">
                {/* <ul>
                  {account_nav.map((item, index) => (
                    <li key={index}>
                      <Link className="sd-link" to={item.url}>
                        {item.label}
                      </Link>
                    </li>
                  ))}
                </ul> */}

                <ul>
                  <li>
                    <Link to={"/orders"} onClick={ToggleSidebar}>
                      Order Status
                    </Link>
                  </li>
                  <li>
                    <Link to={"/contact-us/"} onClick={ToggleSidebar}>
                      Contact Us
                    </Link>
                  </li>
                  <li>
                    <Link to={"/help-center/"} onClick={ToggleSidebar}>
                      Help Center
                    </Link>
                  </li>
                  {!user?.accessToken ? (
                    <>
                      <li className="account-links" onClick={ToggleSidebar}>
                        <Link to={`/login`}>Sign in</Link>/
                        <Link to="/create-account">Create an Account</Link>
                      </li>
                    </>
                  ) : (
                    <li>
                      <Link
                        to="/login"
                        onClick={() => {
                          authCleanup({
                            triggerCrossTab: true,
                            redirectToLogin: true,
                            clearSessionData: true
                          });
                          ToggleSidebar();
                        }}
                      >
                        Sign out
                      </Link>
                    </li>
                  )}
                </ul>
              </div>
            </div>

            <div className="nav-section">
              <div className="nav-title">
                <h4>Categories</h4>
              </div>
              <div className="nav-list-bar">
                <ul>
                  {category_nav.map((item, index) => (
                    <RecursiveNavItem
                      key={index}
                      item={item}
                      ToggleSidebar={ToggleSidebar}
                    />
                  ))}
                </ul>
              </div>
            </div>

            <div className="nav-section">
              <div className="nav-title">
                <h4>Brands</h4>
              </div>
              <div className="nav-list-bar">
                <ul>
                  {brands_nav.map((item, index) => (
                    <RecursiveNavItem
                      key={index}
                      item={item}
                      ToggleSidebar={ToggleSidebar}
                    />
                  ))}
                </ul>
              </div>
            </div>

            <div className="nav-section">
              <div className="nav-title">
                <h4>Quick Links</h4>
                {/* <div className="btn btn-primary" onClick={ToggleSidebar}>
                  <i className="fa fa-times"></i>
                </div> */}
              </div>
              <div className="nav-list-bar">
                <ul>
                  {quick_links.map((item, index) => (
                    <RecursiveNavItem
                      key={index}
                      item={item}
                      ToggleSidebar={ToggleSidebar}
                    />
                  ))}
                </ul>
              </div>
            </div>
          </>
        </div>

        <div
          className={`header-slider-overlay ${isOpen === true ? "active" : ""}`}
          onClick={ToggleSidebar}
        ></div>
      </div>
    </>
  );
}

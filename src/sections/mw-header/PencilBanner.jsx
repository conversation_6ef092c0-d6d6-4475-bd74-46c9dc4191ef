import { Link } from "gatsby";
import React from "react";
import { useSelector } from "react-redux";
import { authCleanup } from "../../utils/cleanup";

function PencilBanner() {
  const { user } = useSelector((state) => state.auth);
  const handleLogout = () => {
    authCleanup({
      triggerCrossTab: true,
      redirectToLogin: true,
      clearSessionData: true
    });
  };

  return (
    <>
      {user?.customer_name ? (
        <div className="header-pencil-banner">
          <div className="container">
            <div className="user-status text-right">
              <p className="font-bold">
                Welcome!&nbsp;{`${user?.customer_name}`}
                {/* ,{" "} */}
                <span>
                  <Link
                    to={"/login"}
                    className="reverse-link-style header-logout-link"
                    onClick={handleLogout}
                  >
                    Logout
                  </Link>
                </span>
              </p>
            </div>
          </div>
        </div>
      ) : null}
    </>
  );
}

export default PencilBanner;

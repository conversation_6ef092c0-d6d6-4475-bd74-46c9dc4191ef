import React from "react";
import { <PERSON> } from "gatsby";
import MWheaderData from "../../../config/MWheaderData";
import { isEmpty } from "../../utils/functions";

const MWHeaderNavigation = () => {
  let level = 1;
  const data = JSON.parse(process.env.GATSBY_header_navigation_id || "{}");

  function renderMenuOptions(node) {
    return node.map((child) => {
      // If current node has one or more childrens.
      if (
        child.children &&
        Array.isArray(child.children) &&
        child.children.length > 0
      ) {
        // Level count for dynamic class.
        level = level + 1;

        return (
          <li className={`has-dropdown has-arrow`} key={child.url}>
            <Link
              className={`${child.label.toLowerCase().replaceAll(/ /gi, "-")}`}
              to={`${child.url}`}
            >
              {child.label}
            </Link>
            <ul className={`dropdown-list`}>
              {renderMenuOptions(child.children)}
            </ul>
          </li>
        );
      }
      // If current node has not any children.
      else {
        level = 1; // Reset level count to 1.
        return (
          <li
            className={`${child.label.toLowerCase().replaceAll(/ /gi, "-")}`}
            key={child.url}
          >
            <Link
              className={`${child.label.toLowerCase().replaceAll(/ /gi, "-")}`}
              to={`${child.url}`}
            >
              {child.label}
            </Link>
          </li>
        );
      }
    });
  }

  return (
    <nav>
      <ul className="flex vertical-middle align-center flex-wrap nav-primary-list">
        {!isEmpty(data)
          ? renderMenuOptions(data["navigations"])
          : renderMenuOptions(MWheaderData())}
      </ul>
    </nav>
  );
};

export default MWHeaderNavigation;

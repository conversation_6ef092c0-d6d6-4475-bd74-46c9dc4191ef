import React from "react";
import { useSelector } from "react-redux";
import { Link } from "gatsby";
import { navigate, useLocation } from "@reach/router";
import { useEffect } from "react";
import useGetCheckloyaltypointsVisibility from "../../hooks/loyaltypoints/useGetCheckloyaltypointsVisibility";
import IconChat from "../../assets/icon_js/IconChat";
import IconSupport from "../../assets/icon_js/IconSupport";
import IconUser from "../../assets/icon_js/IconUser";
import IconCart from "../../assets/icon_js/IconCart";
import IconSearch from "../../assets/icon_js/IconSearch";
import IconCloseLight from "../../assets/icon_js/IconCloseLight";
import { formatPrice } from "../../utils/money";
import IconScanner from "../../assets/icon_js/IconScanner";
import { authCleanup } from "../../utils/cleanup";

const customerGroupIsList = [1, 11, 28, 45, 48, 60, 90, 91, 98, 103, 104, 105];

const MWHeaderRightNavigation = () => {
  const { user } = useSelector((state) => state.auth);
  const location = useLocation();
  const { currentLoginUser } = useSelector((state) => state.customer);
  const { loyalty_points_visible, isChecking } = useGetCheckloyaltypointsVisibility();

  useEffect(() => {
    // Check immediately when component mounts or when URL changes
    const isLoyaltyPage = location.pathname === "/loyalty-and-rewards/redeem-coupon/";
    if (isLoyaltyPage && !isChecking && !loyalty_points_visible) {
      navigate("/");
    }
  }, [loyalty_points_visible, isChecking, location.pathname]);

  const handleLogout = () => {
    authCleanup({
      triggerCrossTab: true,
      redirectToLogin: true,
      clearSessionData: true
    });
  };

  const handelChatApp = () => {
    const element = document.getElementById('zohohc-asap-web-launcherbox');
    if (element) {
      element.click();
    }
  }

  const HeaderToggle = () => {
    const bodyClass = document.body.classList;

    if (bodyClass.contains("header-toggle-active")) {
      bodyClass.remove("header-toggle-active");
    } else {
      document.body.classList.add("header-toggle-active");
    }
  };

  return (
    <>
      <ul className="header-right-nav flex align-middle">
        <li
          className="header-search-link show-tablet-small"
          onClick={() => HeaderToggle()}
        >
          <span className="icon-search">
            <IconSearch />
          </span>
          <span className="icon-close">
            <IconCloseLight />
          </span>
        </li>

        {/* {user?.status === 200 ? (
          <li>
            <Link to="/loyalty-and-rewards/redeem-coupon">
              <IconGift />
            </Link>
          </li>
        ) : null} */}

        {user?.status === 200 ? (
          <li>
            <Link to="/scanner">
              <IconScanner />
            </Link>
          </li>
        ) : null}

        <li className="has-dropdown header-account-link">
          {user?.status === 200 ? (
            <Link to={"/orders"}>
              <IconUser />
            </Link>
          ) : (
            <IconUser />
          )}

          <ul className="dropdown-list dropdown-mid text-center">
            {user?.customer_name ? (
              <>
                <li>
                  <p>
                    Welcome!&nbsp; <strong>{`${user?.customer_name}`}</strong>
                  </p>
                </li>
                {user?.store_credit ? (
                  <li>
                    <p>
                      You have{" "}
                      <strong>{`${formatPrice(user?.store_credit)}`}</strong>{" "}
                      store credit.
                    </p>
                  </li>
                ) : (
                  <></>
                )}
              </>
            ) : null}

            {!user?.accessToken ? (
              <>
                <li>
                  <Link to={`/login`}>Sign in</Link>
                </li>
                <li>
                  <Link to="/create-account">Create an Account</Link>
                </li>
              </>
            ) : (
              <>
                {customerGroupIsList.includes(
                  currentLoginUser?.customer_group_id
                ) ? (
                  <li>
                    <Link to="/available-products">Bulk Orders</Link>
                  </li>
                ) : null}
                <li>
                  <Link to="/orders">My Account</Link>
                </li>
                <li>
                  <Link to="/scanner">Scanner App</Link>
                </li>
                {loyalty_points_visible ? (
                  <li>
                    <Link to="/loyalty-and-rewards/redeem-coupon">
                      Loyalty Points
                    </Link>
                  </li>
                ) : null}
                <li>
                  <Link to="/login" onClick={handleLogout}>
                    Sign out
                  </Link>
                </li>
              </>
            )}
          </ul>
        </li>

        <li className="header-chat-link hide-tablet">
          <button onClick={() => handelChatApp()}>
            <IconChat />
          </button>
        </li>

        <li className="header-support-link hide-tablet">
          <Link to="/contact-us">
            <IconSupport />
          </Link>
        </li>

        <li className="header-cart-icon-black show-tablet">
          <Link to="/cart">
            <IconCart />
          </Link>
        </li>
      </ul>
    </>
  );
};

export default MWHeaderRightNavigation;
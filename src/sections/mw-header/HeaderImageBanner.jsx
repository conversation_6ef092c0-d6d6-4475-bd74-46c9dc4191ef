import React from "react";
import { storeInfoURL } from "../../ApiEndpoints";
import { Link } from "gatsby";

function VideoBanner({ src, className }) {
  return (
    <video id="header_video_0" autoPlay loop preload="auto" playsInline muted className={className}>
      <source src={src} type="video/mp4" />
      Your browser does not support the video tag.
    </video>
  );
}

function ImageBanner({ src, className }) {
  return (
    // WARNING :
    // Don't use <Image /> here because it will create jerk effect.
    // WARNING :
    <img
      src={src}
      alt="top-banner"
      title="Top Banner"
      className={className}
    />
  );
}

function HeaderImageBanner({ desktop_banner, mobile_banner, banner_url }) {
  const processBanner = (banner, isMobile) => {
    const baseUrl = process.env.GATSBY_IMAGE_CDN_BASEURL;
    const url = banner.includes("https://cdn11.bigcommerce.com")
      ? banner
      : `${baseUrl}${storeInfoURL}${banner}`;

    return banner.includes("mp4") ||
      banner.includes("mov") ||
      banner.includes("avi") ||
      banner.includes("mkv") ||
      banner.includes("wmv") ? (
      <VideoBanner
        src={url}
        className={`full-width banner-for-${isMobile ? "mobile" : "desktop"}`}
      />
    ) : (
      <ImageBanner
        src={url}
        className={`full-width banner-for-${isMobile ? "mobile" : "desktop"}`}
      />
    );
  };

  return (
    <div className="header-full-width-banner">
      <Link
        to={banner_url}
      >
        {processBanner(desktop_banner, false)}
        {processBanner(mobile_banner, true)}
      </Link>
    </div>
  );
}

export default HeaderImageBanner;

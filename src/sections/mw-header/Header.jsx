import React, { useContext, useState } from "react";
import { Link } from "gatsby";
import HeaderRightNav from "./MWHeaderRightNavigation";
import MWHeaderNavigation from "./MWHeaderNavigation";
import IconCall from "../../assets/icon_js/IconCall";
import IconCart from "../../assets/icon_js/IconCart";
import { CartContext } from "../../context/CartContext";
import IconHemburger from "../../assets/icon_js/IconHemburger";
import SearchBarHeader from "../../components/common/SearchBarHeader";
import MwSidebar from "./Sidebar";
import HeaderImageBanner from "./HeaderImageBanner";
import { default_header_info } from "../../../config/header";
import { storeInfoURL } from "../../ApiEndpoints";

const headerInfo = process.env.GATSBY_HEADER_INFO
  ? JSON.parse(process.env.GATSBY_HEADER_INFO)
  : default_header_info;

const MWHeader = () => {
  const { lineItemsLength } = useContext(CartContext);
  const [isOpen, setIsopen] = useState(false);

  const ToggleSidebar = () => {
    isOpen === true ? setIsopen(false) : setIsopen(true);
  };


  return (
    <header className="header-section">
      {/* <PencilBanner /> */}

      <div className="notice-banner">
        <p>
          {headerInfo['header_nicotine_banenr']}
        </p>
      </div>
      <HeaderImageBanner desktop_banner={headerInfo['header_pencil_banner']} mobile_banner={headerInfo['mobile_header_pencil_banner']} banner_url={headerInfo['header_pencil_banner_url']} />
      <div className="header-middle-section">
        <div className="container">
          <div className="row flex vertical-middle justify-space">
            <div className="col flex vertical-middle header-logo-block">
              <MwSidebar isOpen={isOpen} ToggleSidebar={ToggleSidebar} />
              <div className="hemburger-link">
                <i className="icon" onClick={ToggleSidebar}>
                  <IconHemburger />
                </i>
              </div>
              <div className="header-logo">
                <Link to="/">
                  {headerInfo["header_logo"].includes(
                    "https://cdn11.bigcommerce.com"
                  ) ? (
                    <img src={headerInfo["header_logo"]} alt="header logo" />
                  ) : (
                    <img
                      src={`${process.env.GATSBY_IMAGE_CDN_BASEURL}${storeInfoURL}${headerInfo["header_logo"]}`}
                      alt="header logo"
                    />
                  )}
                </Link>
              </div>
            </div>

            <div className="col row flex vertical-middle align-center header-search-block">
              <div className="col promo-text">
                <p className="font-medium big">{headerInfo["header_text"]}</p>
              </div>
              <div className="col search-form">
                <SearchBarHeader />
              </div>
              <div className="col header-call-link">
                <i className="icon">
                  <IconCall />
                </i>
                <span className="font-medium">
                  {headerInfo["header_contact_no"]}
                </span>
              </div>
            </div>

            <div className="col header-right-block flex align-right">
              <HeaderRightNav />
            </div>
          </div>
        </div>
      </div>
      {/* header primary navigation */}
      <div className="header-primary-nav-section">
        <div className="container flex justify-space vertical-middle">
          <div className="header-navigation-block flex">
            <MWHeaderNavigation />
          </div>
          <div className="header-cart-menu">
            <Link to="/cart">
              <IconCart />
              <strong>My Cart</strong>(<span>{lineItemsLength}</span>)
            </Link>
          </div>
        </div>
      </div>
    </header>
  );
};

export default MWHeader;

import TextField from "../../components/form/TextField";
import { useFormik } from "formik";
import { getFormError } from "../../utils/form";
import Button from "../../components/form/button/Button";
import { useDispatch } from "react-redux";
import {
  login_form_initial_values,
  login_validation_schema,
} from "../../utils/user";
import { login } from "../../features/userSlice";
import { redirectTo } from "../../utils/url";
import ReCAPTCHA from "react-google-recaptcha";
import React, { useRef } from "react";
import { Link, navigate } from "gatsby";
import useToastify from "../../hooks/ui/useToastify";
import useAccountIframe from "../../hooks/customer/useAccountIframe";
import { useLocation } from "@reach/router";

const LoginForm = ({ setCloseModal, isOpen }) => {
  const { toastMessage } = useToastify();
  const dispatch = useDispatch();
  const { setIframe } = useAccountIframe();
  const recaptchaRef = useRef();
  const location = useLocation();

  const validateCustomer = (values) => {
    dispatch(login({ values: values, toastMessage: toastMessage })).then(
      async (res) => {
        // after successfully logged in...
        if (res?.payload) {
          if (res.payload?.status === 200) {
            await setIframe();
            if (isOpen) {
              navigate(location.pathname);
              setCloseModal();
            } else {
              navigate(redirectTo(location.href));
            }
          }
        }
      }
    );
  };

  const formik = useFormik({
    initialValues: login_form_initial_values,
    validationSchema: login_validation_schema,
    onSubmit: (values) => {
      const recaptchaValue = recaptchaRef.current.getValue();
      if (recaptchaValue) {
        validateCustomer(values);
      } else {
        toastMessage("error", "Please Check re-captcha first to login");
      }
    },
  });

  return (
    <>
      <form onSubmit={formik.handleSubmit} className="login-form form">
        <div className="form-field">
          <TextField
            id="user-email"
            label={"Email Address"}
            labelFor={"Email Address"}
            name={"email"}
            error={getFormError(formik.errors, "email")}
            type={"email"}
            onChange={formik.handleChange}
            touched={formik.touched["email"]}
          />
        </div>

        <div className="form-field">
          <TextField
            id="user-password"
            name={"password"}
            label={"Password"}
            htmlFor={"password"}
            type={"password"}
            onChange={formik.handleChange}
            touched={formik.touched["password"]}
            error={getFormError(formik.errors, "password")}
          />
        </div>
        <div className="gcaptcha-wrapper">
          <ReCAPTCHA
            ref={recaptchaRef}
            sitekey={`${process.env.GATSBY_RECAPTCHA_SITE_KEY}`}
          />
        </div>

        <div className="form-actions">
          <Button type="submit" className="button">
            Sign in
          </Button>
          {isOpen ? (
            <Link className="register-link" to="/create-account">
              Register
            </Link>
          ) : (
            <Link className="forgot-password link-dark" to="/forgot-password">
              Forgot your password?
            </Link>
          )}
        </div>
      </form>
    </>
  );
};

export default LoginForm;

import React, { useContext, useState } from "react";
import PropTypes from "prop-types";
import { Field } from "formik";

import {
  getSingleGridVariant,
  validateInputBox,
} from "../../utils/productGrid";
import useToastify from "../../hooks/ui/useToastify";
import { PriceSkuContext } from "../../context/PriceSkuContext";
import InStockNotify from "./InStockNotify";
import { getMinimumPrice } from "../../utils/money";

function SingleVerticalGrid({
  row_titles,
  variants,
  bigcommerce_id,
  min_purchase_qty,
  max_purchase_qty,
  formik,
}) {
  const { toastMessage } = useToastify();
  const { setPriceSku, priceSku } = useContext(PriceSkuContext);
  const [rowId, setRowId] = useState(null);

  return (
    <>
      {row_titles.map((r_title, row_index) => (
        <div className="pog__row-item" key={r_title.id}>
          <div className="table-cell table-cell-first">{r_title.label}</div>
          {(() => {

            let variant = getSingleGridVariant(r_title, variants);
            const price = getMinimumPrice(
              variant?.["price"]["value"],
              variant?.["sale_price"]["value"],
              variant?.["retail_price"]["value"]
            );

            // if no variant found, return null
            if (!variant) return null;

            return (
              <div className="table-cell table-cell-input" key={variant.id}>
                <label className="form-label" htmlFor={variant.id}>
                  {`${variant.inventory.available_to_sell} Left`}
                </label>
                <Field
                  type="number"
                  id={variant.id}
                  name={`${bigcommerce_id},${variant.id},${variant.sku},${price
                    .toString()
                    .replace(".", "-")}`}
                  placeholder="0"
                  className={
                    variant?.inventory?.available_to_sell === 0 ||
                    !variant?.is_purchasable
                      ? "table-cell-disable"
                      : ""
                  }
                  onWheel={(e) => e.target.blur()}
                  onFocus={() => {
                    setPriceSku(() => {
                      // set price sku in context
                      return {
                        sku: variant.sku,
                        base_price: variant.price.value,
                        sale_price: variant.sale_price.value,
                        retail_price: variant.retail_price.value,
                        variantId: variant?.id,
                      };
                    });

                    if (variant?.["inventory"]?.["available_to_sell"] === 0) {
                      setRowId(row_index);
                    } else {
                      setRowId(null);
                    }
                  }}
                  onBlur={(e) =>
                    validateInputBox(
                      e.target.value,
                      min_purchase_qty,
                      max_purchase_qty,
                      variant.inventory.available_to_sell,
                      toastMessage,
                      `${bigcommerce_id},${variant.id},${variant.sku},${price
                        .toString()
                        .replace(".", "-")}`,
                      formik
                    )
                  }
                  disabled={!variant?.is_purchasable}
                />
              </div>
            );
          })()}
          {rowId === row_index && priceSku?.variantId ? (
            <InStockNotify
              productId={bigcommerce_id}
              variantId={priceSku.variantId}
            />
          ) : null}
        </div>
      ))}
    </>
  );
}

SingleVerticalGrid.propTypes = {
  row_titles: PropTypes.array,
  variants: PropTypes.array,
  bigcommerce_id: PropTypes.number,
  min_purchase_qty: PropTypes.number,
  max_purchase_qty: PropTypes.number,
  formik: PropTypes.object,
};

export default React.memo(SingleVerticalGrid);

// Import React and other required modules
import React from "react";
import PropTypes from "prop-types";

// Import the components required for grid rendering
import SingleHorizontalGrid from "./SingleHorizontalGrid";
import SingleVerticalGrid from "./SingleVerticalGrid";
import TwoDimentionalGrid from "./TwoDimentionalGrid";

// Import utility functions
import { calculateGridTotal } from "../../utils/productGrid";
import { isUserLoggedIn } from "../../utils/auth";

// Import the loader component
import OptionGridLoader from "../../components/form/OptionGridLoader";
import useCustomerGroupRestriction from "../../hooks/customer/useCustomerGroupRestriction";

// Define the TableHead component
const TableHead = ({ headings = [] }) => {
  if (headings.length) {
    return (
      <div className="pog__row-item">
        <div className="table-cell table-cell-first"></div>
        {headings.map((i) => (
          <div className="table-cell" key={i["label"]}>
            {i["label"]}
          </div>
        ))}
      </div>
    );
  } else {
    return <></>;
  }
};

// Define the TableBody component
const TableBody = ({
  row_titles = [],
  variants = [],
  headings = [],
  bigcommerce_id,
  min_purchase_qty,
  max_purchase_qty,
  formik,
}) => {
  // Render SingleHorizontalGrid if only nicotine levels are available
  if (headings.length && row_titles.length === 0) {
    return (
      <SingleHorizontalGrid
        headings={headings}
        variants={variants}
        bigcommerce_id={bigcommerce_id}
        min_purchase_qty={min_purchase_qty}
        max_purchase_qty={max_purchase_qty}
        formik={formik}
      />
    );
  }

  // Render SingleVerticalGrid if only flavors are available
  if (headings.length === 0 && row_titles.length) {
    return (
      <SingleVerticalGrid
        row_titles={row_titles}
        variants={variants}
        bigcommerce_id={bigcommerce_id}
        min_purchase_qty={min_purchase_qty}
        max_purchase_qty={max_purchase_qty}
        formik={formik}
      />
    );
  }

  // Render TwoDimentionalGrid if both nicotine levels and flavors are available
  if (headings.length > 0 && row_titles.length > 0) {
    return (
      <TwoDimentionalGrid
        headings={headings}
        row_titles={row_titles}
        variants={variants}
        bigcommerce_id={bigcommerce_id}
        min_purchase_qty={min_purchase_qty}
        max_purchase_qty={max_purchase_qty}
        formik={formik}
      />
    );
  } else {
    return <></>;
  }
};

// Define the ProductGrid component
function ProductGrid({
  data,
  bigcommerce_id,
  min_purchase_qty,
  max_purchase_qty,
  formik,
  isLoading,
}) {
  // Extract the data for the grid rendering
  const { headings, row_titles, variants } = data;

  // Check if the user is logged in
  const isUser = isUserLoggedIn();
  const { isCustomerRestricted } = useCustomerGroupRestriction()

  return (
    <>
      {isLoading && headings?.length && isUser ? (
        <>
          <OptionGridLoader />
        </>
      ) : (
        <div className="table-option-grid">
          <div className="pog__header">
            <TableHead headings={headings} />
          </div>
          <div className="pog__body">
            <TableBody
              row_titles={row_titles}
              variants={variants}
              headings={headings}
              bigcommerce_id={bigcommerce_id}
              min_purchase_qty={min_purchase_qty}
              max_purchase_qty={max_purchase_qty}
              formik={formik}
            ></TableBody>
          </div>
        </div>
      )}
      {isUser ? (
        headings?.length || row_titles?.length ? (
          !isCustomerRestricted ? <h5>{`Total: ${calculateGridTotal(formik?.["values"])}`}</h5> : null
        ) : null
      ) : null}
    </>
  );
}

ProductGrid.propTypes = {
  variants: PropTypes.array,
};

export default ProductGrid;

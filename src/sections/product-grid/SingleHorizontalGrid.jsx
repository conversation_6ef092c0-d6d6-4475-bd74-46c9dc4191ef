import React, { useContext, useState } from "react";
import PropTypes from "prop-types";
import { Field } from "formik";

import useToastify from "../../hooks/ui/useToastify";
import { PriceSkuContext } from "../../context/PriceSkuContext";
import {
  getSingleGridVariant,
  validateInputBox,
} from "../../utils/productGrid";
import InStockNotify from "./InStockNotify";
import { getMinimumPrice } from "../../utils/money";

function SingleHorizontalGrid({
  headings,
  variants,
  bigcommerce_id,
  min_purchase_qty,
  max_purchase_qty,
  formik,
}) {
  // Destructure toastMessage from useToastify hook instead of accessing it via object dot notation
  const { toastMessage } = useToastify();
  const { setPriceSku, priceSku } = useContext(PriceSkuContext);
  const [rowId, setRowId] = useState(null);

  return (
    <div className="pog__row-item">
      {/* Use empty div instead of table-cell-first class */}
      <div className="table-cell"></div>
      {headings.map((h_title, row_index) => {

        let variant = getSingleGridVariant(h_title, variants);
        const price = getMinimumPrice(
          variant?.["price"]["value"],
          variant?.["sale_price"]["value"],
          variant?.["retail_price"]["value"]
        );

        return (
          <div className="table-cell table-cell-input" key={variant.id}>
            <label className="form-label" htmlFor={""}>
              {`${variant.inventory.available_to_sell} Left`}
            </label>
            <Field
              type="number"
              id={variant.id}
              // Use template literals instead of string concatenation
              name={`${bigcommerce_id},${variant.id},${variant.sku
                },${price.value.toString().replace(".", "-")}`}
              placeholder="0"
              // Use ternary operator instead of conditional statement
              className={
                variant?.inventory?.available_to_sell === 0 ||
                !variant?.is_purchasable
                  ? "table-cell-disable"
                  : ""
              }
              onWheel={(e) => e.target.blur()}
              onFocus={() => {
                setPriceSku(() => ({
                  sku: variant.sku,
                  base_price: variant.price.value,
                  sale_price: variant.sale_price.value,
                  retail_price: variant.retail_price.value,
                  variantId: variant?.id,
                }));

                if (variant?.["inventory"]?.["available_to_sell"] === 0) {
                  setRowId(row_index);
                } else {
                  setRowId(null);
                }
              }}
              // Use arrow function instead of calling a named function with arguments
              onBlur={(e) =>
                validateInputBox(
                  e.target.value,
                  min_purchase_qty,
                  max_purchase_qty,
                  variant.inventory.available_to_sell,
                  toastMessage,
                  // Use template literals instead of string concatenation
                  `${bigcommerce_id},${variant.id},${variant.sku},${price
                    .toString()
                    .replace(".", "-")}`,
                  formik
                )
              }
              disabled={!variant?.is_purchasable}
            ></Field>
            {rowId === row_index && priceSku?.variantId ? (
              <InStockNotify
                productId={bigcommerce_id}
                variantId={priceSku.variantId}
              />
            ) : null}
          </div>
        );
      })}
    </div>
  );
}

SingleHorizontalGrid.propTypes = {
  headings: PropTypes.array.isRequired,
  variants: PropTypes.array.isRequired,
  bigcommerce_id: PropTypes.number.isRequired,
  min_purchase_qty: PropTypes.number.isRequired,
  max_purchase_qty: PropTypes.number.isRequired,
  formik: PropTypes.object.isRequired,
};

// Use React.memo to memoize component and prevent unnecessary re-renders
export default React.memo(SingleHorizontalGrid);

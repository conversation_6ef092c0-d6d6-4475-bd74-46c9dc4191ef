import React, { useState, useContext } from "react";
import PropTypes from "prop-types";
import { Field } from "formik";

import {
  getCurrentVariantData,
  validateInputBox,
} from "../../utils/productGrid";
import useToastify from "../../hooks/ui/useToastify";
import { PriceSkuContext } from "../../context/PriceSkuContext";
import InStockNotify from "./InStockNotify";
import { getMinimumPrice } from "../../utils/money";

// Component to render a two-dimensional grid with input boxes for user input
function TwoDimentionalGrid({
  headings, // Array of objects representing column headings for the grid
  row_titles, // Array of objects representing row titles for the grid
  variants, // Array of objects representing product variants for each cell of the grid
  bigcommerce_id, // The ID of the BigCommerce product associated with the grid
  min_purchase_qty, // The minimum quantity of the product that can be purchased
  max_purchase_qty, // The maximum quantity of the product that can be purchased
  formik, // Formik object for form handling
}) {
  const { toastMessage } = useToastify(); // Hook to display toast messages to the user
  const { setPriceSku, priceSku } = useContext(PriceSkuContext); // Context to store the selected variant's price and SKU
  const [rowId, setRowId] = useState(null);

  // Render the grid
  return (
    <>
      {/* Map over the row titles to create the rows */}
      {row_titles.map((r_title, row_index) => (
        <div className="pog__row-item" key={`${r_title["id"]}`}>
          {/* Render the row title */}
          <div className="table-cell table-cell-first">{r_title["label"]}</div>
          {/* Map over the column headings to create the columns */}
          {headings.map((head) => {
            // Get the variant data for the current cell
            let variant = getCurrentVariantData(head, r_title, variants);           
            const price = getMinimumPrice(
              variant?.["price"]["value"],
              variant?.["sale_price"]["value"],
              variant?.["retail_price"]["value"]
            );

            // Render the input box for the current cell
            return (
              <div
                className="table-cell table-cell-input"
                key={variant?.["id"]}
              >
                {/* Render the label for the input box */}
                <label className="form-label" htmlFor={""}>
                  {/* If the variant exists, display the quantity available for purchase */}
                  {variant
                    ? `${
                        getCurrentVariantData(head, r_title, variants)?.[
                          "inventory"
                        ]["available_to_sell"]
                      } Left`
                    : "N/A"}
                </label>
                {/* Render the input box */}
                <Field
                  type={"number"}
                  id={variant?.["id"]}
                  // Name of the input box includes the BigCommerce product ID, variant ID, SKU, and price
                  name={`${bigcommerce_id},${variant?.["id"]},${
                    variant?.["sku"]
                  },${price.toString().replace(".", "-")}`}
                  placeholder={"0"}
                  // Disable the input box if the product is out of stock
                  className={
                    variant?.["inventory"]?.["available_to_sell"] === 0 ||
                    !variant?.is_purchasable
                      ? `table-cell-disable`
                      : null
                  }
                  // Disable the input box scrolling
                  onWheel={(e) => e.target.blur()}
                  // Set the price and SKU context when the input box is focused
                  onFocus={() => {
                    setPriceSku(() => {
                      return {
                        sku: variant["sku"],
                        base_price: variant["price"]["value"],
                        sale_price: variant["sale_price"]["value"],
                        retail_price: variant["retail_price"]["value"],
                        variantId: variant["id"],
                      };
                    });

                    if (variant?.["inventory"]?.["available_to_sell"] === 0) {
                      setRowId(row_index);
                    } else {
                      setRowId(null);
                    }
                  }}
                  // Validate the input box when it loses focus
                  onBlur={(e) =>
                    validateInputBox(
                      e.target.value,
                      min_purchase_qty,
                      max_purchase_qty,
                      variant["inventory"]["available_to_sell"],
                      toastMessage,
                      `${bigcommerce_id},${variant["id"]},${
                        variant["sku"]
                      },${price.toString().replace(".", "-")}`,
                      formik
                    )
                  }
                  disabled={!variant?.is_purchasable}
                ></Field>
              </div>
            );
          })}
          {rowId === row_index && priceSku?.variantId ? (
            <InStockNotify
              productId={bigcommerce_id}
              variantId={priceSku.variantId}
              setRowId={setRowId}
            />
          ) : null}
        </div>
      ))}
    </>
  );
}

TwoDimentionalGrid.propTypes = {
  headings: PropTypes.array,
  row_titles: PropTypes.array,
  variants: PropTypes.array,
  bigcommerce_id: PropTypes.number,
  min_purchase_qty: PropTypes.number,
  max_purchase_qty: PropTypes.number,
  formik: PropTypes.object,
};

export default React.memo(TwoDimentionalGrid);

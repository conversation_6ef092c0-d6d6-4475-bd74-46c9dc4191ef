import React, { useEffect, useState } from "react";
import { validateInputBox } from "../../utils/productGrid";
import useToastify from "../../hooks/ui/useToastify";
import { getMinimumPrice, isPromoProduct } from "../../utils/money";
import { Field } from "formik";

function ProductQuantities({
  data,
  formik,
  bigcommerce_id,
  min_purchase_qty,
  max_purchase_qty,
}) {
  const { toastMessage } = useToastify();
  const availableInventory =
    data?.["variants"]?.[0]?.["inventory"]?.["available_to_sell"] || 0;
  const variant = data?.["variants"]?.[0];
  const price = getMinimumPrice(
    variant?.["price"]["value"],
    variant?.["sale_price"]["value"],
    variant?.["retail_price"]["value"]
  );
  const minPurchaseQty = min_purchase_qty || 1;
  const [quantity, setQuantity] = useState(minPurchaseQty);

  const name = Object.keys(formik.values)[0];

  useEffect(() => {
    if (quantity !== 0) {
      formik.setFieldValue(name, quantity);
    }
  }, [quantity, name]);

  const handleQuantityChange = async (event, value) => {
    if (quantity >= 1) {
      if (quantity === 1 && value === 1) {
        setQuantity(quantity + value);
      }
      setQuantity(quantity + value);
    }
    event.preventDefault();
  };

  const validateTheValue = (inputValue) => {
    validateInputBox(
      inputValue,
      min_purchase_qty,
      max_purchase_qty,
      availableInventory,
      toastMessage,
      name,
      formik
    );
    if (quantity > availableInventory) {
      setQuantity(availableInventory);
      formik.setFieldValue(name, availableInventory);
    } else if (quantity <= 0) {
      setQuantity(1);
    }
  };

  return (
    <>
      <div className="productView-info-item">
        <span className="productView-info-name">Current Stock:</span>
        <span className="productView-info-value">{`${availableInventory}`}</span>
      </div>
      {availableInventory ? (
        <div className="productView-info-item product-qty-box">
          <span className="productView-info-name">Quantity:</span>
          <div className="form-increment">
            <button
              className="button"
              onClick={(e) => handleQuantityChange(e, -1)}
              disabled={quantity === minPurchaseQty || isPromoProduct(variant?.["price"]?.["value"])}
            >
              -
            </button>
            <Field
              type="number"
              id={variant.id}
              name={name}
              onWheel={(e) => e.target.blur()}
              value={quantity}
              onChange={(e) => setQuantity(+e.target.value)}
              className="form-input qty-input"
              onBlur={(e) => {
                e.preventDefault();
                validateTheValue(e.target.value);
              }}
              disabled={isPromoProduct(variant?.["price"]?.["value"])}
            />
            <button
              className="button"
              onClick={(e) => handleQuantityChange(e, 1)}
              disabled={quantity === availableInventory || isPromoProduct(variant?.["price"]?.["value"])}
            >
              +
            </button>
          </div>
        </div>
      ) : (
        <div className="productView-info-item product-qty-box"></div>
      )}
    </>
  );
}

export default React.memo(ProductQuantities);

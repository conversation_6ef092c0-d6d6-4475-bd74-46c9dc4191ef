import React from "react";
import useNotify from "../../hooks/products/useNotify";
import { useSelector } from "react-redux";
import InStockNotifyIcon from "../../assets/images/icon-instocknotify.png";
import Image from "../../components/common/Image";

function InStockNotify({ productId, variantId, setRowId }) {
  const { user } = useSelector((state) => state.auth);
  const { callInStockNotify } = useNotify();

  return (
    <div id="instocknotify-container" className="instocknotify-wrapper">
      <div className="instocknotify-form flex flex-wrap">
        <div className="instocknotify-icon">
          <Image src={InStockNotifyIcon} alt="InStockNotifyIcon" />
        </div>
        <div className="instocknotify-message">
          <p>
            Enter your email address to be notified when this item is back in
            stock.
          </p>
        </div>
        <div className="instocknotify-form-inner">
          <input
            disable={true}
            value={user["customer_email"]}
            className="form-input"
          ></input>
          <span
            className="button"
            onClick={async () => {
              const data = {
                email: user["customer_email"],
                productId: productId,
                variantId: variantId,
              };

              await callInStockNotify(data);
              setRowId(null);
            }}
          >
            Notify Me
          </span>
        </div>
      </div>
    </div>
  );
}

export default InStockNotify;

import React from "react";
import { Link } from "gatsby";
import data from "../../data/headerData.json";

const HeaderNavigation = () => {
  let level = 1;
  function renderMenuOptions(node) {
    return node.map((child) => {
      // If current node has one or more childrens.
      if (
        child.children &&
        Array.isArray(child.children) &&
        child.children.length > 0
      ) {
        // Level count for dynamic class.
        level = level + 1;

        return (
          <li className="has-dropdown has-arrow" key={child.link}>
            <Link to={`${child.link}`}>{child.label}</Link>
            <ul className={`dropdown-list`}>
              {renderMenuOptions(child.children)}
            </ul>
          </li>
        );
      }
      // If current node has not any children.
      else {
        level = 1; // Reset level count to 1.
        return (
          <li key={child.link}>
            <Link to={`${child.link}`}>{child.label}</Link>
          </li>
        );
      }
    });
  }

  return (
    <nav>
      <ul className="flex vertical-middle align-center flex-wrap nav-primary-list">
        {renderMenuOptions(data)}
      </ul>
    </nav>
  );
};

export default HeaderNavigation;

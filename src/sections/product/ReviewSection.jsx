import React, { useEffect } from "react";
import PropTypes from "prop-types";
import Review from "../../components/products/Review";
import ReviewModal from "../../components/form/Dialog/ReviewModal";
import useGetProductReview from "../../hooks/review/useGetProductReview";
import ReactModal from "../../components/form/Dialog/NewModal";
import useToggle from "../../hooks/useToggle";
import { scrollTo } from "../../utils/common";
import useCustomerGroupRestriction from "../../hooks/customer/useCustomerGroupRestriction";
import useReviewModal from "../../hooks/products/useShowReviewModal";

function ReviewSection({
  reviews_rating_sum = 0,
  images,
  bigcommerce_id,
  name,
  reviewSectionRef,
  setActivateReview,
  reviewHandler,
}) {
  const { closeReviewModal } = useReviewModal()
  const modalID = sessionStorage.getItem('reviewModal')

  const [isOpen, toggleDialog] = useToggle(false);
  const { isCustomerRestricted } = useCustomerGroupRestriction()

  const { data: reviewData } = useGetProductReview(bigcommerce_id);

  const approvedReview = reviewData?.filter((val) => {
    return val.status === "approved";
  });

  const onRewiewClick = () => {
    scrollTo(reviewSectionRef);
    setActivateReview(true);
  };

  const openReviewDialogModal = () => {
    if (reviewHandler) {
      reviewHandler()
    } else {
      toggleDialog();
    }
  };

  useEffect(() => {
    if (modalID) {
      if (+modalID === bigcommerce_id) {
        toggleDialog(true)
      }
    }
  }, [modalID])

  const handleToggleDialog = () => {
    if (closeReviewModal) {
      closeReviewModal()
    }
    toggleDialog()
  }

  return (
    <>
      <div className="review-link-block flex flex-wrap vertical-middle">
        <Review reviews_rating_sum={reviews_rating_sum} />
        {approvedReview?.length === 0 ? (
          <>
            <p className="product-review-link">{`(No reviews yet)`}</p>
          </>
        ) : approvedReview && approvedReview.length ? (
          <p
            className="product-review-link"
            onClick={onRewiewClick}
          >{` (${approvedReview.length} reviews) `}</p>
        ) : null}
        {!isCustomerRestricted ? (
          <div className="review-text-wrapper">
            <div className="review-text" onClick={() => openReviewDialogModal()}>
              Write a Review
            </div>
          </div>
        ) : null}
      </div>

      {isOpen && (
        <ReactModal
          isOpen={isOpen}
          setIsOpen={() => handleToggleDialog()}
          title={"Write a Review"}
        >
          <ReviewModal
            onClose={toggleDialog}
            images={images}
            productid={bigcommerce_id}
            name={name}
          />
        </ReactModal>
      )}
    </>
  );
}

ReviewSection.propTypes = {
  reviewsCount: PropTypes.number,
  reviews_rating_sum: PropTypes.number,
};

export default ReviewSection;

import React from "react";
import { <PERSON> } from "gatsby";

import IconYoutube from "../../assets/icon_js/IconYoutube";
import IconPinterest from "../../assets/icon_js/IconPinterest";
import IconLinkedin from "../../assets/icon_js/IconLinkedin";
import IconInstagram from "../../assets/icon_js/IconInstagram";
import IconFacebook from "../../assets/icon_js/IconFacebook";

export default () => (
  <ul className="social-icon-list flex align-middle">
    <li>
      <Link to="/" className="icon-youtube">
        <IconYoutube />
      </Link>
    </li>
    <li>
      <Link to="/" className="icon-pinterest">
        <IconPinterest />
      </Link>
    </li>
    <li>
      <Link to="/" className="icon-linkedin">
        <IconLinkedin />
      </Link>
    </li>
    <li>
      <Link to="/" className="icon-instagram">
        <IconInstagram />
      </Link>
    </li>
    <li>
      <Link to="/" className="icon-facebook">
        <IconFacebook />
      </Link>
    </li>
  </ul>
);

import React from 'react'
import { Link } from 'gatsby'
import { StaticImage } from 'gatsby-plugin-image'

function FullWidthImageBanner() {
  return (
    <>
      <div className="full-width-banner-section page-block">
        <div className="container">
          <div className="full-width-banner-item hide-mobile">
            <Link to={'/'}>
              <StaticImage
                src={
                  'https://cdn11.bigcommerce.com/s-964anr/images/stencil/original/image-manager/phun-disposable-ecigs-01-23-2.jpg'
                }
                alt="slider 01"
                placeholder="blurred"
                className="img-container"
              />
            </Link>
          </div>

          <div className="full-width-banner-item hide-mobile">
            <Link to={'/'}>
              <StaticImage
                src={
                  'https://cdn11.bigcommerce.com/s-964anr/images/stencil/original/image-manager/flum-disposable-ecigs-appreciation-deal-1655x57.jpg'
                }
                alt="slider 01"
                placeholder="blurred"
                className="img-container"
              />
            </Link>
          </div>

          <div className="full-width-banner-item show-mobile">
            <Link to={'/'}>
              <StaticImage
                src={
                  'https://cdn11.bigcommerce.com/s-964anr/images/stencil/original/image-manager/block-bar-limited-disposable-ecigs-03-21-23-mobile.jpg'
                }
                alt="slider 01"
                placeholder="blurred"
                className="img-container"
              />
            </Link>
          </div>

          <div className="full-width-banner-item show-mobile">
            <Link to={'/'}>
              <StaticImage
                src={
                  'https://cdn11.bigcommerce.com/s-964anr/images/stencil/original/image-manager/flum-disposable-ecigs-appreciation-deal-750x70.jpg'
                }
                alt="slider 01"
                placeholder="blurred"
                className="img-container"
              />
            </Link>
          </div>
        </div>
      </div>
    </>
  )
}

export default FullWidthImageBanner

import React from "react";

// slick slider
import Slider from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import { carousel_slider_settings } from "../../../config/slider";
import { StaticImage } from "gatsby-plugin-image";

function HeroCarousel() {
  return (
    <div className="hero-carousel-section">
      <Slider {...carousel_slider_settings} className={"slider-wrapper"}>
        <div className="hero-carousel-item">
          <div className="hero-crousel-image">
            <StaticImage
              src={"https://cdn11.bigcommerce.com/s-964anr/images/stencil/original/carousel/2071/Tyson-Hevyweigh-and-Bites-dropping-soon-Banner-1020-x-392.jpg"}
              alt="carousel slider"
              placeholder="blurred"
            />
          </div>
        </div>

        <div className="hero-carousel-item">
          <div className="hero-crousel-image">
            <StaticImage
              src={"https://cdn11.bigcommerce.com/s-964anr/images/stencil/original/carousel/2067/Innevape_the_berg_berry_eliquid_100ml_COMING_SOON.jpg"}
              alt="slider 02"
              placeholder="blurred"
            />
          </div>
        </div>

        <div className="hero-carousel-item">
          <div className="hero-crousel-image">
            <StaticImage
              src={"https://cdn11.bigcommerce.com/s-964anr/images/stencil/original/carousel/2069/fruitia_esco_bars_Icy_mint_diposable_ecigs.jpg"}
              alt="slider 02"
              placeholder="blurred"
            />
          </div>
        </div>
      </Slider>
    </div>
  );
}

export default HeroCarousel;

import React from "react";
import HeroCarousel from "../sections/home-page/HomeCarouselSlider";
// import CategoryBanners from "../sections/home-page/CategoryBanners";
import AboutContent from "../sections/home-page/AboutContent";
import FlavourBanner from "../sections/home-page/FlavourBanner";
import FullWidthImageBanner from "../sections/home-page/FullWidhtImageBanner";
import FiveColumnBanner from "../sections/home-page/FiveColumnBanner";
import TopTenBanner from "../sections/home-page/top-ten-banner";
import BrandLogoSlider from "../sections/home-page/BrandLogoSlider";
import Layout from "../layout/Layout";
// import PageBanner from "../components/banner/PageBanner";
import useGetFeaturedProducts from "../hooks/products/useGetFeaturedProducts";
import useGetNewProducts from "../hooks/products/useGetNewProducts";
import useGetPopularProducts from "../hooks/products/useGetPopularProducts";
import ProductsList from "../sections/product/ProductsList";
import FourColumnBanner from "../sections/home-page/FourColumnBanner";
import useGetAllBanners from "../hooks/banners/useGetAllBanners";
import { StaticImage } from "gatsby-plugin-image";

const IndexPage = () => {
  const { featuredProducts } = useGetFeaturedProducts();
  const { newProducts } = useGetNewProducts();
  const { popularProducts } = useGetPopularProducts();
  const { top_banner, bottom_banner } = useGetAllBanners("home_page");

  return (
    <>
      {/* herocarousel */}

      <div className="home-hero-banner-section page-block">
        <div className="container">
          <div className="row flex align-self-start flex-wrap">
            <div className="col hero-banner-col">
              <HeroCarousel />
            </div>

            <div className="col hero-small-banner-col">
              <div className="row flex flex-wrap">
                <div className="col banner-1">
                  <StaticImage
                    src={"../img/banner-images/banner-col-1.jpg"}
                    alt=""
                    placeholder="blurred"
                    width={300}
                    height={188}
                  />
                </div>
                <div className="col banner-2">
                  <StaticImage
                    src={"../img/banner-images/banner-col-2.jpg"}
                    alt=""
                    placeholder="blurred"
                    width={300}
                    height={188}
                  />
                </div>
                <div className="col banner-3">
                  <StaticImage
                    src={"../img/banner-images/banner-col-3.jpg"}
                    alt=""
                    placeholder="blurred"
                    width={300}
                    height={188}
                  />
                </div>
                <div className="col banner-4">
                  <StaticImage
                    src={"../img/banner-images/banner-col-4.jpg"}
                    alt=""
                    placeholder="blurred"
                    width={300}
                    height={188}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="banner">
        <div className="container">
          <span
            dangerouslySetInnerHTML={{
              __html: top_banner,
            }}
          />
        </div>
      </div>

      <TopTenBanner />

      <FullWidthImageBanner />

      <BrandLogoSlider />

      {/* category banners */}
      {/* <CategoryBanners /> */}

      {/* featured products */}
      <ProductsList
        title={"Featured Products"}
        subTitle={"Featured of the Month"}
        products={featuredProducts}
        disableBg={true}
      />

      <FourColumnBanner />

      {/* popular products */}
      <ProductsList
        title={"Most Popular products"}
        subTitle={"Popular products of the Month"}
        products={popularProducts}
        disableBg={true}
      />

      {/* <PageBanner /> */}

      <FiveColumnBanner />

      {/* new products */}
      <ProductsList
        title={"New Products"}
        subTitle={"Hot release of the month"}
        products={newProducts}
        disableBg={true}
      />

      <FlavourBanner />

      <AboutContent />

      <div className="banner">
        <div className="container">
          <span
            dangerouslySetInnerHTML={{
              __html: bottom_banner,
            }}
          />
        </div>
      </div>
    </>
  );
};

export const Head = ({ pageContext }) => (
  <>
    <title>{pageContext.pageTitle}</title>
    <meta name="description" content={pageContext.metaDescription} />
  </>
);

IndexPage.Layout = Layout
export default IndexPage;

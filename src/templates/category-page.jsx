import React, { useState, useRef, useEffect } from "react";
import { navigate } from "gatsby";
import Layout from "../layout/Layout";
import PropTypes from "prop-types";
import useGetAllBanners from "../hooks/banners/useGetAllBanners";
// import useGetSubCategoryByParentId from "../hooks/categories/useGetCategoryById";
import useSearch from "../hooks/searchspring/useSearch";
import Breadcrumbs from "../components/common/breadcrumbs/Breadcrumbs";
import ProductFilters from "../components/ProductFilters";
import ProductSortFilter from "../components/searchSpring/ProductSortFilter";
import ProductListing from "../components/products/ProductListing";
import Banner from "../components/banner/Banner";
import Pagination from "../components/common/Pagination";
import SidebarList from "../components/category/SidebarList";
import { SearchContext } from "../context/SearchContext";
// import useRevalidateUserSession from "../hooks/useRevalidateUserSession";
import { useSelector } from "react-redux";
import { isUserLoggedIn } from "../utils/auth";
import { intersection } from "../utils/functions";

const CategoryPageTemplate = ({ location, pageContext }) => {
  const { currentLoginUser } = useSelector((state) => state.customer);
  const user = isUserLoggedIn();

  const {
    categoryId,
    name: category_name,
    description: category_description,
    // imageUrl: category_image,
  } = pageContext;

  useEffect(() => {
    // if user is not authorized to view category
    if (
      intersection(currentLoginUser?.category_access?.categories || [], [categoryId])
        .length === 0 &&
      currentLoginUser?.category_access?.type === "specific"
    ) {
      navigate("/403");
    }
  }, [categoryId, currentLoginUser?.category_access]);

  const pageWrapperRef = useRef(null);

  const { top_banner, bottom_banner } = useGetAllBanners(
    "category_page",
    categoryId
  );

  // const { subCategories } = useGetSubCategoryByParentId(categoryId);
  const [view, setProductView] = useState("grid");

  const categoryPageFilter = {
    [`bgfilter.categories_hierarchy`]: category_name,
  };

  const {
    filters,
    products,
    sortingOptions,
    isLoading,
    pagination,
    breadcrumbs,
    facets,
    filterSummary,
    handlePageChange,
    applyFilter,
    handlePriceChange,
    onPriceReset,
    onClearAllFilter,
    handleSortingOptionChange,
  } = useSearch(`categories_hierarchy`, categoryPageFilter, pageWrapperRef);

  const { breadcrumbTitle } = breadcrumbs;
  const { currentPage, totalPages } = pagination;

  const contextValues = {
    filters,
    isLoading,
    products,
    facets,
    view,
    filterSummary,
    sortingOptions,
    setProductView,
    applyFilter,
    handlePriceChange,
    onPriceReset,
    onClearAllFilter,
    handleSortingOptionChange,
  };

  const CategoyToggle = () => {
    const bodyClass = document.body.classList;

    if (bodyClass.contains("category-toggle-active")) {
      bodyClass.remove("category-toggle-active");
    } else {
      document.body.classList.add("category-toggle-active");
    }
  };

  return (
    <SearchContext.Provider value={contextValues}>
      <div className="page-wrapper" ref={pageWrapperRef}>
        <div className="container">
          <Breadcrumbs location={location} title={breadcrumbTitle} />
        </div>

        <div className="container">
          <div className="row flex flex-wrap page-layout">
            <div className="mobile-title-col show-mobile">
              <h3 className="page-title page-title-mobile">{category_name}</h3>
            </div>

            {user ? (
              <>
                <button
                  className="mobile-sidebar-toggle show-mobile"
                  onClick={() => CategoyToggle()}
                >
                  <span className="show-filter">Show Filter</span>
                  <span className="hide-filter">Hide Filter</span>
                </button>
              </>
            ) : null}

            <div className="col page-sidebar page-sidebar-toggle page-has-filter">
              <div className="page-sidebar-inner-wrap">
                {user ? <ProductFilters /> : null}
                <SidebarList />
              </div>
            </div>

            <div className="col page-content">
              {top_banner ? (
                <>
                  <div className="page-banner-section top-banner">
                    <Banner banner={top_banner} />
                  </div>
                </>
              ) : (
                <></>
              )}

              {category_description ? (
                <>
                  <div className="page-banner-section category-description-section">
                    <Banner banner={category_description} />
                  </div>
                </>
              ) : (
                <></>
              )}

              <div className="page-action-bar flex justify-space vertical-middle">
                <h1 className="page-title hide-mobile">{category_name}</h1>

                <Pagination
                  className="pagination-bar"
                  currentPage={currentPage}
                  totalCount={totalPages}
                  onPageChange={(page) => handlePageChange(page)}
                />

                <div className="flex vertical-middle">
                  <ProductSortFilter />
                </div>
              </div>

              <ProductListing />

              <Pagination
                className="pagination-bar"
                currentPage={currentPage}
                totalCount={totalPages}
                onPageChange={(page) => handlePageChange(page)}
              />

              {bottom_banner ? (
                <>
                  <div className="page-banner-section bottom-banner">
                    <Banner banner={bottom_banner} />
                  </div>
                </>
              ) : (
                <></>
              )}
            </div>
          </div>
        </div>
      </div>
    </SearchContext.Provider>
  );
};

CategoryPageTemplate.propTypes = {
  location: PropTypes.object,
  pageContext: PropTypes.object,
};

function CategoryDetails({ location, pageContext }) {
  return (
    <CategoryPageTemplate location={location} pageContext={pageContext} />
  );
}

CategoryDetails.propTypes = {
  location: PropTypes.object,
  pageContext: PropTypes.object,
};

export const Head = ({ pageContext }) => {
  return (
    <>
      <link rel='canonical' href={`${process.env.GATSBY_STOREFRONT_URL}${pageContext.categoryUrl}`} />
      <title>{pageContext.pageTitle}</title>
      <meta name="description" content={pageContext.metaDescription} />
    </>
  );
};

CategoryDetails.Layout = Layout
export default CategoryDetails;

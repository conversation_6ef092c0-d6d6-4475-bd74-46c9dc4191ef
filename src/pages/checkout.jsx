import React, { useEffect, useState } from "react";
import useCartOperations from "../hooks/cart/useCartOperations";
import Loader from "../components/form/Loader";
import { staticPage } from "../../config/staticPage";

const Checkout = () => {
  const { refreshCheckout, checkoutLoading } = useCartOperations();
  const [iframeLoading, setIframeLoading] = useState(true);

  useEffect(() => {
    const iframe = document.getElementById("checkout-iframe");

    // ✅ 20s timeout to wait for confirmation
    const timeoutId = setTimeout(() => {
      console.warn("⏱️ No postMessage received within 20s. Reloading page...");
      window.location.reload(); // 🔁 Full page reload as fallback
    }, 20000);

    // ✅ Handle messages from BigCommerce iframe
    const handleMessage = (event) => {
      if (!event.data || typeof event.data !== "object") return;

      if (event.data.type === "checkout-status") {
        clearTimeout(timeoutId); // ✅ Cancel fallback timeout on any message

        if (event.data.status === "success") {
          console.log("✅ Checkout iframe loaded successfully.");
          setIframeLoading(false);

          // visible iframe 
          if (iframe) {
            iframe.style.display = "block";
          }
        } else if (event.data.status === "error") {
          console.warn("❌ Checkout iframe failed. Reloading page...");
          window.location.reload(); // 🔁 Reload on explicit error
        }
      }
    };

    window.addEventListener("message", handleMessage);

    // ✅ On mount: check if iframe exists
    if (!iframe) {
      console.warn("🆕 No iframe found. Likely hard refresh. Rebuilding iframe...");
      setIframeLoading(true);
      refreshCheckout(() => setIframeLoading);
    } else {
      console.log("✅ Iframe found. Waiting for confirmation...");
    }

    return () => {
      window.removeEventListener("message", handleMessage);
      // 🔥 Do NOT clear timeout unless postMessage is received
    };
  }, []);

  return (
    <>
      {(iframeLoading || checkoutLoading) && <Loader />}
    </>
  );
};

export const Head = () => {
  const page = staticPage.find((obj) => obj.name === "checkout");
  return (
    <>
      <title>{page?.title}</title>
      <meta name="description" content={page?.description} />
    </>
  );
};

export default Checkout;
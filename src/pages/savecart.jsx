import React, { useEffect } from "react";
import { useSelector } from "react-redux";
import { navigate } from "gatsby";
import useSaveCart from "../hooks/cart/useSaveCart";
import Loader from "../components/form/Loader";
import { parseQueryString } from "../utils/url";
import { staticPage } from "../../config/staticPage";
import Layout from "../layout/Layout";

function SaveCartPage({ location }) {
  const { cartId } = parseQueryString(location?.search);

  const { currentLoginUser } = useSelector((state) => state.customer);

  const { replaceBCCart, isLoading } = useSaveCart();

  useEffect(async () => {
    // if user is not logged in then redirect to login page.
    if (!currentLoginUser && cartId) {
      navigate(`/login?redirect_to=/savecart?cartId=${cartId}`);
    }

    if (cartId && currentLoginUser) {
      return await replaceBCCart(cartId);
    }
  }, []);

  return isLoading ? <Loader /> : "";
}
export const Head = () => {
  let login_nav = staticPage;
  let loginDetails = null;
  for (const obj of login_nav) {
    if (obj.name === "savecart") {
      loginDetails = obj;
      break; // Exit the loop once the desired object is found.
    }
  }
  return (
    <>
      <title>{loginDetails.title}</title>
      <meta name="description" content={loginDetails.description} />
    </>
  );
};


SaveCartPage.Layout = Layout
export default SaveCartPage;

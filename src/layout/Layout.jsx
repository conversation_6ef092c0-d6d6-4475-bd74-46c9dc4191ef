import React, { useEffect, useCallback } from "react";
import "../sass/app.scss";
import Header from "../sections/mw-header/Header";
import Footer from "../sections/mw-footer/Footer";
import { Provider, useDispatch } from "react-redux";
import { CartContext } from "../context/CartContext";
import useCartOperations from "../hooks/cart/useCartOperations";
import { PersistGate } from "redux-persist/lib/integration/react";
import store, { persistor } from "../redux/store";
import CustomBanner from "../components/common/CustomBanner";
import SubscriptionNotification from "../components/common/SubscriptionNotification";
import useGetCartSummary from "../hooks/cart/useGetCartSummary";
import { resetCart, setLineItems } from "../features/cartSlice";
import { setCurrentUser } from "../features/customerSlice";
import { navigate } from "gatsby";
// import AgeVerificationPopup from "../components/common/AgeVerificationPopup";
import useCreateReorder from "../hooks/cart/useCreateReorder";
import useCheckoutFallback from "../hooks/cart/useCheckoutFallback";
import useRevalidateUserSession from "../hooks/useRevalidateUserSession";
import Loader from "../components/form/Loader";
import useSendIpInfoApi from "../hooks/useSendIpInfoApi";
import useGetScripts from "../hooks/useGetScripts";
import FraudAlertPopup from "../components/common/FraudAlertPopup";

const ALLOWED_URLS = [
  "/login",
  "/create-account",
  "/forgot-password",
  "/reset-password",
  "/customer-verification",
  "/customer-login",
  "/create-new-account",
];
const INVALID_CUSTOMER_GROUPS = [2, 32, 72, 73, 74, 75];

const Layout = ({ children }) => {
  const { data: user, isValidating } = useRevalidateUserSession();
  useGetScripts();

  const { fetchPingPostApi } = useSendIpInfoApi();
  const { lineItemsLength } = useGetCartSummary();
  const dispatch = useDispatch();
  const { reorderProducts } = useCreateReorder();
  const { handleFallbackCheckout } =
    useCheckoutFallback();

  // Extract pathname once from children props
  const pathname = children?.props?.location?.pathname;
  const isPathAllowed = ALLOWED_URLS.some((url) => pathname?.startsWith(url));
  const isValidCustomerGroup =
    !isValidating &&
    user?.id &&
    !INVALID_CUSTOMER_GROUPS.includes(user?.customer_group_id);

  useEffect(() => {
    if (user?.id) {
      fetchPingPostApi(user?.id);
    }
  }, [user?.id]);

  // Sync current user data to Redux store
  useEffect(() => {
    if (user?.id) {
      dispatch(setCurrentUser(user));
    } else {
      dispatch(setCurrentUser(null));
    }
  }, [user]);

  // Sync cart summary to the store
  useEffect(() => {
    if (lineItemsLength !== 0) {
      dispatch(setLineItems(lineItemsLength));
    } else {
      dispatch(resetCart());
    }
  }, [lineItemsLength, dispatch]);

  // Handle navigation based on user authentication and allowed routes
  useEffect(() => {
    if (!isValidating) {
      if (!user?.id) {
        if (pathname === "/customer-verification" || !isPathAllowed) {
          navigate("/login");
        }
      } else if (
        (!isPathAllowed && !isValidCustomerGroup && user?.id) ||
        pathname === "/login"
      ) {
        navigate("/");
      }
    }
  }, [isValidating, user, pathname, isPathAllowed]);

  // Destructure cart operations
  const {
    state,
    checkoutLoading,
    setCheckoutLoadingState,
    addToCart,
    clearCart,
    removeCartItem,
    updateCartItem,
    applyCouponCode,
    removeCouponCode,
    removeMultipleCartItems,
  } = useCartOperations();
  const { isLoading: isCartOperationRunning, updatingItem, removing } = state;

  const contextValue = {
    checkoutLoading,
    isCartOperationRunning,
    updatingItem,
    lineItemsLength,
    setCheckoutLoadingState,
    addToCart,
    clearCart,
    removeCartItem,
    updateCartItem,
    applyCouponCode,
    removeCouponCode,
    removeMultipleCartItems,
    removing,
  };

  // Single event handler for external messages
  const handleMessageEvent = useCallback(
    async (e) => {
      const data = e.message ? e.message : e.data;

      if (data.from === "bc") {
        switch (data.type) {
          case "loadCheckout":
            await handleFallbackCheckout();
            break;
          case "changeHeight": {
            const iFrame = document.getElementById("my-account-page");
            if (iFrame && iFrame.style.height !== data.height) {
              iFrame.style.height = data.height;
            }
            break;
          }
          case "loadCart":
            navigate("/cart");
            break;
          case "homePageRedirect":
            navigate("/");
            break;
          case "reOrder":
            await reorderProducts(data.data);
            break;
          case "productPageRedirect":
            if (data.productURL) {
              navigate(data.productURL);
            }
            break;
          case "nyRestriction":
            if (data?.data?.length > 0 && user?.customer_id) {
              const storageKey = `${user.customer_id}`;
              const prevDataStr =
                window.parent.sessionStorage.getItem(storageKey);
              const prevData = prevDataStr ? JSON.parse(prevDataStr) : null;
              if (JSON.stringify(data.data) !== JSON.stringify(prevData)) {
                window.parent.sessionStorage.setItem(
                  storageKey,
                  JSON.stringify(data.data)
                );
              }
            }
            break;
          default:
            break;
        }
      }
    },
    [user]
  );

  // Register event listener with proper cleanup
  useEffect(() => {
    const eventMethod = window.addEventListener
      ? "addEventListener"
      : "attachEvent";
    const messageEvent =
      eventMethod === "attachEvent" ? "onmessage" : "message";

    window[eventMethod](messageEvent, handleMessageEvent, false);

    return () => {
      if (window.removeEventListener) {
        window.removeEventListener(messageEvent, handleMessageEvent, false);
      } else if (window.detachEvent) {
        window.detachEvent(messageEvent, handleMessageEvent);
      }
    };
  }, [handleMessageEvent]);

  return (
    <div
      id="layout"
      className={`${user?.id ? "user-loggedin" : "guest-user-mode"} ${user?.id ? `customer-group-${user?.customer_group_id}` : ""
        }`}
    >
      <CartContext.Provider value={contextValue}>
        <Provider store={store}>
          <PersistGate loading={null} persistor={persistor}>
            <Header />
            {!isValidating ? (
              <CustomBanner isValidCustomerGroup={isValidCustomerGroup} />
            ) : (
              <div
                style={{
                  width: "100%",
                  height: "100vh",
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  position: "relative",
                }}
              >
                <Loader />
              </div>
            )}
            <SubscriptionNotification />
            <main className="body-section">
              {(isPathAllowed || isValidCustomerGroup) && children}
            </main>
            {isValidCustomerGroup && <Footer />}
            {/* <AgeVerificationPopup /> */}
            <FraudAlertPopup />
          </PersistGate>
        </Provider>
      </CartContext.Provider>
    </div>
  );
};

export default Layout;

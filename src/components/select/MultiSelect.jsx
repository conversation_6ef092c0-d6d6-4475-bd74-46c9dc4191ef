import React, { useEffect, useState } from "react";
import { MultiSelect } from "react-multi-select-component";
import isEqual from "lodash/isEqual";

const MultipleSelect = ({
  options,
  placeholder,
  isDisabled,
  onChange,
  initialValue,
}) => {
  const [selected, setSelected] = useState(initialValue || []);

  useEffect(() => {
    if (initialValue && !isEqual(selected, initialValue)) {
      setSelected(initialValue);
    }
  }, [initialValue, selected]);

  const handleChange = (selected) => {
    setSelected(selected);
    onChange(selected);
  };

  return (
    <MultiSelect
      options={options}
      value={selected}
      placeholder={placeholder}
      onChange={handleChange}
      disabled={isDisabled}
      labelledBy={"Select Fruits"}
    />
  );
};

export default MultipleSelect;

import React, { useContext } from "react";
import ProductCard from "./ProductCard";
import SkeletonLoader from "../form/SkeletonLoader";
import { SearchContext } from "../../context/SearchContext";
import useFetchProductPrice from "../../hooks/products/useFetchProductPrice";
import { uniqBy } from "../../utils/functions";
import useFetchProductBadges from "../../hooks/products/useFetchProductBadges";

function ProductListing() {
  const { isLoading, products, view } = useContext(SearchContext);

  // Remove duplicate products by it's Id.
  const removeDuplicates = products?.length > 1 && uniqBy([...products], "uid");

  // Array of Ids.
  const productIds =
    removeDuplicates &&
    removeDuplicates.map(function (item) {
      return item["uid"];
    });

  const { prices } = useFetchProductPrice(productIds);
  const { badges } = useFetchProductBadges(productIds);

  return (
    <div
      className={`section bc-product-grid bc-product-grid--archive bc-product-grid--4col product-list ${view === "list" && "style-list-view"
        }`}
    >
      {isLoading ? (
        <>
          <SkeletonLoader />
        </>
      ) : products.length === 0 ? (
        <p>There are no products listed under this category.</p>
      ) : (
        products.map((product, index) => {
          product["image"] = product?.["imageUrl"];
          product["custom_url"] = product["url"];

          if (prices) {            
            product["price"] = prices?.[product["uid"]]?.["price"];
            product["sale_price"] = prices?.[product["uid"]]?.["salePrice"] || 0;
            product["retail_price"] =
              prices?.[product["uid"]]?.["retailPrice"];
          }
          if (badges) {
            product["badges"] = badges?.[product["uid"]];
          }
          return <ProductCard key={product.name} product={product} />;
        })
      )}
    </div>
  );
}

export default ProductListing;

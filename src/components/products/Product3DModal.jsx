import React, { Suspense, useEffect, useRef, useState } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { Environment, OrbitControls, useAnimations, useGLTF } from '@react-three/drei';
import Button from '../form/button/Button';
import * as THREE from 'three';


function VapeModel({ url, activeAnimation, isPlaying }) {
  const group = useRef()
  const { scene, materials, animations } = useGLTF(url)
  const { actions, names } = useAnimations(animations, group)


const STATIC_EMISSION_INTENSITY = 1.5


  useEffect(() => {

    if (scene) {
      scene.position.set(0, 0, 0)
      scene.scale.set(1, 1, 1)
      scene.updateMatrixWorld()
      const box = new THREE.Box3().setFromObject(scene)
      const center = box.getCenter(new THREE.Vector3())
      scene.position.sub(center)
    }

    names.forEach((name) => {
      if (name === activeAnimation) {
        actions[name].reset().fadeIn(0.5).play()
      } else {
        actions[name].fadeOut(0.5)
      }
    })
  }, [actions, names, activeAnimation, scene])

  useFrame(() => {
    if (group.current && isPlaying) {
      group.current.rotation.y += 0.005
    }
    Object.values(materials).forEach((material) => {
      if (material.emissive) {
        material.emissiveIntensity = STATIC_EMISSION_INTENSITY
      }
    })
  })

  return (
    <group ref={group}>
      <primitive object={scene} />
    </group>
  )
}


function Scene({ url, activeAnimation, isPlaying }) {
  return (
    <>
      <VapeModel url={url} activeAnimation={activeAnimation} isPlaying={isPlaying} />
      <OrbitControls
        enablePan={true}
        enableZoom={true}
        enableRotate={true}
        target={[0, 0, 0]}
      />
      <Environment preset="city" />
    </>
  )
}

function Controls({ names, activeAnimation, setActiveAnimation, isPlaying, setIsPlaying }) {
  const controlsStyle = {
    position: 'absolute',
    bottom: '1rem',
    right: '1rem',
    color: 'white',
    borderRadius: '0.5rem',
  }

  const buttonContainerStyle = {
    display: 'flex',
    flexWrap: 'wrap',
    gap: '0.5rem',
    justifyContent: 'flex-end',
  }

  const buttonStyle = {
    fontSize: '0.875rem',
  }

  return (
    <div style={controlsStyle}>
      <div style={buttonContainerStyle}>
        {names.map((name) => (
          <Button
            key={name}
            onClick={() => {
              if (activeAnimation === name) {
                setActiveAnimation(null)
                setIsPlaying(false)
              } else {
                setActiveAnimation(name)
                setIsPlaying(true)
              }
            }}
            variant={activeAnimation === name ? "secondary" : "primary"}
            style={buttonStyle}
          >
            {activeAnimation === name ? 'Stop' : 'Play'}
          </Button>
        ))}
      </div>
    </div>
  )
}


const Renderer = ({ modelUrl }) => {
  const url = modelUrl || ''
  const [activeAnimation, setActiveAnimation] = useState(null)
  const [isPlaying, setIsPlaying] = useState(false)
  const { animations } = useGLTF(url)

  return (
    <div style={{ position: 'relative', width: '100%', height: '100%' }}>
      <Canvas
        camera={{
          position: [0.1, 0.1, -0.2],
          fov: 50,
          near: 0.1,
          far: 1000

        }}
        style={{ width: '100%', height: '100%' }}
      >
        <ambientLight intensity={1.5} />
        <spotLight
          position={[0, 0, 4]}
          angle={0.5}
          penumbra={0.8}
          intensity={1.5}
          castShadow={false}
        />
        <spotLight
          position={[4, 4, 4]}
          angle={0.3}
          penumbra={1}
          intensity={1.6}
          castShadow
          shadow-mapSize-width={2048}
          shadow-mapSize-height={2048}
        />
        <spotLight
          position={[-4, 2, -4]}
          angle={0.4}
          penumbra={1}
          intensity={1.5}
          castShadow={false}
        />
        <pointLight
          position={[0, 3, -4]}
          intensity={1.5}
        />
        <pointLight
          position={[0, -3, 0]}
          intensity={1.5}
        />
        <pointLight
          position={[-2, 0, 3]}
          intensity={1.5}
        />
        <pointLight
          position={[2, 0, 3]}
          intensity={1.5}
        />
        <Scene url={url} activeAnimation={activeAnimation} isPlaying={isPlaying} />
      </Canvas>
      <Controls
        names={animations.map(a => a.name)}
        activeAnimation={activeAnimation}
        setActiveAnimation={setActiveAnimation}
        isPlaying={isPlaying}
        setIsPlaying={setIsPlaying}
      />
    </div>
  );
};

const Product3DModal = ({ modelUrl }) => {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <Renderer modelUrl={modelUrl} />
    </Suspense>
  )
}

export default Product3DModal;
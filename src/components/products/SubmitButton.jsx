import React, { useState, useEffect } from "react";
import PropTypes from "prop-types";
import ButtonLoader from "../form/ButtonLoader";
import useCustomerGroupRestriction from "../../hooks/customer/useCustomerGroupRestriction";
import LoginModal from "../form/Dialog/LoginModal";
import { isProductAvailable } from "../../utils/product";

function SubmitButton({ isUser, isLoading, isdisable = false, availability, is_no_grid, quantity_to_sell }) {
  const [isOpen, setIsOpen] = useState(false);
  const { isCustomerRestricted } = useCustomerGroupRestriction();
  const [isNewDisabled, setIsNewDisabled] = useState(isdisable)
  const [isNewAvailable, setIsNewAvailable] = useState(isProductAvailable(availability))

  useEffect(() => {
    if (is_no_grid) {
      if (quantity_to_sell === 0) {
        setIsNewDisabled(true)
        setIsNewAvailable(false)
      }
    }

  }, [isdisable, availability, is_no_grid, quantity_to_sell]);

  return (
    <div className="bc-product-card table-option-grid">
      <div className="bc-product__actions" data-js="bc-product-group-actions">
        <div className="bc-form bc-product-form">
          <div className="bc-product-form__product-message"></div>
          {(
            isUser ? (
              !isCustomerRestricted ? (
                isLoading ? (
                  <ButtonLoader />
                ) : (
                  isNewAvailable ?
                    <button
                      className="bc-btn bc-btn--form-submit bc-btn--add_to_cart button button-large text-uppercase button-addtocart"
                      type="submit"
                      disabled={isNewDisabled}
                    >
                      {"Add to Cart"}
                    </button> : (
                      <button className="button out-of-stock-btn" disabled>
                        Out of stock
                      </button>
                    )
                )
              ) : null
            ) : (
              <button
                className="bc-btn bc-btn--form-submit bc-btn--add_to_cart button button-large text-uppercase button-addtocart"
                onClick={() => setIsOpen(true)}
              >
                {"Sign in"}
              </button>
            )
          )}
        </div>
      </div>
      {isOpen && (
        <LoginModal isOpen={isOpen} setIsOpen={setIsOpen} title={"Login"} />
      )}
    </div>
  );
}

SubmitButton.propTypes = {
  isUser: PropTypes.bool,
  isLoading: PropTypes.bool,
  isdisable: PropTypes.bool,
  availability: PropTypes.string,
};

export default SubmitButton;

import React, { useContext, useEffect, useState } from "react";
import { Form, Formik } from "formik";
import PropTypes from "prop-types";
import { dataToLineItems, initialValues } from "../../utils/productGrid";
import { CartContext } from "../../context/CartContext";
import { isUserLoggedIn } from "../../utils/auth";
import ProductGrid from "../../sections/product-grid/ProductGrid";
import ProductQuantities from "../../sections/product-grid/ProductQuantities";
import SubmitButton from "./SubmitButton";
import useToastify from "../../hooks/ui/useToastify";
import {
  LINE_ITEM_FIXED_LIMIT,
  LINE_ITEM_THRESOLD_LIMIT,
  LINE_ITEM_THRESOLD_LIMIT_MESSAGE,
  LINE_ITEM_WARNING_MESSAGE,
} from "../../utils/common";
import CancleIcon from "/src/assets/svg/cancle_icon.svg";
import BasicModal from "../form/Dialog/BasicModal";

function OptionForm({
  data,
  bigcommerce_id,
  max_purchase_qty,
  min_purchase_qty,
  isLoading,
  lineItemsLength,
  availability
}) {
  const isUser = isUserLoggedIn();
  const { toastMessage } = useToastify();
  const [response, setResponse] = useState();
  const { addToCart, isCartOperationRunning } = useContext(CartContext);

  const is_variant_not_available =
    data?.headings?.length === 0 && data?.row_titles?.length === 0
      ? true
      : false;
  const total_available_inventory =
    data?.variants?.[0]?.inventory?.available_to_sell;

  useEffect(() => {
    if (lineItemsLength === LINE_ITEM_THRESOLD_LIMIT) {
      toastMessage("warning", LINE_ITEM_THRESOLD_LIMIT_MESSAGE);
    }
  }, [lineItemsLength]);

  const [open, setOpen] = useState(false);

  useEffect(() => {
    const errorText = "You cannot add more than 250 line items in cart.";
    const compareResult = errorText.localeCompare(response?.error, undefined, {
      sensitivity: "base",
    });
    if (compareResult === 0) {
      setOpen(true);
    }
  }, [response]);

  const onClose = () => {
    setOpen(!open);
  };

  return (
    <>
      <Formik
        initialValues={initialValues(data, bigcommerce_id, min_purchase_qty)}
        enableReinitialize={true}
        onSubmit={(values, { resetForm }) => {
          const line_items = dataToLineItems(
            values,
            min_purchase_qty,
            max_purchase_qty,
            total_available_inventory,
            toastMessage,
            is_variant_not_available
          );
          const resetGridForm = () => {
            return !is_variant_not_available
              ? resetForm(initialValues(data, bigcommerce_id, min_purchase_qty))
              : () => null;
          };
          isUser &&
            line_items &&
            addToCart(line_items, resetGridForm, setResponse);
        }}
      >
        {(formik) => (
          <Form id="select-variants">
            {is_variant_not_available ? (
              <ProductQuantities
                data={data}
                formik={formik}
                bigcommerce_id={bigcommerce_id}
                min_purchase_qty={min_purchase_qty}
                max_purchase_qty={max_purchase_qty}
              />
            ) : (
              <ProductGrid
                formik={formik}
                data={data}
                bigcommerce_id={bigcommerce_id}
                min_purchase_qty={min_purchase_qty}
                max_purchase_qty={max_purchase_qty}
                isLoading={isLoading}
              />
            )}
            <SubmitButton
              isUser={isUser}
              isLoading={isCartOperationRunning}
              isdisable={
                lineItemsLength >= LINE_ITEM_FIXED_LIMIT ? true : false
              }
              availability={availability}
              is_no_grid={is_variant_not_available}
              quantity_to_sell={total_available_inventory}
            />
          </Form>
        )}
      </Formik>
      <BasicModal
        show={open}
        onClose={onClose}
        button={"OK"}
        body={LINE_ITEM_WARNING_MESSAGE}
        icon={CancleIcon}
      />
    </>
  );
}

OptionForm.prototype = {
  data: PropTypes.array,
  bigcommerce_id: PropTypes.number,
  max_purchase_qty: PropTypes.number,
  min_purchase_qty: PropTypes.number,
  isLoading: PropTypes.bool,
  lineItemsLength: PropTypes.number,
  availability: PropTypes.string,
};

export default React.memo(OptionForm);

import React from "react";
import { formatPrice } from "../../utils/money";
import useToggle from "../../hooks/useToggle";
import ReactModal from "../form/Dialog/NewModal";
import CustomFields from "../../components/products/CustomFields";

const BulkPricingModal = ({ i }) => {
  if ("percentOff" in i) {
    return <li>{`Buy ${i.minimumQuantity} - ${i.maximumQuantity} and get ${i.percentOff}% off`}</li>
  } else if ("price" in i) {
    return <li>{`Buy ${i.minimumQuantity} - ${i.maximumQuantity} and pay only ${formatPrice(i.price)} each`}</li>
  } else if ("priceAdjustment" in i) {
    return <li key={i.id}>{`Buy ${i.minimumQuantity} - ${i.maximumQuantity} and get ${formatPrice(i.priceAdjustment)} off`}</li>
  } else {
    return <></>
  }
};

function ProductInfo({
  sku,
  upc,
  weight,
  weightUnit,
  availability,
  order_quantity_minimum,
  order_quantity_maximum,
  fixed_cost_shipping_price,
  bulk_pricing_rules,
  customFields
}) {
  const [isOpen, toggleDialog] = useToggle(false);

  return (
    <div className="productView-info-list">
      {sku ? (
        <div className="productView-info-item">
          <span className="productView-info-name">SKU:</span>
          <span className="productView-info-value">{sku}</span>
        </div>
      ) : null}

      <CustomFields customFields={customFields} />

      {upc ? (
        <div className="productView-info-item">
          <span className="productView-info-name">UPC:</span>
          <span className="productView-info-value">{upc}</span>
        </div>
      ) : null}

      {availability ? (
        <div className="productView-info-item">
          <span className="productView-info-name">Availability:</span>
          <span className="productView-info-value">{`${availability}`}</span>
        </div>
      ) : null}

      {!weight ? null : (
        <div className="productView-info-item">
          <span className="productView-info-name">Weight:</span>
          <span className="productView-info-value">{`${weight} ${
            weightUnit ? weightUnit : ""
          }`}</span>
        </div>
      )}

      {order_quantity_minimum ? (
        <div className="productView-info-item">
          <span className="productView-info-name">Minimum Purchase:</span>
          <span className="productView-info-value">
            {`${order_quantity_minimum} Units`}
          </span>
        </div>
      ) : null}

      {order_quantity_maximum ? (
        <div className="productView-info-item">
          <span className="productView-info-name">Maximum Purchase:</span>
          <span className="productView-info-value">
            {`${order_quantity_maximum} Units`}
          </span>
        </div>
      ) : null}

      {fixed_cost_shipping_price ? (
        <div className="productView-info-item">
          <span className="productView-info-name">Shipping:</span>{" "}
          <span className="productView-info-value">{`${formatPrice(
            fixed_cost_shipping_price
          )} (Fixed Shipping Cost)`}</span>
        </div>
      ) : null}

      {bulk_pricing_rules?.length ? (
        <div className="productView-info-item">
          <span className="productView-info-name">Bulk Pricing:</span>
          <span className="productView-info-value">
            {
              <span
                onClick={toggleDialog}
                className="link"
                role="button"
                tabIndex={0}
                onKeyDown={(e) => {
                  if (e.key === "Enter" || e.key === "Space") {
                    toggleDialog();
                  }
                }}
              >
                Buy in bulk and save
              </span>
            }
          </span>
        </div>
      ) : null}

      <ReactModal isOpen={isOpen} setIsOpen={toggleDialog} title={""}>
        <div>
          <p>
            Below are the available bulk discount rates for each individual item
            when you purchase a certain amount
          </p>
          <ul>
            {bulk_pricing_rules?.length ?
              bulk_pricing_rules.map((i, index) => <BulkPricingModal key={index} i={i} />) : null}
          </ul>
        </div>
      </ReactModal>

      {availability?.message ? (
        <div className="productView-info-item">
          <p className="form-error-message">{availability.message}</p>
        </div>
      ) : null}
    </div>
  );
}

export default React.memo(ProductInfo);

import { Link } from "gatsby";
import React from "react";
// import { useDispatch } from "react-redux";
// import useToastify from "../../hooks/ui/useToastify";
import useToggle from "../../hooks/useToggle";
// import { increment } from "../../redux/counter";
// import { setCompareCookie } from "../../utils/product";
import Button from "../form/button/Button";
import Price from "./Price";
import QuickView from "./QuickView";
import Review from "./Review";
// import useGetCartSummary from "../../hooks/cart/useGetCartSummary";
// import useRevalidateUserSession from "../../hooks/useRevalidateUserSession";
import { useSelector } from "react-redux";
import ReactModal from "../form/Dialog/NewModal";
import useCustomerGroupRestriction from "../../hooks/customer/useCustomerGroupRestriction";
import Image from "../common/Image";

const ProductCard = ({ product }) => {
  const isPreorder = false
  // const { toastMessage } = useToastify();
  const [isOpen, toggleDialog] = useToggle(false);
  const { lineItemsLength } = useSelector((state) => state.cart);
  const { currentLoginUser } = useSelector((state) => state.customer);
  // const dispatch = useDispatch();
  const { isCustomerRestricted } = useCustomerGroupRestriction()

  return (
    <>
      <div className="bc--featured-product-card product-item">
        <div className="product-item-inner">
          <div className="product-item-image">

            <div className="card-badges-section">
              {isPreorder ? (
                <div className="card-preorder-tag">PRE-ORDER</div>
              ) : null}
              {product?.badges?.length > 0 ? (
                <div >
                  {product?.badges?.map((badge, index) => (
                    <p><span className="card-badge" key={index} style={{ backgroundColor: badge?.bg_color, color: badge?.font_color }}>{badge?.tag_label}</span></p>
                  ))}
                </div>
              ) : null}
            </div>

            <div className="product-action-overlay">
              <div className="quickview-button">
                <Button onClick={toggleDialog}>Quick View</Button>
              </div>
              {/* <div className="card-compare-button">
                <Button
                  onClick={() => {
                    setCompareCookie(product["bigcommerce_id"], toastMessage);
                    dispatch(increment());
                  }}
                >
                  Compare
                </Button>
              </div> */}
            </div>
            <Link
              to={
                typeof product["custom_url"] === "object"
                  ? `${product?.custom_url?.url}`
                  : product?.custom_url
                    ? `${product?.custom_url}`
                    : ""
              }
              className="bc-product-card-image-anchor"
              title={product?.name}
              state={{ productId: product['id'], type: 'product' }}
            >
              <div className="bc-product-card__featured-image">
                <Image
                  className="attachment-bc-medium size-bc-medium"
                  src={product?.image || "/img/default-bc-product.png"}
                  alt={product?.name}
                />
              </div>
            </Link>
          </div>

          <div className="product-item-detail">
            <p className="hide">
              {product?.["brand_name"] ? product["brand_name"] : null}
            </p>
            <div className="bc-product__meta">
              <h3 className="bc-product__title product-item-name">
                <Link
                  to={
                    typeof product["custom_url"] === "object"
                      ? `${product?.custom_url?.url}`
                      : product?.custom_url
                        ? `${product?.custom_url}`
                        : ""
                  }
                  className="bc-product__title-link reverse-link-style"
                  title={product?.name}
                >
                  {product?.name}
                </Link>
              </h3>
              {!isCustomerRestricted ? (
                <Price
                  sale_price={product?.sale_price}
                  price={product?.price}
                  retail_price={product?.retail_price}
                  currentUser={currentLoginUser}
                />
              ) : null}
              <div className="card-review text-center">
                <Review reviews_rating_sum={product?.reviews_rating_sum ? +product?.reviews_rating_sum : 0} />
              </div>
            </div>
          </div>
        </div>
      </div>
      {isOpen && (
        <ReactModal
          isOpen={isOpen}
          setIsOpen={() => toggleDialog()}
          title={'Quick Details'}
        >
          <QuickView
            productId={product ? +product.uid || product.bigcommerce_id : null}
            lineItemsLength={lineItemsLength}
            currentUser={currentLoginUser}
          />
        </ReactModal>
      )}
    </>
  );
};

export default ProductCard;

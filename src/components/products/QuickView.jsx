import React, { useState, useEffect } from "react";
import useGetProductDetails from "../../hooks/products/useGetProductDetails";
import useGetProductOptions from "../../hooks/products/useGetProductOptions";
import ProductImages from "./ProductImages";
import Price from "./Price";
import OptionForm from "./OptionForm";
import ProductInfo from "./ProductInfo";
import ReviewSection from "../../sections/product/ReviewSection";
import Loader from "../form/Loader";
import { isUserLoggedIn } from "../../utils/auth";
import { PriceSkuContext } from "../../context/PriceSkuContext";
import {
  LINE_ITEM_FIXED_LIMIT,
  LINE_ITEM_WARNING_MESSAGE,
} from "../../utils/common";
import useCustomerGroupRestriction from "../../hooks/customer/useCustomerGroupRestriction";
import { Link, navigate } from "gatsby";
import useShowReviewModal from "../../hooks/products/useShowReviewModal";
import { dataToProductQuickViewDetails } from "../../utils/product";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faFacebook,
  faTwitter,
  faPinterest,
} from "@fortawesome/free-brands-svg-icons";
import { faEnvelope, faPrint } from "@fortawesome/free-solid-svg-icons";
import {
  EmailShareButton,
  FacebookShareButton,
  PinterestShareButton,
  TwitterShareButton,
} from "react-share";
import OtherCustomerGroupsPriceInfo from "./OtherCustomerGroupsPriceInfo";

function QuickView({ productId, lineItemsLength, currentUser }) {
  const user = isUserLoggedIn();
  const { setReviewModalVisible } = useShowReviewModal();
  const { productDetails, isLoading } = useGetProductDetails(productId, "1", user);
  const { product_variants } = useGetProductOptions(productId, user);
  const [reviewImage, setReviewImage] = useState({});
  const { isCustomerRestricted } = useCustomerGroupRestriction();
  const location = window.location.origin;
  const handlePrint = () => {
    window.print();
  };
  const {
    name,
    brandName,
    description,
    brandUrl,
    basePrice,
    retailPrice,
    salePrice,
    availability,
    sku,
    upc,
    orderQuantityMinimum,
    orderQuantityMaximum,
    weight,
    reviewCount,
    reviewRatingSum,
    bulkPricingRules,
    images,
    customFields,
    badges
  } = dataToProductQuickViewDetails(productDetails);

  const [priceSku, setPriceSku] = useState({
    sku: sku || "",
    base_price: basePrice,
    retail_price: retailPrice,
    sale_price: salePrice,
    variantId: null,
  });

  useEffect(() => {
    if (basePrice || retailPrice || salePrice || sku) {
      setPriceSku((prevState) => ({
        ...prevState,
        base_price: basePrice,
        retail_price: retailPrice,
        sale_price: salePrice,
        sku: sku,
      }));
    }
  }, [basePrice, retailPrice, salePrice, sku]);

  useEffect(() => {
    if (priceSku?.variantId !== null) {
      setPriceSku((prevState) => ({
        ...prevState,
        base_price: priceSku?.base_price,
        retail_price: priceSku?.retail_price,
        sale_price: priceSku?.sale_price,
      }));
    }
  }, [priceSku.variantId]);

  const contextValue = {
    setPriceSku,
  };

  const reviewHandler = () => {
    setReviewModalVisible(productId);
    navigate(`${productDetails.url}`);
  };

  return isLoading ? (
    <div className="quickview-modal-wrapper">
      <Loader />
    </div>
  ) : (
    <>
      <div className="quickview-modal-wrapper">
        <div className="bc-product-single">
          {lineItemsLength >= LINE_ITEM_FIXED_LIMIT ? (
            <div className="line-item-warning">{LINE_ITEM_WARNING_MESSAGE}</div>
          ) : (
            <></>
          )}
          <div className="row flex flex-wrap vertical-top">
            <div className="col product-image-section">
              <ProductImages images={images} setReviewImage={setReviewImage} badges={badges} />
            </div>
            <div className="col product-detail-section">
              <div className="bc-product-single__meta">
                {brandUrl ? <Link to={brandUrl}>{brandName}</Link> : brandName}
                <h1 className="bc-product__title">{name}</h1>

                <ReviewSection
                  reviewsCount={reviewCount}
                  reviews_rating_sum={reviewRatingSum}
                  images={reviewImage}
                  reviewHandler={reviewHandler}
                />

                {!isCustomerRestricted ? (
                  <Price
                    sale_price={priceSku["sale_price"]}
                    price={priceSku["base_price"]}
                    retail_price={priceSku["retail_price"]}
                    currentUser={currentUser}
                  />
                ) : null}

                <OtherCustomerGroupsPriceInfo productId={productId} />

                <ProductInfo
                  sku={priceSku["sku"]}
                  upc={upc}
                  availability={availability?.status}
                  order_quantity_minimum={orderQuantityMinimum}
                  order_quantity_maximum={orderQuantityMaximum}
                  weight={weight?.value}
                  weightUnit={weight?.unit}
                  bulk_pricing_rules={bulkPricingRules}
                  customFields={customFields}
                />

                {!isCustomerRestricted ? (
                  <PriceSkuContext.Provider value={contextValue}>
                    <OptionForm
                      min_purchase_qty={orderQuantityMinimum}
                      max_purchase_qty={orderQuantityMaximum}
                      data={product_variants}
                      bigcommerce_id={productId}
                      lineItemsLength={lineItemsLength}
                      availability={availability?.status}
                    />
                  </PriceSkuContext.Provider>
                ) : null}
              </div>
              <div>
                <FacebookShareButton url={location + productDetails.url}>
                  <FontAwesomeIcon
                    icon={faFacebook}
                    size="2x"
                    style={{ color: "#1877f2", marginRight: "9px" }}
                  />
                </FacebookShareButton>

                <PinterestShareButton
                  url={location + productDetails.url}
                  media={"Product"}
                  description={description}
                >
                  <FontAwesomeIcon
                    icon={faPinterest}
                    size="2x"
                    style={{ color: "#bd081c", marginRight: "9px" }}
                  />
                </PinterestShareButton>

                <TwitterShareButton
                  url={location + productDetails.url}
                  title={"Check out this amazing product"}
                >
                  <FontAwesomeIcon
                    icon={faTwitter}
                    size="2x"
                    style={{ color: "#1da1f2", marginRight: "9px" }}
                  />
                </TwitterShareButton>

                <EmailShareButton
                  url={location + productDetails.url}
                  subject={"This is the product"}
                  body={"Check out this amazing product"}
                >
                  <FontAwesomeIcon
                    icon={faEnvelope}
                    size="2x"
                    style={{ color: "#007bff", marginRight: "9px" }}
                  />
                </EmailShareButton>

                <FontAwesomeIcon
                  icon={faPrint}
                  size="2x"
                  style={{ color: "#bd081c", cursor: "pointer" }}
                  onClick={handlePrint}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

export default QuickView;

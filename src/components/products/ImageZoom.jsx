import React from "react";
import PropTypes from "prop-types";
import "react-inner-image-zoom/lib/InnerImageZoom/styles.min.css";
import InnerImageZoom from "react-inner-image-zoom";


function ImageZoom({ selectedImg, badges }) {
  const { url_standard, url_zoom } = selectedImg;

  // const [, setStyle] = useState({
  //   backgroundImage: `url(${url_zoom})`,
  //   backgroundPosition: "0% 0%",
  // });

  // const handleMouseMove = (e) => {
  //   const { left, top, width, height } = e.target.getBoundingClientRect();
  //   const x = ((e.pageX - left) / width) * 100;
  //   const y = ((e.pageY - top) / height) * 100;

  //   setStyle((prevState) => ({
  //     backgroundImage: `url(${url_zoom})`,
  //     backgroundPosition: `${x}% ${y}%`,
  //   }));
  // };

  return (
    <div className="product-main-image">
    
      <InnerImageZoom
        src={url_standard}
        zoomSrc={url_zoom}
        zoomType="hover"
        hideHint
        // height={300}
        // width={300}
        zoomScale={2}
      />
    </div>
  );
}

ImageZoom.propTypes = {
  selectedImg: PropTypes.object,
};

export default ImageZoom;

import React from "react";
import { formatPrice } from "../../utils/money";
import useGetProductPrice from "../../hooks/products/useGetProductPrice";

function OtherCustomerGroupsPriceInfo({ productId }) {
  const { data } = useGetProductPrice(productId);

  if (!data?.distributor_price && !data?.vip_price) return null;

  return (
    <div className="productView-info-list text-uppercase customer-price-list">
      <div className="customer-price-list-inner">
        {data?.distributor_price ? (
          <div className="productView-info-item">
            <span className="productView-info-name price-distro">Distro:</span>
            <span className="productView-info-value">
              ${data?.distributor_price}
            </span>
          </div>
        ) : null}

        {data?.vip_price ? (
          <div className="productView-info-item">
            <span className="productView-info-name price-vip">Vip:</span>
            <span className="productView-info-value">${data?.vip_price}</span>
          </div>
        ) : null}
      </div>
    </div>
  );
}

export default React.memo(OtherCustomerGroupsPriceInfo);

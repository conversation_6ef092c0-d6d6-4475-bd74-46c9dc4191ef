import React from "react";
import IconStar from "../../assets/icon_js/IconStar";
import PropTypes from "prop-types";
import { times } from "../../utils/functions";

function Review({ reviews_rating_sum = 0 }) {
  return (
    <div className="review-star">
      {times(5, (i) => (
        <span
          key={i}
          className={`star-icon ${i < reviews_rating_sum ? "icon-fill" : "icon-empty"
            }`}
        >
          <IconStar />
        </span>
      ))}
    </div>
  );
}

Review.propTypes = {
  reviews_rating_sum: PropTypes.number,
};

export default Review;

import React from "react";
import PropTypes from "prop-types";
import { replaceBigCommerceUrlToHeadlessUrl } from "../../utils/url";

const fieldsToHide = [
  "login only",
  "only show to loggedin",
  "interval quantity",
  "warning",
  "pack count"
];

function CustomFields({ customFields }) {

  let filteredCustomFields = []

  filteredCustomFields = customFields?.length
    ? customFields.filter(
      (item) => !fieldsToHide.includes(item?.name.toLowerCase())
    )
    : [];

  return filteredCustomFields.map((item) => (
    <div className="productView-info-item" key={item?.id}>
      <span className="productView-info-name">{item?.name}:</span>
      <span
        className="productView-info-value"
        dangerouslySetInnerHTML={{
          __html: replaceBigCommerceUrlToHeadlessUrl(item?.value) || "N/A",
        }}
      />
    </div>
  ));
}

CustomFields.propTypes = {
  customFields: PropTypes.array,
};

export default CustomFields;

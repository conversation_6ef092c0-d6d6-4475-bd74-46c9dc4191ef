import React, { useEffect, useState } from "react";
import PropTypes from "prop-types";
import { pdp_thumbnail_images } from "../../../config/slider";
import Slider from "react-slick";
import ImageZoom from "./ImageZoom";
import Image from "../common/Image";
import { dataToImages } from "../../utils/product";
import Product3DModal from "./Product3DModal";
import Product3DModalIcon from "../../assets/images/3d_icon.png";
function ProductImages({ images, setReviewImage, badges }) {
  const [selectedImg, setSelectedImage] = useState({});
  const [show3DModel, setShow3DModel] = useState(false);
  const imgs = dataToImages(images);
  const modelUrl = 'https://cdn.tinyglb.com/models/ad4c9da26aeb44b6a6d64c15a82e2c39.glb';
  const isValidFor3D = window?.location?.pathname?.replace(/\/+$/, '') === '/north-stellar-dark-moon-edition-40k-puffs-20ml-disposable-device-with-fully-curved-screen-four-screen-animations-display-of-5-msrp-25-00-each';

  useEffect(() => {
    if (imgs) {
      let defaultImg = imgs.filter((i) => i.is_default === true);

      if (defaultImg.length === 0) {
        defaultImg = [imgs[0]]
      }

      setSelectedImage({
        id: defaultImg[0]['id'],
        url_standard: defaultImg[0]["url_standard"],
        url_zoom: defaultImg[0]["url_zoom"],
        url_thumbnail: defaultImg[0]["url_thumbnail"],
      });

      setReviewImage && setReviewImage({
        url_standard: defaultImg[0]["url_standard"],
      });
    }
  }, []);

  return (
    <div className="bc-product__gallery">

      <div className="product-main-image-wrapper">

        {badges?.length > 0 ? (
          <div className="product-detail-badges-section">
            {badges?.map((badge, index) => (
              <p>
                <span className="card-badge" key={index} style={{ backgroundColor: badge?.bg_color, color: badge?.font_color }}>{badge?.tag_label}</span>
              </p>
            ))}
          </div>
        ) : null}

        {show3DModel ? (
          <div className="product-main-image">
            <Product3DModal modelUrl={modelUrl} />
          </div>
        ) : !selectedImg?.url_standard ? (
          <div className="product-main-image">
            <div className="skeleton-product-main-image">
              <div className="has-bg"></div>
            </div>
          </div>
        ) : (
          <ImageZoom selectedImg={selectedImg} badges={badges} />
        )}

        {isValidFor3D && !show3DModel && <div className="product-image-3d-action">
          <span onClick={() => {
            setShow3DModel(true);
            setSelectedImage({});
          }}>
            <img src={Product3DModalIcon} alt="" />
          </span>
        </div>}
      </div>

      {imgs?.length > 5 ? (
        <div className="product-thumb-slider">
          <Slider {...pdp_thumbnail_images} className={"slider-wrapper"}>
            {imgs?.map((img, index) => (
              <div className={`product-thumb-item ${selectedImg.id === img.id ? "img-selected" : ""
                }`} key={index}>
                <div className="thumb-inner">
                  <Image
                    height="100px"
                    width="100px"
                    src={img.url_thumbnail}
                    alt={img.url_thumbnail}
                    key={JSON.stringify(img)}
                    onClick={() => {
                      setShow3DModel(false);
                      setSelectedImage({
                        url_standard: img["url_standard"],
                        url_zoom: img["url_zoom"],
                        id: img.id
                      })
                    }}
                  />
                </div>
              </div>
            ))}
          </Slider>
        </div>
      ) : (
        <div className="product-thumb-slider">
          {/* <Slider {...pdp_single_thumbnail_images} className={"slider-wrapper"}> */}
          {imgs?.map((img, index) => (
            <div className={`product-thumb-item ${selectedImg.id === img.id ? "img-selected" : ""
              }`} key={index}>
              <div className="thumb-inner">
                <Image
                  height="100px"
                  width="100px"
                  src={img.url_thumbnail}
                  alt={img.url_thumbnail}
                  key={JSON.stringify(img)}
                  onClick={() => {
                    setShow3DModel(false);
                    setSelectedImage({
                      url_standard: img["url_standard"],
                      url_zoom: img["url_zoom"],
                      id: img.id
                    })
                  }}
                />
              </div>
            </div>
          ))}
          {/* </Slider> */}
        </div>
      )
      }
    </div>
  );
}

ProductImages.propTypes = {
  images: PropTypes.array,
  setReviewImage: PropTypes.func,
};

export default React.memo(ProductImages);

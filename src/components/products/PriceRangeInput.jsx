import React, { useContext, useEffect } from "react";
import PropTypes from "prop-types";
import { useFormContext } from "react-hook-form";
import { SearchContext } from "../../context/SearchContext";

function RenderError({ errors }) {
  if (errors?.["minPrice"] && errors?.["maxPrice"]) {
    return (
      <p className="error-message">Please enter Min Price and Max Price</p>
    );
  } else if (errors?.["minPrice"]) {
    return <p className="error-message">{errors["minPrice"].message}</p>;
  } else if (errors?.["maxPrice"]) {
    return <p className="error-message">{errors["maxPrice"].message}</p>;
  }
  return null; // Return null if there are no errors
}

function PriceRangeInput({ min, max }) {
  const {
    register,
    setValue,
    getValues,
    formState: { errors },
    trigger,
  } = useFormContext();
  const { handlePriceChange, onPriceReset } = useContext(SearchContext);

  const onPriceChange = async () => {
    const validationResults = await trigger(["minPrice", "maxPrice"]);

    // Check if there are validation errors
    if (validationResults) {
      // No validation errors, proceed with your action (e.g., calling handlePriceChange)
      handlePriceChange({
        minPrice: getValues("minPrice"),
        maxPrice: getValues("maxPrice"),
      });
    }
  };

  const handleReset = () => {
    setValue("minPrice", undefined);
    setValue("maxPrice", undefined);
    onPriceReset();
  };

  // prefill values if filters price filer is already applied...
  useEffect(() => {
    if (min) {
      setValue("minPrice", min);
    }
    if (max) {
      setValue("maxPrice", max);
    }
  }, [min, max]);

  return (
    <div className="price-range-field">
      <div className="price-input row flex">
        <div className="col">
          <input
            type="number"
            className="form-input input-small"
            placeholder="Min Price"
            min={`${min}`}
            {...register("minPrice")}
          />
        </div>
        <div className="col">
          <input
            type="number"
            className="form-input input-small"
            placeholder="Max Price"
            max={`${max}`}
            {...register("maxPrice")}
          />
        </div>
      </div>
      <RenderError errors={errors} />
      <div>
        <span
          className={`button`}
          onClick={onPriceChange}
          role="button"
          tabIndex={0}
          onKeyDown={(e) => {
            if (e.key === "Enter" || e.key === "Space") {
              onPriceChange();
            }
          }}
        >
          Update
        </span>
      </div>
      <div className="filter-price-reset">
        <span
          className="link-style"
          onClick={handleReset}
          role="button"
          tabIndex={0}
          onKeyDown={(e) => {
            if (e.key === "Enter" || e.key === "Space") {
              handleReset();
            }
          }}
        >
          Reset
        </span>
      </div>
    </div>
  );
}

PriceRangeInput.propTypes = {
  onPriceChange: PropTypes.func,
  onReset: PropTypes.func,
  formReset: PropTypes.func,
  register: PropTypes.func,
};

export default PriceRangeInput;

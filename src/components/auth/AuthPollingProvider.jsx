import useAuthPolling from '../../hooks/useAuthPolling'

/**
 * AuthPollingProvider - A component that handles authentication polling
 * This component should be placed high in the component tree but outside of Layout
 * to avoid cyclic dependencies and page reloads
 */
const AuthPollingProvider = ({ children }) => {
  // Just initialize the polling hook - no state dependencies to avoid infinite loops
  useAuthPolling()

  // This component doesn't render anything visible
  // It just runs the authentication polling logic in the background
  return children || null
}

export default AuthPollingProvider

import React from "react";
import { <PERSON> } from "gatsby";
import { camelCase, startCase } from "../../../utils/functions";

function createBreadCrumbs(location, url) {
  let crumbs = [];

  if (location?.pathname.includes("products") && url) {
    crumbs = url.split("/").filter((item) => item !== "");
    // remove empty value if any ...
    if (crumbs[0] === "") {
      crumbs.shift();
    }
  } else {
    crumbs = location?.pathname?.split("/").filter((item) => item !== "");
  }
  return crumbs;
}

function Breadcrumbs({ location, url, title }) {
  let crumbs = createBreadCrumbs(location, url);
  if (location?.pathname.includes("blogs")) {
    crumbs = crumbs.slice(0, 1);
  }
  return (
    <>
      <ul className="breadcrumb-list">
        <li>
          <Link to="/">{`Home `}</Link>
        </li>
        {crumbs?.map((item, index) => (
          <li key={index}>
            <Link to={`/${crumbs.slice(0, index + 1).join("/")}`} key={item}>
              {` ${startCase(camelCase(item))}`}
            </Link>
          </li>
        ))}
      </ul>
      <h2>{title}</h2>
    </>
  );
}

export default Breadcrumbs;

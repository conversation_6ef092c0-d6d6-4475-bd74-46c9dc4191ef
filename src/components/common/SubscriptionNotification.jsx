import React, { useEffect, useState } from "react";
import { useLocation } from "@reach/router";
import { formatDateNew } from "../../utils/date";
import useGetSubscriptionDetails from "../../hooks/useGetSubscriptionDetails";
import { useSelector } from "react-redux";

const SubscriptionNotification = () => {
  const location = useLocation();
  const [expirationInfo, setExpirationInfo] = useState({
    expiringSoon: [],
    expired: [],
  });
  const [isNotificationVisible, setIsNotificationVisible] = useState(false);
  const { user } = useSelector((state) => state.auth);
  const { data } = useGetSubscriptionDetails();

  useEffect(() => {
    if (data?.status === 200) {
      const expiringSoon = Array.isArray(data.expiry_soon)
        ? data.expiry_soon
        : [];
      const expired = Array.isArray(data.expired) ? data.expired : [];

      setExpirationInfo({ expiringSoon, expired });
      setIsNotificationVisible(data.expiring_soon || data.expired_flag);
    } else {
      setExpirationInfo({ expiringSoon: [], expired: [] });
      setIsNotificationVisible(false);
    }
  }, [data]);

  // If notification should not be visible, return null
  if (!isNotificationVisible) return null;

  return (
    <div className="custom-expiry-message">
      <div className="container">
        <div className="custom-expiry-message-inner">
          {/* Render messages for expiring soon licenses */}
          {expirationInfo.expiringSoon.map((item, index) => (
            <div className="custom-expiry-message-item" key={index}>
              <p>
                Your license, <strong>{item.licence_name}</strong>, is expiring
                soon on{" "}
                <strong>
                  {item.expiration_date
                    ? formatDateNew(item.expiration_date)
                    : "an unknown date"}
                </strong>
                . To avoid any disruption, please reach out to the support team
                at{" "}
                <a
                  href={`mailto:${
                    item.support_email || "<EMAIL>"
                  }`}
                >
                  <strong>
                    {item.support_email || "<EMAIL>"}
                  </strong>
                </a>{" "}
                to update your license.{" "}
                <a
                  href={`https://midwestgoods2.my.salesforce-sites.com/requestsite/BusinessDoc?id=${user?.customer_id}`}
                  target="_blank"
                >
                  Click here to upload your license file.
                </a>
              </p>
            </div>
          ))}

          {/* Render messages for expired licenses */}
          {expirationInfo.expired.map((item, index) => (
            <div className="custom-expiry-message-item" key={index}>
              <p>
                Your license, <strong>{item.licence_name}</strong>, was expired
                on{" "}
                <strong>
                  {item.expiration_date
                    ? formatDateNew(item.expiration_date)
                    : "an unknown date"}
                </strong>
                . To avoid any disruption, please reach out to the Accounts &
                Billing team at{" "}
                <a href="mailto:<EMAIL>">
                  <strong><EMAIL></strong>
                </a>{" "}
                to update your license.{" "}
                <a
                  href={`https://midwestgoods2.my.salesforce-sites.com/requestsite/BusinessDoc?id=${user?.customer_id}`}
                  target="_blank"
                >
                  Click here to upload your license file.
                </a>
              </p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default SubscriptionNotification;

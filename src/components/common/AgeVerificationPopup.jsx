import React, { useEffect, useState } from "react";
import AgeVerifyModal from "../form/Dialog/AgeVerifyModal";
import Cookie from "js-cookie";
import Sitelogo from "../../../static/img/midwest-goods-super-hero-jobs-at-midwest-goods-logo.jpeg";
// import Banner from "../../../static/img/banner.jpg";
import sample from "../../assets/images/sample.jpeg";
import SocialIcons from "../../sections/footer/SocialIcons";
import CookieConsent from "./CookieConsent";
import Image from "./Image";
import { default_age_verification_info } from "../../../config/ageVerificationInfo";
import { storeInfoURL } from "../../ApiEndpoints";
import { useLocation } from "@reach/router";

const baseUrl = process.env.GATSBY_IMAGE_CDN_BASEURL;
const ageVerificationInfo = process.env.GATSBY_AGE_VERIFICATION_INFO
  ? JSON.parse(process.env.GATSBY_AGE_VERIFICATION_INFO)
  : default_age_verification_info;

const AgeVerificationContent = ({ setItOpen }) => {

  const closeModal = () => {
    setItOpen(false);
  };

  const setValidCookie = (e) => {
    e.preventDefault();
    const ageCookie = Cookie.get("mws_age_cookie_verify");
    if (!ageCookie && ageCookie !== "valid") {
      Cookie.set("mws_age_cookie_verify", "valid", { expires: 7 });
      closeModal();
    }
  };

  const handleMinorUser = (e) => {
    e.preventDefault();
    window.location.href = "https://www.google.com/";
  };

  return (
    <>
      <div className="quickview-age-popup">
        <div className="age-popup">
          <div className="main-content">
            <div className="main-box clearfix">
              <div className="logo-pan text-center">
                {/* <Image className="header-logo-image lazyload" src={Sitelogo} /> */}
                <img
                  src="https://cdn11.bigcommerce.com/s-964anr/images/stencil/original/image-manager/midwest-goods-inc-wholesale-vape-tyson-disposables-2.gif"
                  alt=""
                />
              </div>
              <div className="age-image">
                <a href={ageVerificationInfo["aging_img_url"]}>
                  <Image
                    className="lazyload"
                    src={`${baseUrl}${storeInfoURL}${ageVerificationInfo["aging_img"]}`}
                    alt="Innevape The Berg Berry Eliquids"
                    title="Innevape The Berg Berry Eliquids"
                  />
                </a>
              </div>
              <div className="text-panel">
                Welcome to Midwest Distribution the fastest growing distributor
                of e-cigarette products in the nation. By entering this site you
                are stating that you are of legal age to purchase, handle, and
                own electronic cigarettes and vaping products. Use at your own
                risk.
              </div>

              <div className="blue-text">
                Please verify that you are 21 years of age or older.
              </div>
              <div className="age-button text-center">
                <a
                  className="green-button"
                  href="#"
                  id="OverAge"
                  onClick={setValidCookie}
                >
                  Yes, I am 21 or older
                </a>
                <a
                  className="blue-button"
                  href="#"
                  onClick={handleMinorUser}
                  id="UnderAge"
                >
                  No, I am under 21
                </a>
              </div>
              <p className="text-panel-bottom">
                This website uses cookies, by continuing to use our site you're
                agreeing to our cookie policy.{" "}
                <a href="/privacy-policy/" className="green-text">
                  Click here for info
                </a>
                .
              </p>
              <div className="age-social-pan">
                <SocialIcons />
              </div>
            </div>
          </div>
        </div>
      </div>

      <a
        data-fancybox
        data-src="#hidden-content"
        href="#"
        style={{ display: "none" }}
      >
        Hidden div
      </a>
    </>
  );
};

const AgeVerificationPopup = () => {
  const location = useLocation();

  const checkUserCookie = Cookie.get("mws_age_cookie_verify");
  const [isOpen, setItOpen] = useState(
    checkUserCookie === "valid" ? false : true
  );

  // for hiding age verification pop for customer who are coming 
  // from midwest goods project to express...
  useEffect(() => {
    if (location.search.includes("et=")) {
      Cookie.set("mws_age_cookie_verify", "valid", { expires: 7 });
      setItOpen(false);
    }
  }, []);

  return (
    <>
      {isOpen && (
        <>
          <AgeVerifyModal
            isOpen={isOpen}
            setIsOpen={setItOpen}
            title={""}
            showClose={false}
          >
            <AgeVerificationContent setItOpen={setItOpen} />
          </AgeVerifyModal>
          <CookieConsent />
        </>
      )}
    </>
  );
};

export default AgeVerificationPopup;

import React, { useEffect, useState, useRef } from "react";
import { Formik } from "formik";
import { navigate } from "gatsby";
import useSearchSpring from "../../hooks/products/useSearchSpring";
import IconSearch from "../../assets/icon_js/IconSearch";
import SearchResults from "../searchSpring/SearchResults";
import useToggle from "../../hooks/useToggle";
import IconClose from "../../assets/icon_js/IconClose";
import Loader from "../form/Loader";
import { debounce } from "../../utils/functions";

/**
 * Component for rendering the search bar in the header of the page.
 */
function SearchBarHeader() {
  // State variables
  const [q, setQ] = useState(null);
  const [isOpen, setToggle] = useToggle(true);

  // Ref variables
  const searchInput = useRef(null);
  const elementRef = useRef(null);

  // Hook for fetching search suggestions
  const { autocompletes, suggestions, onSearch, resetData, isLoading } =
    useSearchSpring();

  // Function to handle search input change
  const debouncedSearchHandler = debounce((e) => {
    onSearch(e?.target?.value);
  }, 500);

  // Effect to handle click outside of search bar
  useEffect(() => {
    function handleClickOutside(event) {
      if (elementRef.current && !elementRef.current.contains(event.target)) {
        setToggle(false);
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [elementRef]);

  // Effect to handle search query change
  useEffect(() => {
    if (q !== null) {
      resetData();
      onSearch("");
      navigate(`/search?q=${q}`);
    }
  }, [q]);

  // Function to handle search bar focus
  const onFocus = () => {
    if (!isOpen) {
      setToggle(false);
    }
  };

  return (
    <Formik
      initialValues={{ q: "" }}
      onSubmit={(values, actions) => {
        resetData();

        if (values?.q?.split("").length) {
          navigate(`/search?q=${values.q}`);
        }

        actions.setSubmitting(false);

        actions.resetForm({
          q: "",
        });

        if (isOpen) {
          setToggle(false);

          // Defocus the input
          searchInput.current.blur();
        }
      }}
    >
      {(props) => (
        <form onSubmit={props.handleSubmit}>
          {/* Search input */}
          <input
            ref={searchInput}
            placeholder="Search for the products here..."
            className="form-input"
            onChange={(e) => {
              props.handleChange(e);
              debouncedSearchHandler(e);
            }}
            value={props?.values?.q}
            onFocus={() => onFocus()}
            name={"q"}
            autoComplete={"off"}
          />

          {/* Search button or loading indicator */}
          {isLoading ? (
            <button type="button" className="search-action search-action-loading">
              <Loader />
            </button>
          ) : props?.values?.q === "" ? (
            <button type="submit" className="search-button">
              <IconSearch />
            </button>
          ) : (
            <button
              type="button"
              className="search-action search-action-close"
              onClick={() => {
                resetData();
                props.resetForm();
              }}
            >
              <IconClose />
            </button>
          )}

          {/* Search suggestions */}
          {(autocompletes?.length || suggestions?.length) &&
            props.values?.q !== "" ? (
            <SearchResults
              elementRef={elementRef}
              suggestions={suggestions}
              autocompletes={autocompletes}
              setFieldValue={props.setFieldValue}
              setQ={setQ}
              hide={setToggle}
              isOpen={isOpen}
            />
          ) : null}
        </form>
      )}
    </Formik>
  );
}

export default SearchBarHeader;

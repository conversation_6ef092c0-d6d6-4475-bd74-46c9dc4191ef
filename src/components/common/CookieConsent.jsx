import React, { useEffect, useState } from "react"
import <PERSON>actD<PERSON> from 'react-dom';
import <PERSON><PERSON> from "js-cookie"
import "../../sass/cookie-conset.scss"

function MyCookieConsent({ ccPopupCookie }) {
    const [isCC, setCC] = useState(ccPopupCookie ? true : false)

    const handleOkButton = () => {
        Cookie.set('mws_cookieconsent_status', "dismiss")
        setCC(true)
    }

    if (!isCC) {
        return (
            <div role="dialog" aria-live="polite" aria-label="cookieconsent" aria-describedby="cookieconsent:desc" className="cc-container" >
                <span className="cc-message">
                    This website uses cookies to ensure you get the best experience on our website.
                    <a aria-label="learn more about cookies" role="button" tabIndex="0" className="cc-link" href="http://cookiesandyou.com" target="_blank">Learn more about cookies</a>
                </span>
                <div className="cc-okbtn">
                    <a aria-label="dismiss cookie message" role="button" tabIndex="0" className="cc-btn cc-dismiss" onClick={handleOkButton}>Got it!</a>
                </div>
            </div >
        )
    }

    return null
}

const CookieConsent = () => {
    useEffect(() => {
        const ageCookie = Cookie.get('mws_age_cookie_verify')
        const ccPopupCookie = Cookie.get('mws_cookieconsent_status')

        if (!ageCookie && !ccPopupCookie) {
            // Create a new div to render your component into
            const div = document.createElement('div');
            document.body.appendChild(div);

            // Render your CookieConsent component into the created div
            ReactDOM.render(<MyCookieConsent ccPopupCookie={ccPopupCookie} />, div);

            // Cleanup when the component unmounts
            return () => {
                ReactDOM.unmountComponentAtNode(div);
                document.body.removeChild(div);
            };
        }

    }, []);
    return null; // This component doesn't render any content itself
}

export default CookieConsent
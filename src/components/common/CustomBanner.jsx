import React, { useEffect, useState } from "react";
import { isUserLoggedIn } from "../../utils/auth";
import { useSelector } from "react-redux";
import { useLocation } from "@reach/router";
import { authCleanup } from "../../utils/cleanup";

const CustomerGroupMessage = ({ customerGroupId, customerEmail }) => {
  switch (customerGroupId) {
    case 73:
      return (
        <>
          <div className="document-upload-notification text-center">
            <strong>
              To Upload business documents <u><a href="https://www.midwestgoods.com/customer-verification/">click here</a></u>
            </strong>
          </div>
          <div className="custom-notification">
            <span
              dangerouslySetInnerHTML={{
                __html: `<p>
              Your account has been created and details were emailed to ${customerEmail}
              <br /><br />
              You are almost done, just a few more details need to be taken care of. We want to make this a smooth process
              for you so we have provided an easy to use form below. Please allow 1 to 2 business days for our accounts
              team to wrap up the details and change your account status so you can purchase. If you cannot see the
              Document upload form please <a href="https://www.midwestgoods.com/customer-verification/">click here</a>.
              <br /><br />
              If you have any questions our fantastic accounts team is on standby to help you. Do not hesitate to reach
              out anytime by calling (630)912-2674 #4 or by email to <a
                  href="mailto:<EMAIL>"><EMAIL></a>
              <br /></br>
              <b>Please Note: You will not be able to purchase products until your account has been verified and
                  approved.</b>

              </p>`,
              }}
            />
          </div>
        </>
      );

    case 32:
      return (
        <div className="custom-notification">
          <span
            dangerouslySetInnerHTML={{
              __html: `<p>
              Your account is currently on hold, if you have any questions please reach out to our accounts team at:
              <EMAIL> or (630)-912-2673 #4.
            </p>`,
            }}
          />
        </div>
      );

    case 72:
      return (
        <div className="custom-notification">
          <span
            dangerouslySetInnerHTML={{
              __html: `<p>
              Your account is currently on hold, if you have any questions please reach out to our accounts team at:
        <EMAIL> or (630)-912-2673 #4.
            </p>`,
            }}
          />
        </div>
      );

    case 74:
      return (
        <div className="custom-notification">
          <span
            dangerouslySetInnerHTML={{
              __html: `Your account status is currently in the verification process. Once our accounts team has completed the
        verification process they will reach out to you and will move your status to an approved wholesale account and
        you can begin to purchase. Please allow 24 to 48 hours for them to reach out to you. In the meantime if you have
        any questions you can reach out to our accounts team at: <EMAIL> or (630)-912-2673 #4.
        <br /><br />

        <b>Please Note: You will not be able to purchase products until your account has been verified and approved.</b>`,
            }}
          />
        </div>
      );
    case 75:
      return (
        <div className="custom-notification">
          <span
            dangerouslySetInnerHTML={{
              __html: `Your account is currently on hold, if you have any questions please reach out to our accounts team at:
            <EMAIL> or (630)-912-2673 #4.`,
            }}
          />
        </div>
      );
    case 2:
      return (
        <div className="custom-notification">
          <span
            dangerouslySetInnerHTML={{
              __html: `Hello Visitor! We are a business to business wholesale website. Visitors cannot purchase products. Please <a
                href="/login.php?action=create_account">click here</a> to create an account. Member already? <a
                href="/login.php">Click here</a> to sign in. If you have any questions you can also reach out to our
                  customer accounts team by calling (630)912-2673 #4, or by email <a href="mailto:<EMAIL>"><EMAIL></a>`,
            }}
          />
        </div>
      );
    case 96:
      return (
        <div className="custom-notification">
          <span
            dangerouslySetInnerHTML={{
              __html: `Your account has been approved as a Non-Ends account - This means you will not be able to see or purchase ENDS
                (also called electronic cigarettes, e-cigarettes, vaping devices, or vape pens, which are battery-powered
                devices used to smoke or “vape” a flavored solution). This is due to not having the Required License. If your
                situation changes and you acquire the required license, you can submit the document to <a
                    href="mailto:<EMAIL>"><EMAIL></a> or reach out to us at (630) 912 2673
                #4, and one of our representatives will work with you to update your account.`,
            }}
          />
        </div>
      );
    case 94:
      return (
        <div className="custom-notification">
          <span
            dangerouslySetInnerHTML={{
              __html: `Your account was approved with a limited payment option, all your payments are required to be via Bank Deposit /
            Wire Transfer / ACH / Zelle / Pay upon Pickup only, this is due to missing required documents. When you obtain
            the required document, please submit to <a
                href="mailto:<EMAIL>"><EMAIL></a>. Once we receive the needed documents,
            we will review and update your account to have a Credit Card payment option added at the time of check out.`,
            }}
          />
        </div>
      );
    default:
      return null;
  }
};

function CustomBanner({ isValidCustomerGroup }) {
  const user = isUserLoggedIn();
  const { currentLoginUser } = useSelector((state) => state.customer);
  const location = useLocation();
  const [isRendered, setDelayRendered] = useState(false);

  const handleLogout = () => {
    authCleanup({
      triggerCrossTab: true,
      redirectToLogin: true,
      clearSessionData: true
    });
  };

  // below useEffect is to delay render of non logged in user message.
  useEffect(() => {
    const timer = setTimeout(() => {
      setDelayRendered(true);
    }, 500);

    return () => {
      clearTimeout(timer);
    };
  }, []);



  return user &&
    currentLoginUser?.customer_group_id ? (
    (location?.pathname === "/" || location?.pathname.includes("/account")) ? (
      <div className="container">
        <CustomerGroupMessage
          customerGroupId={currentLoginUser?.customer_group_id}
          customerEmail={currentLoginUser?.email}
        />
        {!isValidCustomerGroup && <button className='button button-small' onClick={handleLogout}>
          Sign out
        </button>}
      </div>
    ) : (null)
  ) : (
    location?.pathname !== '/' && isRendered &&
    <div className="container">
      <div className="custom-notification">
        <span
          dangerouslySetInnerHTML={{
            __html: `Hello Visitor! We are a business to business wholesale website. Visitors cannot purchase products. Please <a href="/create-account">Click here</a> to create an account. Member already? <a href="/login">Click here</a> to sign in. If you have any questions you can also reach out to our
              customer accounts team by calling (630)912-2673 #4, or by email <a href="mailto:<EMAIL>"><EMAIL></a>`,
          }}
        />
      </div>
    </div>
  )
}

export default CustomBanner;

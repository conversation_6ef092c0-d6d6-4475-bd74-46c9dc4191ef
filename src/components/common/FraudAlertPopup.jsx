import React, { useEffect, useState } from "react";
import AgeVerifyModal from "../form/Dialog/AgeVerifyModal";
import Cookie from "js-cookie";
import { useSelector } from "react-redux";

const FraudAlertContent = ({ setItOpen }) => {
  const closeModal = () => {
    setItOpen(false);
  };

  const handleAcknowledge = (e) => {
    e.preventDefault();
    // Set cookie to prevent showing again for 30 days
    Cookie.set("mws_fraud_alert_acknowledged", "true", { expires: 30 });
    closeModal();
  };

  return (
    <>
      <div className="fraud-alert-popup">
        <div className="fraud-alert-container">
          <div className="fraud-alert-content">
            <div className="fraud-alert-box">
              {/* Warning Triangle Icon */}
              <div className="fraud-alert-icon">
                <svg width="80" height="80" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M12 2L1 21H23L12 2Z"
                    fill="#FF0000"
                    stroke="#FFFFFF"
                    strokeWidth="2"
                    strokeLinejoin="round"
                  />
                  <path
                    d="M12 9V13"
                    stroke="#FFFFFF"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                  <path
                    d="M12 17H12.01"
                    stroke="#FFFFFF"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              </div>

              {/* Alert Title */}
              <div className="fraud-alert-title">
                FRAUD ALERT:
              </div>

              {/* Alert Message */}
              <div className="fraud-alert-message">
                WIRE PAYMENTS ONLY TO<br />
                BYLINE BANK ACCOUNT<br />
                ENDING IN 8960.<br />
                SCAMMERS USING<br />
                MIDWEST GOODS INVOICES<br />
                TO COLLECT PAYMENTS.
              </div>

              {/* Phone Number */}
              <div className="fraud-alert-phone">
                CALL ************
              </div>

              {/* Confirmation Text */}
              <div className="fraud-alert-confirm">
                TO CONFIRM.
              </div>

              {/* Acknowledge Button */}
              <div className="fraud-alert-button">
                <button
                  className="acknowledge-button"
                  onClick={handleAcknowledge}
                >
                  I UNDERSTAND
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

const FraudAlertPopup = () => {
  const { currentLoginUser } = useSelector((state) => state.customer);
  const [isOpen, setItOpen] = useState(false);
  const [hasShownForCurrentSession, setHasShownForCurrentSession] = useState(false);

  useEffect(() => {
    // Check if user is logged in and we haven't shown the popup for this session
    if (currentLoginUser?.id && !hasShownForCurrentSession) {
      const fraudAlertCookie = Cookie.get("mws_fraud_alert_acknowledged");

      // Show popup if cookie doesn't exist (user hasn't acknowledged before)
      if (!fraudAlertCookie) {
        setItOpen(true);
        setHasShownForCurrentSession(true);
      }
    }
  }, [currentLoginUser?.id, hasShownForCurrentSession]);

  // Reset session flag when user logs out
  useEffect(() => {
    if (!currentLoginUser?.id) {
      setHasShownForCurrentSession(false);
    }
  }, [currentLoginUser?.id]);

  return (
    <>
      {isOpen && (
        <AgeVerifyModal
          isOpen={isOpen}
          setIsOpen={setItOpen}
          title=""
          showClose={false}
        >
          <FraudAlertContent setItOpen={setItOpen} />
        </AgeVerifyModal>
      )}
    </>
  );
};

export default FraudAlertPopup;

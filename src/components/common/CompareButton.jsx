import { navigate } from "gatsby";
import React from "react";
import Button from "../form/button/Button";
import { useSelector } from "react-redux";

function CompareButton() {
  const { products } = useSelector((state) => state.product_list);

  return (
    <>
      <div className="compare-button">
        <Button
          onClick={() => navigate("/compare")}
        >{`Compare (${products})`}</Button>
      </div>
    </>
  );
}

export default CompareButton;

import React from "react";
import { formatPrice} from "../../utils/money";

const OrderItem = ({ product ,customer_id,handleProductSelection}) => {
    return <>
        <div className="account-product account-product--alignMiddle">
            <div className="account-product-checkItem">
                <input type="checkbox" value={product.product_id} onClick={(e)=>{
                    handleProductSelection(
                        product.product_id,
                        product.product_options,
                        product.sku,
                        e.target.checked
                    )
                }}/>
            </div>
            <figure className="account-product-figure">
                <img
                    className="account-product-image"
                    src={product.images}
                    alt={product.name}
                    title={product.name} />
            </figure>
            <div className="account-product-body">

                <span className="account-product-price account-product-price-custom">
                    {formatPrice(product.base_price)}
                    <div className="price-customer-id">{customer_id}</div>

                    <div className="price-customer-id-text"><span className="customer_id"></span></div>

                    <div className="price-customer-id-text">{customer_id}</div>
                </span>
                <h5 className="account-product-title">{product.quantity} × {product.name}</h5>
                <dl className="definitionList">
                    {product.product_options?.map((opt, index) => (
                        <>
                            <dt className="definitionList-key">{opt.display_name}:</dt>
                            <dd className="definitionList-value">{opt.display_value}</dd>
                        </>
                    ))}

                </dl>
            </div>
        </div>
    </>
}

export default OrderItem
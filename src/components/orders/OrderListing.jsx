import React, { useState } from "react";
import useGetOrders from "../../hooks/customer/useGetOrders";
import { DATE_FORMAT_PRIMARY, formatDate } from "../../utils/date";
import { formatPrice } from "../../utils/money";
import defaultImage from "../../../static/img/default-bc-product.png";
import useToggle from "../../hooks/useToggle";
import OrderDetail from "./OrderDetail";
import Image from "../common/Image";

function OrderListing({ page, limit, hasMoreData }) {
  const { orders } = useGetOrders({ page, limit, hasMoreData });
  const [isActive, setActive] = useToggle(false);
  const [orderId, setOrderId] = useState();

  return (
    <>
      {!isActive && (
        <ul className="order-list">
          {orders &&
            orders.map((item) => (
              <li
                className="account-listItem"
                key={item.id}
                onClick={() => setOrderId(() => item.id, setActive())}
              >
                <div className="row flex vertical-top">
                  <div className="col account-product-figure">
                    <Image src={defaultImage} alt="ProductThumb" />
                  </div>
                  <div className="col account-product-body">
                    <div className="account-right-tag account-orderStatus">
                      <h6 className="account-orderStatus-label">
                        {item.status}
                      </h6>
                    </div>

                    <h5 className="account-product-title">
                      {`Order #${item.id}`}
                    </h5>
                    <p className="account-product-description">
                      {item.items_total} product totaling
                      {` ${formatPrice(parseInt(item.total_inc_tax, 10))}`}
                    </p>

                    <div className="account-product-details flex flex-wrap">
                      <div className="account-product-detail">
                        <p className="account-product-detail-heading">
                          Order Placed
                        </p>
                        <strong>
                          {formatDate(item.date_created, DATE_FORMAT_PRIMARY)}
                        </strong>
                      </div>
                      <div className="account-product-detail">
                        <p className="account-product-detail-heading">
                          Last Update
                        </p>
                        <strong>
                          {formatDate(item.date_modified, DATE_FORMAT_PRIMARY)}
                        </strong>
                      </div>
                    </div>
                  </div>
                </div>
              </li>
            ))}
        </ul>
      )}
      {isActive && <OrderDetail orderId={orderId} />}
    </>
  );
}

export default OrderListing;

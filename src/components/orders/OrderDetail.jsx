import React from "react";
import defaultImage from "../../../static/img/default-bc-product.png";
import CheckBox from "../form/CheckBox";
import Button from "../form/button/Button";
import { useFormik } from "formik";
import useGetOrderDetails, {
  DataToOrderInfo,
  DataToOrderProducts,
  DataToOrderShippingAddress,
} from "../../hooks/customer/useGetOrderDetail";
import PropTypes from "prop-types";
import Image from "../common/Image";
import { isEmpty } from "../../utils/functions";

const DUMMY_DATA = [
  {
    id: "1",
    name: "first",
    value: "first",
  },
];

const formik = useFormik;

const OrderDetail = ({ orderId }) => {
  const { orderInfo, orderProducts, orderShippingAddress } =
    useGetOrderDetails(orderId);
  const order = DataToOrderInfo(orderInfo);
  const products = DataToOrderProducts(orderProducts);
  const shippingAddress = DataToOrderShippingAddress(orderShippingAddress);

  return (
    <>
      <div className="page-wrapper">
        <div className="page-heading-section">
          <h1 className="page-title">{`Order - #${order.id}`}</h1>
        </div>
      </div>

      <div className="container">
        <div className="account-content">
          <div className="row flex flex-wrap">
            <div className="col account-page-content">
              <h3 className="account-heading">Order content</h3>

              <ul className="order-list">
                {products.map((item) => (
                  <li className="account-listItem" key={item.id}>
                    <div className="row flex vertical-top">
                      <div className="col order-item-select">
                        {DUMMY_DATA.map((item) => (
                          <CheckBox
                            id={item.id}
                            key={item.id}
                            name={item.name}
                            value={item.value}
                            label={item.label}
                            count={item.count}
                            onChange={formik.handleChange}
                          />
                        ))}
                      </div>
                      <div className="col account-product-figure">
                        <Image src={defaultImage} alt="OrderDetail" />
                      </div>
                      <div className="col account-product-body">
                        <div className="account-right-tag order-detail-price">
                          <strong>{item.basePrice}</strong>
                        </div>
                        <h5 className="account-product-title">
                          {`${item.quantity} x ${item.productName}`}
                        </h5>
                      </div>
                    </div>
                  </li>
                ))}
              </ul>

              <div className="order-detail-total-section">
                <div className="flex justify-space order-total-item">
                  <span>Sub Total</span>
                  <strong>{`${order.subTotalIncludingTax}`}</strong>
                </div>
                <div className="flex justify-space order-total-item">
                  <span>Shipping</span>
                  <strong>{`${order.shippingCostIncTax}`}</strong>
                </div>
                <div className="flex justify-space order-total-item">
                  <span>Grand Total</span>
                  <strong>
                    <big>{`${order.totalIncludingTax}`}</big>
                  </strong>
                </div>
              </div>
            </div>
            <div className="col account-page-sidebar">
              <div className="account-sidebar-block">
                <h3 className="account-heading">Order Details</h3>
                <dl className="definitionList">
                  <dt>Order status:</dt>
                  <dd>{order.orderStatus}</dd>
                  <dt>Order date:</dt>
                  <dd>{order.dateCreated}</dd>
                  <dt>Order total:</dt>
                  <dd>{order.totalIncludingTax}</dd>
                  <dt>Payment method:</dt>
                  <dd>{order.paymentMethod}</dd>
                </dl>
                <Button type={"submit"} size={"small"} variant={"secondary"}>
                  Print Invoice
                </Button>
              </div>

              <div className="account-sidebar-block">
                <h3 className="account-heading">Ship to</h3>
                <ul className="sidebar-address">
                  {!isEmpty(shippingAddress) && (
                    <>
                      <li>{`${shippingAddress[0].shipping_address_first_name} ${shippingAddress[0].shipping_address_last_name}`}</li>
                      <li>{shippingAddress[0].shipping_address_company}</li>
                      <li>{shippingAddress[0].shipping_address_street_1}</li>
                      <li>{shippingAddress[0].shipping_address_street_2}</li>
                      <li>{`${shippingAddress[0].shipping_address_city}, ${shippingAddress[0].shipping_address_state} ${shippingAddress[0].shipping_address_zip}`}</li>
                      <li>{shippingAddress[0].shipping_address_country}</li>
                    </>
                  )}
                </ul>
              </div>

              <div className="account-sidebar-block">
                <h3 className="account-heading">Bill to</h3>
                <ul className="sidebar-address">
                  <li>{order.billing_address_first_name}</li>
                  <li>{order.billing_address_company}</li>
                  <li>{order.billing_address_street_1}</li>
                  <li>{order.billing_address_street_2}</li>
                  <li>{`${order.billing_address_city}, ${order.billing_address_state} ${order.billing_address_zip}`}</li>
                  <li>{order.billing_address_country}</li>
                </ul>
              </div>

              <div className="account-sidebar-block">
                <h3 className="account-heading">Actions</h3>
                <Button type={"submit"} size={"small"} variant={"secondary"}>
                  Reorder
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

OrderDetail.propTypes = {
  orderId: PropTypes.number,
};

export default OrderDetail;

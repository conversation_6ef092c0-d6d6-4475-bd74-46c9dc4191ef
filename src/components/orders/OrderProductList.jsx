import React from "react";
import OrderItem from "./OrderItem";

const OrderProductList = ({ products, customer_id,handleReorderProductSelection }) => {

    return <ul className="productList">
        {products?.map((op, index) => (
            <OrderItem
                key={op.id}
                product={op}
                customer_id={customer_id}
                handleProductSelection={handleReorderProductSelection}
            />
        ))}
    </ul>
}

export default OrderProductList
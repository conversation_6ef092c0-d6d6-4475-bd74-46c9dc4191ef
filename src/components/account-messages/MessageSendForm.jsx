import React from "react";
import Select from "../form/Select";
import { useForm } from "react-hook-form";
import { createOption } from "../../utils/components";
import TextField from "../form/TextField";
import TextArea from "../form/TextArea";
import { object, string } from "yup";
import { getFormError, requiredMsg } from "../../utils/form";

const MessageSendForm = ({ location }) => {
  const savedValues = location && location.state;
  const initialValues = {
    order: "United States",
    subject: "",
    message: "",
  };
  const validationSchema = object().shape({
    subject: string().required(requiredMsg("Subject")),
    message: string().required(requiredMsg("Message")),
  });
  const {
    register,
    handleSubmit,
    formState: { errors },
    trigger,
    reset,
  } = useForm({
    defaultValues: savedValues || initialValues,
    validationSchema: validationSchema,
  });
  const ORDER_OPTIONS = [createOption("US", "United States")];

  const handleClear = () => {
    reset(initialValues);
  };

  const onSubmit = (data) => {
    console.log(data);
  };

  return (
    <>
      <form
        className="form create-account-form form-mid"
        onSubmit={handleSubmit(onSubmit)}
      >
        <div className="form-field">
          <Select
            id="order"
            name="order"
            {...register("orders")}
            placeholder="Select"
            options={ORDER_OPTIONS}
            required
          />
        </div>
        <div className="form-field">
          <TextField
            id="subject"
            name="subject"
            label="Subject"
            type="input"
            onBlur={() => trigger("subject")} // Trigger validation on blur
            {...register("subject", {
              required: "subject is required",
            })}
            error={getFormError(errors, "subject")}
            required
          />
          {errors.subject && (
            <div className="form-error-message">{errors.subject.message}</div>
          )}
        </div>
        <div className="form-field">
          <TextArea
            id="message"
            rows="5"
            name="message"
            {...register("message", {
              required: "message is required",
            })}
            error={getFormError(errors, "message")}
            label="Message"
            required
          />
          {errors.message && (
            <div className="form-error-message">{errors.message.message}</div>
          )}
        </div>
        <div className="form-actions text-center">
          <button type="submit" className="button">
            Send Message
          </button>
          <button type="button" className="button button--secondary" onClick={handleClear}>
            Clear
          </button>
        </div>
      </form>
    </>
  );
};

export default MessageSendForm;

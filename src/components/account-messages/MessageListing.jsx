import React from "react";
import { DATE_FORMAT_PRIMARY, formatDate } from "../../utils/date";

const MessageListing = ({ meassages }) => {
  return (
    <>
      {" "}
      <ul className="message-list">
        {meassages &&
          meassages.map((item) => (
            <li className="message-listItem" key={item.id}>
              <div className="message-body">
                <div className="message-date">
                  <span>{formatDate(item.message_date, DATE_FORMAT_PRIMARY)} @ {item.message_time}</span>
                </div>
                <p className="message-status">{item.meassage_status}</p>
                <p class="message-detail is-read">{item.message}</p>
              </div>
            </li>
          ))}
      </ul>
    </>
  );
};

export default MessageListing;

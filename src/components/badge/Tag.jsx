import React from "react";
import PropTypes from "prop-types";

function Tag({ children, onClose, state }) {
  return (
    <div>
      <div className="selected-facet">
        <span>
          {children}
          <div
            className="close-icon"
            onClick={() => onClose(state)}
            role="button"
            tabIndex={0}
            onKeyDown={(e) => {
              if (e.key === "Enter") {
                onClose();
              }
            }}
          >
            x
          </div>
        </span>
      </div>
    </div>
  );
}

Tag.prototype = {
  children: PropTypes.string,
  onClose: PropTypes.func,
  state: PropTypes.object,
};

export default Tag;

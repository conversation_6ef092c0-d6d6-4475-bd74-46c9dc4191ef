import React, { useState, useCallback } from "react";
import { Link } from "gatsby";
import parse, { domToReact } from "html-react-parser";

// Helper to extract id from href="javascript:toggleElement('a1')"
const getToggleId = (href) => {
  const match = href?.match(/^javascript:toggleElement\(['"](.+?)['"]\)$/);
  return match ? match[1] : null;
};

function parseStyleString(styleString) {
  if (!styleString) return {};
  return styleString.split(';').reduce((acc, item) => {
    const [key, value] = item.split(':').map((s) => s && s.trim());
    if (key && value) {
      // Convert kebab-case to camelCase for React
      const camelKey = key.replace(/-([a-z])/g, (g) => g[1].toUpperCase());
      acc[camelKey] = value;
    }
    return acc;
  }, {});
}

// This component is used to parse HTML content and convert it to React components...
const ParseHTML = ({ html, className }) => {
  const [toggled, setToggled] = useState({});

  const handleToggle = useCallback(
    (id) => {
      setToggled((prev) => ({
        ...prev,
        [id]: !prev[id],
      }));
    },
    []
  );

  const options = {
    replace: (domNode) => {
      if (domNode.name === "a") {
        const toggleId = getToggleId(domNode.attribs.href);
        if (toggleId) {
          // Toggle link
          return (
            <a
              role="button"
              tabIndex={0}
              className={domNode.attribs.class}
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                handleToggle(toggleId);
              }}
              onKeyPress={(e) => {
                if (e.key === "Enter" || e.key === " ") {
                  e.preventDefault();
                  handleToggle(toggleId);
                }
              }}
            >
              {domToReact(domNode.children, options)}
            </a>
          );
        } else {
          // Normal navigation link
          return (
            <Link to={domNode.attribs.href} className={domNode.attribs.class}>
              {domToReact(domNode.children, options)}
            </Link>
          );
        }
      }
      // Always wrap any element with an id, so toggling works both ways
      if (domNode.attribs && domNode.attribs.id) {
        const id = domNode.attribs.id;
        const Tag = domNode.name;
        return (
          <Tag
            {...domNode.attribs}
            style={{
              ...parseStyleString(domNode.attribs.style),
              display: toggled[id] ? undefined : "none",
            }}
          >
            {domToReact(domNode.children, options)}
          </Tag>
        );
      }
    },
  };

  if (html === null) return null;
  return <div className={className}>{parse(html, options)}</div>;
};

export default ParseHTML;

import React from "react";

function TableHeader({
  type,
  adminName,
  displayQty,
  sortConfig,
  onSort, // Use the passed function to handle sorting
}) {
  const getSortIcon = (columnName) => {
    if (sortConfig?.key === columnName) {
      return sortConfig?.direction === "asc" ? <span>▲</span> : <span>▼</span>;
    }
    return (
      <span class="table-sorting-icon">
        <span>▲</span>
        <span>▼</span>
      </span>
    );
  };

  return (
    <thead>
      <tr>
        {type === "bulkorder" && (
          <th className="w-150px">{displayQty} Pack UPC</th>
        )}
        <th
          className={`${type === "bulkorder" ? "w-250px" : ""}`}
          onClick={() => onSort("flavor")}
        >
          <div class="table-sorting-area">
            <span> Flavor</span>
            <span className="table-sorting-icon-inner">
              {getSortIcon("flavor")}
            </span>
          </div>
        </th>
        {adminName && (
          <>
            <th
              className="w-150px text-center"
              onClick={() => onSort("sold30Days")}
            >
              <div class="table-sorting-area">
                <span>Sold (30 Days)</span>
                <span className="table-sorting-icon-inner">
                {getSortIcon("sold30Days")}
                </span>
              </div>
            </th>
            <th
              className="w-150px text-center"
              onClick={() => onSort("availCaseQty")}
            >
              <div class="table-sorting-area">
                <span>Avail. case qty</span>
                <span className="table-sorting-icon-inner">
                {getSortIcon("availCaseQty")}
                </span>
              </div>
            </th>
          </>
        )}
        <th className="w-150px text-center">Required Case Qty </th>
      </tr>
    </thead>
  );
}


export default TableHeader;

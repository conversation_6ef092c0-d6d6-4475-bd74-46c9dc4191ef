import React, { useState } from "react";
import BulkOrderCatalogueProduct from "./BulkOrderCatalogueProduct";
import TableHeader from "./TableHeader";
import TableBody from "./TableBody";
import { calculateTotal } from "../../../utils/bulkOrder";
import TableFooter from "./TableFooter";
import {
  getPOItems,
  updatePOLINEItem,
  mergeLocalDataToApiData,
  removePOLineItem
} from "../../../utils/purchaseOrder";
import GrandTotal from "./GrandTotal";

function CreateBO({
  orders: apiData,
  toggleTable,
  collapsedTables,
  type,
  admin_name,
}) {
  const [data, setDatas] = useState(mergeLocalDataToApiData(type, apiData));
  const [sortConfig, setSortConfig] = useState(
    data.map(() => ({
      key: "flavor",
      direction: "asc",
    }))
  );  // Array of sort configs, one for each item

  const handleSort = (index, key) => {
    setSortConfig((prevSortConfig) => {
      const newSortConfig = [...prevSortConfig];
      const currentSortConfig = newSortConfig[index];

      // Toggle the sort direction when the same key is clicked
      const newDirection = currentSortConfig.key === key
        ? currentSortConfig.direction === "asc" ? "desc" : "asc"
        : "asc";

      newSortConfig[index] = { key, direction: newDirection };
      return newSortConfig;
    });
  };

  const handleIncrement = (product, variant) => {
    let variant_id = 0;
    let qty = 0;

    const updatedData = data.map((item) => {
      if (item.bop_id === product.bop_id) {
        return {
          ...item,
          variants: item.variants.map((row) => {
            if (row.variant_id === variant.variant_id) {
              variant_id = variant.variant_id;
              qty = !row.requested_qty
                ? 1
                : parseInt(row.requested_qty, 10) + 1;
              return {
                ...row,
                requested_qty: parseInt(
                  !row.requested_qty ? 1 : variant.requested_qty + 1,
                  10
                ),
              };
            }
            return row;
          }),
        };
      }
      return item;
    });

    setDatas(updatedData);

    product.variants.map((newrow) => {
      if (newrow.variant_id === variant_id) {
        newrow.requested_qty = qty;
      }
    });
    variant.requested_qty = qty;
    // update the sessionStorage with the updated data
    updatePOLINEItem(type, product, variant);
  };

  const handleDecrement = (product, variant) => {
    let variant_id = 0;
    let qty = 0;
    const updatedData = data.map((item) => {
      if (item.bop_id === product.bop_id) {
        return {
          ...item,
          variants: item.variants.map((row) => {
            if (row.variant_id === variant.variant_id) {
              variant_id = variant.variant_id;
              qty =
                variant.requested_qty - 1 > 0
                  ? parseInt(variant.requested_qty - 1, 10)
                  : NaN;
              return {
                ...row,
                requested_qty:
                  variant.requested_qty - 1 > 0
                    ? parseInt(variant.requested_qty - 1, 10)
                    : NaN,
              };
            }
            return row;
          }),
        };
      }
      return item;
    });

    setDatas(updatedData);

    product.variants.map((newrow) => {
      if (newrow.variant_id === variant_id) {
        newrow.requested_qty = qty;
      }
    });
    variant.requested_qty = qty;

    // update the sessionStorage with the updated data
    if (qty) {
      updatePOLINEItem(type, product, variant);
    } else {
      removePOLineItem(type, product, variant);
    }
  };

  const handleLocalQtyChange = (e, product, variant) => {
    const newQty = parseInt(e.target.value, 10);
    if (newQty === 0) {
      // Prevent setting the quantity to zero and remove
      e.target.value = variant.requested_qty;
      return;
    }

    const updatedData = data.map((item) => {
      if (item.bop_id === product.bop_id) {
        return {
          ...item,
          variants: item.variants.map((row) => {
            if (row.variant_id === variant.variant_id) {
              return {
                ...row,
                requested_qty: newQty,
              };
            }
            return row;
          }),
        };
      }
      return item;
    });
    setDatas(updatedData);

    // Update the sessionStorage with the updated data
    updatePOLINEItem(type, product, variant);
  };

  const handleLocalStorageQtyChange = (qty, item, variant) => {
    if (!qty || +qty === 0) {
      // remove the variant
      removePOLineItem(type, item, variant);
    } else {
      updatePOLINEItem(type, item, variant);
    }
  };

  return (
    <>
      {data?.map((item, index) => (
        <div key={index} className="bulk-order-catalogue-section">
          {/* Prodcut information */}
          <BulkOrderCatalogueProduct
            index={index}
            toggleTable={toggleTable}
            collapsedTables={collapsedTables}
            item={item}
          />

          <div
            className={`bulk-order-catalogue-table-section ${
              collapsedTables[index] ? "show-table" : "hide-table"
            }`}
          >
            <table className="bulk-order-table product-catalogue-table">
              {/* Table header */}
              <TableHeader
                type={type}
                adminName={admin_name}
                displayQty={item?.display_qty}
                sortConfig={sortConfig[index]} 
                onSort={(key) => handleSort(index, key)} 
              />

              {/* Table body */}
              <TableBody
                type={type}
                item={item}
                index={index}
                handleQtyChange={handleLocalQtyChange}
                handleIncrement={handleIncrement}
                handleDecrement={handleDecrement}
                handleLocalStorageQtyChange={handleLocalStorageQtyChange}
                adminName={admin_name}
                sortConfig={sortConfig[index]} 
              />

              {/* Table footer */}
              <TableFooter
                type={type}
                orders={data}
                index={index}
                calculateTotal={calculateTotal}
                adminName={admin_name}
              />
            </table>
          </div>
        </div>
      ))}
      <div className="bulk-order-sticky-section">
        <div className="container">
          <div className="bulk-order-sticky-inner">
            <GrandTotal type={type} orders={data} adminName={admin_name} />
          </div>
        </div>
      </div>
    </>
  );
}

export default CreateBO;

import React from "react";

function TableFooter({ type, orders, index, adminName, calculateTotal }) {
  return (
    <tfoot className="">
      <tr className="">
        {type === "bulkorder" ? (
          <>
            <td className="">&nbsp;</td>
            {adminName && (
              <>
                <td className="">&nbsp;</td>
                <td className="">&nbsp;</td>
              </>
            )}
          </>
        ) : (
          adminName && (
            <>
              <td className="">&nbsp;</td>
              <td className="">&nbsp;</td>
            </>
          )
        )}
        <td className={adminName ? `w-150px text-center` : ""}>
          <strong>Total Case Qty</strong>
        </td>

        <td className="text-center">
          <strong>{calculateTotal(orders, index)}</strong>
        </td>
      </tr>
    </tfoot>
  );
}

export default TableFooter;

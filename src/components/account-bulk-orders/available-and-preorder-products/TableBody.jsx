import React from "react";
import IconMinus from "../../../assets/icon_js/IconMinus";
import IconPlus from "../../../assets/icon_js/IconPlus";

function TableBody({
  item,
  index,
  type,
  handleQtyChange,
  handleIncrement,
  handleDecrement,
  handleLocalStorageQtyChange,
  adminName,
  sortConfig,
}) {
  const sortedVariants = [...item.variants].sort((a, b) => {
    let aValue = a[sortConfig?.key];
    let bValue = b[sortConfig?.key];

    if (sortConfig?.key === "flavor") {
      aValue = a.flavor || a.po_option;
      bValue = b.flavor || b.po_option;
    } else if (sortConfig?.key === "availCaseQty") {
      aValue = a.current_stock / item.case_qty;
      bValue = b.current_stock / item.case_qty;
    } else if (sortConfig?.key === "sold30Days") {
      aValue = a.total_sold_30;
      bValue = b.total_sold_30;
    }

    if (aValue < bValue) return sortConfig?.direction === "asc" ? -1 : 1;
    if (aValue > bValue) return sortConfig?.direction === "asc" ? 1 : -1;
    return 0;
  });

  return (
    <tbody>
      {sortedVariants.map((rowData, rowIndex) => (
        <tr key={rowIndex}>
          {type === "bulkorder" && <td>{rowData?.bc_sku}</td>}
          <td>{rowData?.flavor || rowData?.po_option}</td>
          {adminName && (
            <>
              <td className="text-center">{rowData?.total_sold_30 || "0"}</td>
              <td className="text-center">
                {(rowData?.current_stock / item.case_qty)?.toFixed(2)}
              </td>
            </>
          )}
          <td className="bulk-order-table-input text-center">
            <div className="flex align-center">
              <div className="form-increment form-increment-style-2">
                <span
                  className="button"
                  onClick={() => {
                    handleDecrement(item, rowData);
                  }}
                >
                  <IconMinus />
                </span>
                <input
                  type="number"
                  className="form-input text-center qty-input"
                  value={rowData?.requested_qty}
                  onChange={(e) => {
                    handleQtyChange(e, item, rowData);
                  }}
                  onBlur={(e) =>
                    handleLocalStorageQtyChange(e.target.value, item, rowData)
                  }
                  placeholder="0"
                  min={0}
                />
                <span
                  className="button"
                  onClick={() => {
                    handleIncrement(item, rowData);
                  }}
                >
                  <IconPlus />
                </span>
              </div>
            </div>
          </td>
        </tr>
      ))}
    </tbody>
  );
}

export default TableBody;

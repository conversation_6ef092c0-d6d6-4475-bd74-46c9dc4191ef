import React, { useEffect, useState } from "react";
import Breadcrumbs from "../../../components/common/breadcrumbs/Breadcrumbs";
import AccountPageNavigation from "../../../components/account/AccountPageNavigation";
import useGetBulkOrders from "../../../hooks/bulk-orders/useGetBulkOrders";
import { useSelector } from "react-redux";
import RepresentativeNavigation from "../../../components/account/RepresentativeNavigation";
import useGetRepresentativesDetails from "../../../hooks/representatives-details/useGetRepresentativesDetails";
// import useCreatePo from "../../../hooks/bulk-orders/useCreatePo";
import Loader from "../../../components/form/Loader";
// import TableActions from "./TableActions";
// import GrandTotal from "./GrandTotal";
// import { createPutPayload } from "../../../utils/bulkOrder";
import POPageTitle from "../POPageTitle";
import BrandsListingDropdown from "../BrandsListingDropdown";
import BulkOrderTopActions from "./BulkOrderTopActions";
import CreateBO from "./CreateBO";
import ReviewBO from "./ReviewBO";

const CreateBulkOrders = ({ location, path, type }) => {
  const [collapsedTables, setCollapsedTables] = useState([]);
  const [step, setStep] = useState("create");

  const { data, isLoading, selectMultipleBrandIds, resetState } =
    useGetBulkOrders(type, step);
  const [orders, setOrders] = useState([]);
  const { user } = useSelector((state) => state.auth);
  const { admin_name } = user;

  const { details: representative } = useGetRepresentativesDetails();

  // const handleCancel = () => {
  //   setOrders(() =>
  //     data.map((data) => ({
  //       ...data,
  //       variants: data.variants.map((row) => ({
  //         ...row,
  //         requested_qty: "",
  //       })),
  //     }))
  //   );
  //   setChangedOrders([]);
  // };

  // const { createOrders, loading } = useCreatePo(handleCancel);
  // const [changedOrders, setChangedOrders] = useState([]);

  useEffect(() => {
    // reset filter value on step chanhge ...
    if (step !== "create") {
      resetState();
    }
  }, [step]);

  useEffect(() => {
    setCollapsedTables(Array(data?.length).fill(true));
    setOrders(() =>
      data?.map((data) => ({
        ...data,
        variants: data.variants.map((row) => ({
          ...row,
          requested_qty: "",
        })),
      }))
    );
  }, [data]);

  const toggleTable = (index) => {
    const newCollapsedTables = [...collapsedTables];
    newCollapsedTables[index] = !newCollapsedTables[index];
    setCollapsedTables(newCollapsedTables);
  };

  // const sendPutRequest = async () => {
  //   const putPayload = createPutPayload(
  //     representative,
  //     changedOrders,
  //     type,
  //     user,
  //     orders
  //   );
  //   await createOrders(putPayload);
  // };

  // const isCreatePOButtonDisabled = () => {
  //   return orders?.every((order) =>
  //     order.variants.every(
  //       (row) => row.requested_qty === undefined || row.requested_qty === ""
  //     )
  //   );
  // };

  return (
    <div className="page-wrapper account-page-wrapper">
      <div className="container">
        <Breadcrumbs location={location} />
      </div>
      <div className="container">
        <div className="account-content">
          <div className="row flex flex-wrap page-layout">
            <div className="col page-sidebar">
              <div className="page-sidebar-inner">
                <AccountPageNavigation path={path} />
                {representative && (
                  <RepresentativeNavigation representative={representative} />
                )}
              </div>
            </div>
            <div className="col page-content">
              <div className="page-content-inner">
                <div className="page-title-sticky">


                  <div className="page-heading-section bulk-order-template-title flex flex-wrap justify-space apply-sticky">
                    <POPageTitle />
                    {step === "create" ? (
                      <BrandsListingDropdown
                        selectMultipleBrandIds={selectMultipleBrandIds}
                      />
                    ) : null}
                  </div>

                  <BulkOrderTopActions
                    step={step}
                    setStep={setStep}
                    representative={representative}
                    type={type}
                    user={user}
                  />

                </div>

                {orders ? (
                  isLoading ? (
                    <Loader />
                  ) : (
                    <>
                      {step === "create" && (
                        <CreateBO
                          orders={orders}
                          toggleTable={toggleTable}
                          collapsedTables={collapsedTables}
                          type={type}
                          admin_name={admin_name}
                        />
                      )}

                      {step === "review" && (
                        <ReviewBO
                          toggleTable={toggleTable}
                          collapsedTables={collapsedTables}
                          type={type}
                          admin_name={admin_name}
                        />
                      )}

                      {/* <div className="bulk-order-sticky-section">
                        <div className="container">
                          <div className="bulk-order-sticky-inner">
                            <GrandTotal
                              type={type}
                              orders={orders}
                              adminName={admin_name}
                            />

                            <TableActions
                              sendPutRequest={sendPutRequest}
                              isCreatePOButtonDisabled={() =>
                                isCreatePOButtonDisabled()
                              }
                              loading={loading}
                              handleCancel={handleCancel}
                            />
                          </div>
                        </div>
                      </div> */}
                    </>
                  )
                ) : (
                  !isLoading && (
                    <table className="bulk-order-table product-catalogue-table">
                      <tr className="text-center">
                        <td>
                          <p className="text-center">No Record found</p>
                        </td>
                      </tr>
                    </table>
                  )
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CreateBulkOrders;

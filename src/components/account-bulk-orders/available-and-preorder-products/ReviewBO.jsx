import React, { useState } from "react";
import {
  getPOItems,
  updatePOLINEI<PERSON>,
  removePOLineItem,
} from "../../../utils/purchaseOrder";
import BulkOrderCatalogueProduct from "./BulkOrderCatalogueProduct";
import TableHeader from "./TableHeader";
import TableBody from "./TableBody";
import { calculateTotal } from "../../../utils/bulkOrder";
import TableFooter from "./TableFooter";
import GrandTotal from "./GrandTotal";

function ReviewBO({ toggleTable, collapsedTables, type, admin_name }) {
  const [data, setDatas] = useState(getPOItems(type) || []);
  const [sortConfig, setSortConfig] = useState(
    data.map(() => ({
      key: "flavor",
      direction: "asc",
    }))
  ); 

  const handleSort = (index, key) => {
    setSortConfig((prevSortConfig) => {
      const newSortConfig = [...prevSortConfig];
      const currentSortConfig = newSortConfig[index];

      const newDirection =
        currentSortConfig.key === key
          ? currentSortConfig.direction === "asc"
            ? "desc"
            : "asc"
          : "asc";

      newSortConfig[index] = { key, direction: newDirection };
      return newSortConfig;
    });
  };
  
  // if no data in sessionStorage, then return a message
  if (!data || !data?.length) {
    return <p className="text-center">Please add some quantities to proceed</p>;
  }

  const handleIncrement = (product, variant) => {
    let variant_id = 0;
    let qty = 0;

    const updatedData = data.map((item) => {
      if (item.bop_id === product.bop_id) {
        return {
          ...item,
          variants: item.variants.map((row) => {
            if (row.variant_id === variant.variant_id) {
              variant_id = variant.variant_id;
              qty = parseInt(variant.requested_qty + 1, 10);
              return {
                ...row,
                requested_qty: parseInt(variant.requested_qty + 1, 10),
              };
            }
            return row;
          }),
        };
      }
      return item;
    });

    setDatas(updatedData);

    product.variants.map((newrow) => {
      if (newrow.variant_id === variant_id) {
        newrow.requested_qty = qty;
      }
    });
    variant.requested_qty = qty;
    // update the sessionStorage with the updated data
    updatePOLINEItem(type, product, variant);
  };

  const handleDecrement = (product, variant) => {
    let variant_id = 0;
    let qty = 0;
  
    const updatedData = data.reduce((acc, item) => {
      if (item.bop_id === product.bop_id) {
        const updatedVariants = item.variants.reduce((varAcc, row) => {
          if (row.variant_id === variant.variant_id) {
            variant_id = variant.variant_id;
            qty = row.requested_qty === null ? NaN : parseInt(row.requested_qty, 10) - 1;
            if (!isNaN(qty) && qty >= 1) {
              varAcc.push({
                ...row,
                requested_qty: qty,
              });
            }
          } else {
            varAcc.push(row);
          }
          return varAcc;
        }, []);
  
        if (updatedVariants.length > 0) {
          acc.push({
            ...item,
            variants: updatedVariants,
          });
        }
      } else {
        acc.push(item);
      }
      return acc;
    }, []);
  
    setDatas(updatedData);
  
    // Update product variants and session storage
    const variantIndex = product.variants.findIndex((newrow) => newrow.variant_id === variant_id);
    if (variantIndex > -1) {
      if (isNaN(qty) || qty <= 1 ) {
        product.variants.splice(variantIndex, 1);
        removePOLineItem(type, product, variant);
      } else {
        product.variants[variantIndex].requested_qty = qty;
        variant.requested_qty = qty;
        updatePOLINEItem(type, product, variant);
      }
    }
  };

  const handleLocalQtyChange = (e, product, variant) => {
    // find that product in the data and then find appropriate variant and update it's requested_qty
    const updatedData = data.map((item) => {
      if (item.bop_id === product.bop_id) {
        return {
          ...item,
          variants: item.variants.map((row) => {
            if (row.variant_id === variant.variant_id) {
              return {
                ...row,
                requested_qty: parseInt(e.target.value, 10),
              };
            }
            return row;
          }),
        };
      }
      return item;
    });
    setDatas(updatedData);

    // update the localStorage with the updated data
    updatePOLINEItem(type, product, variant);
  };

  const handleLocalStorageQtyChange = (qty, product, variant) => {
    const newQty = parseInt(qty, 10);

    // Update the data with the new quantity, removing the variant if the quantity is zero
    const updatedData = data.reduce((acc, item) => {
      if (item.bop_id === product.bop_id) {
        const updatedVariants = item.variants.reduce((varAcc, row) => {
          if (row.variant_id === variant.variant_id) {
            if (newQty > 0) {
              varAcc.push({ ...row, requested_qty: newQty });
            }
          } else {
            varAcc.push(row);
          }
          return varAcc;
        }, []);

        if (updatedVariants.length > 0) {
          acc.push({ ...item, variants: updatedVariants });
        }
      } else {
        acc.push(item);
      }
      return acc;
    }, []);

    setDatas(updatedData);

    // Update the sessionStorage with the updated data
    if (newQty === 0) {
      removePOLineItem(type, product, variant);
    } else {
      updatePOLINEItem(type, product, variant);
    }
  };

  return (
    <>
      {data?.map((item, index) => (
        <div key={index} className="bulk-order-catalogue-section">
          <BulkOrderCatalogueProduct
            index={index}
            toggleTable={toggleTable}
            collapsedTables={collapsedTables}
            item={item}
          />
          <div
            className={`bulk-order-catalogue-table-section ${
              collapsedTables[index] ? "show-table" : "hide-table"
            }`}
          >
            <table className="bulk-order-table product-catalogue-table">
              <TableHeader
                type={type}
                adminName={admin_name}
                displayQty={item?.display_qty}
                sortConfig={sortConfig[index]}
                onSort={(key) => handleSort(index, key)}
              />

              {/* Table body */}
              <TableBody
                type={type}
                item={item}
                index={index}
                handleQtyChange={handleLocalQtyChange}
                handleIncrement={handleIncrement}
                handleDecrement={handleDecrement}
                handleLocalStorageQtyChange={handleLocalStorageQtyChange}
                adminName={admin_name}
                sortConfig={sortConfig[index]}
              />

              {/* Table footer */}
              <TableFooter
                type={type}
                orders={data}
                index={index}
                calculateTotal={calculateTotal}
                adminName={admin_name}
              />
            </table>
          </div>
        </div>
      ))}

      <div className="bulk-order-sticky-section">
        <div className="container">
          <div className="bulk-order-sticky-inner">
            <GrandTotal type={type} orders={data} adminName={admin_name} />
          </div>
        </div>
      </div>
    </>
  );
}

export default ReviewBO;

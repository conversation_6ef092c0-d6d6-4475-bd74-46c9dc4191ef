import React from 'react'

function TableActions({
  handleCancel,
  sendPutRequest,
  loading,
  isCreatePOButtonDisabled
}) {
  return (
    <div className="bulk-order-table-action text-right">
      <button
        className="button button-border button-small"
        onClick={handleCancel}
      >
        Cancel
      </button>
      {isCreatePOButtonDisabled() ? (
        <button
          className="button"
          disabled={isCreatePOButtonDisabled()}
        >
          CREATE PO
        </button>
      ) : (
        <button
          className="button"
          onClick={sendPutRequest}
        >
          CREATE PO
          {loading && (
            <span className="button-loading-spinner "></span>
          )}
        </button>
      )}
    </div>
  )
}

export default TableActions
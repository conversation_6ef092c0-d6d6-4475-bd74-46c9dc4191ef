import React from "react";
import { useState } from "react";
import useCreatePo from "../../../hooks/bulk-orders/useCreatePo";
import {
  createPayload,
  getPOItems,
  isPODisable,
} from "../../../utils/purchaseOrder";

const steps = [
  { label: "Create PO", value: "create" },
  { label: "Review PO", value: "review" },
];

const BulkOrderTopActions = ({ step, setStep, representative, type, user }) => {
  const data = getPOItems(type);
  const [isSubmitDisabled, setIsSubmitDisabled] = useState(false);

  const { createOrders } = useCreatePo(() => { });

  const handleSubmitPO = async () => {
    setIsSubmitDisabled(true)
    const putPayload = createPayload(representative, type, user);
    await createOrders(putPayload);
    setIsSubmitDisabled(false)
  };

  return (
    <div className="bulk-order-top-action flex flex-wrap justify-space vertical-middle">
      <div className="bulk-order-steps">
        <ul className="flex list-style-none">
          {steps.map((item, index) => (
            <li
              key={item.label}
              className={item.value === step ? "active" : null}
              onClick={() => setStep(item.value)}
            >
              <span>{index + 1}</span> {item.label}
            </li>
          ))}
        </ul>
      </div>
      <div className="bulk-order-steps-action">
        {step === "create" ? (
          <span
            className="button button-small"
            onClick={() => {
              window.scrollTo({ top: 0, behavior: "smooth" });
              setStep("review");
            }}
          >
            Next: Review PO
          </span>
        ) : null}
        {step === "review" && data?.length > 0 ? (
          <>
            <span
              className="button button-border button-small"
              onClick={() => {
                window.scrollTo({ top: 0, behavior: "smooth" });
                setStep("create");
              }}
            >
              Previous
            </span>
            <button
              className="button button-small"
              onClick={() => handleSubmitPO()}
              disabled={isPODisable(type) || isSubmitDisabled}
            >
              Next: Submit PO
            </button>
          </>
        ) : null}
      </div>
    </div>
  );
};

export default BulkOrderTopActions;

import React from 'react'
import IconMinus from '../../../assets/icon_js/IconMinus'
import IconPlus from '../../../assets/icon_js/IconPlus'
import Image from '../../common/Image'
import defaultImage from "../../../../static/img/default-bc-product.png";
import { Link } from 'gatsby'

function BulkOrderCatalogueProduct({ toggleTable, index, collapsedTables, item }) {
  return (
    <div className="bulk-order-catalogue-product flex row">
      <div className="catalogue-toggle-button col">
        <span
          className="icon"
          onClick={() => toggleTable(index)}
        >
          {collapsedTables[index] ? (
            <IconMinus />
          ) : (
            <IconPlus />
          )}
        </span>
      </div>

      <div className="catalogue-product-image col">
        <Image
          src={item.product_image || defaultImage} // Updated property name
          alt="ProductThumb"
        />
      </div>
      <div className="catalogue-product-detail col">
        <h6 className="">
          <strong>
            {item.name} {/* Updated property name */}
          </strong>
        </h6>
        <p>
          Case QTY: <strong>{item.case_qty}</strong>
        </p>
        <p>
          Pack Of: <strong>{item.display_qty}</strong>
        </p>
        <p>
          Total Pieces:{" "}
          <strong>{item.display_qty * item.case_qty}</strong>
        </p>
        <Link className='link-style'  to={`${process.env.GATSBY_STOREFRONT_URL}${item?.product_url}`} target='_blank'>View Product</Link>
      </div>
    </div>
  )
}

export default BulkOrderCatalogueProduct
import React, { useEffect, useState } from "react";
import AccountPageNavigation from "../../account/AccountPageNavigation";
import Breadcrumbs from "../../common/breadcrumbs/Breadcrumbs";
import { Link } from "gatsby";
import useGetRepresentativesDetails from "../../../hooks/representatives-details/useGetRepresentativesDetails";
import RepresentativeNavigation from "../../account/RepresentativeNavigation";
import useGetBulkOrdersListing from "../../../hooks/bulk-orders/useGetBulkOrdersListing";
import Loader from "../../form/Loader";
import FormateProductName from "./table/FormateProductName";

const PurchaseOrdersListing = ({ location, status, path }) => {
  const [poListing, setPoListing] = useState([]);
  const [loading, setLoading] = useState(false);
  const getListing = useGetBulkOrdersListing();

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      const { data, isLoading } = await getListing(status);
      if (data) {
        setPoListing(data);
        setLoading(isLoading);
      }
    };

    fetchData();
  }, []);

  const pathnameArray = location && location?.pathname?.split("/");
  const modifiedArray = pathnameArray.filter((item) => item !== "");
  const pathname = modifiedArray[modifiedArray.length - 1]?.split("-")[0];
  const { details: representative } = useGetRepresentativesDetails();

  return (
    <>
      <div className="page-wrapper account-page-wrapper">
        <div className="container">
          <Breadcrumbs location={location} />
        </div>
        <div className="container">
          <div className="account-content">
            <div className="row flex flex-wrap page-layout">
              <div className="col page-sidebar">
                <div className="page-sidebar-inner">
                  <AccountPageNavigation path={path} />
                  {representative && (
                    <RepresentativeNavigation representative={representative} />
                  )}
                </div>
              </div>
              <div className="col page-content">
                <div className="page-content-inner">
                  <div className="page-heading-section">
                    <h1 className="page-title">{pathname} Purchase Order</h1>
                  </div>
                  {poListing?.length > 0 ? (
                    <div className="bulk-order-catalogue-table-section">
                      <table className="bulk-order-table po-order-table">
                        <thead className="">
                          <tr>
                            <th className="table-col-date">Date</th>
                            <th className="table-col-po">PO ID</th>
                            <th className="table-col-product">Product Name</th>
                            <th className="table-col-customer-rep">
                              Customer representative
                            </th>
                          </tr>
                        </thead>
                        <tbody className="">
                          {poListing?.map((rowData, rowIndex) => (
                            <tr key={rowIndex}>
                              <td>
                                {rowData?.date_created?.split(" ")[0] || "-"}
                              </td>
                              <td>
                                <Link
                                  to={`/purchase-orders/${pathname}/details/?id=${rowData.po_id}`}
                                  state={{ po_id: rowData.po_id }}
                                >
                                  {rowData.po_id || "-"}
                                </Link>
                              </td>
                              <td>
                                {rowData?.products_name != "" ? (
                                  <FormateProductName
                                    title={rowData?.products_name}
                                  />
                                ) : (
                                  "-"
                                )}
                              </td>
                              <td>{rowData.customer_rep_name || "-"}</td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  ) : (
                    !loading && <p className="text-center">No Records Found</p>
                  )}

                  {loading && <Loader />}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default PurchaseOrdersListing;

import React from "react";
import MultipleSelect from "../select/MultiSelect";
import { dataTOBrandsOptions } from "../../utils/purchaseOrder";
import useGetBrandList from "../../hooks/bulk-orders/useGetBrandList";

function BrandsListingDropdown({ selectMultipleBrandIds }) {
  const { brandList } = useGetBrandList();

  const handleMultipleBrandSelect = (selectedValues) => {
    let brandIdString = [];

    selectedValues.forEach((item) => {
      brandIdString.push(item.value);
    });

    selectMultipleBrandIds(brandIdString.join(","));
  };

  return (
    <>
      <div className="bulk-order-filter">
        <p>Select Brand </p>
        <MultipleSelect
          options={dataTOBrandsOptions(brandList)}
          onChange={(selectedValues) => handleMultipleBrandSelect(selectedValues)}
          placeholder="Select Brands"
        />
      </div>
    </>
  );
}

export default BrandsListingDropdown;

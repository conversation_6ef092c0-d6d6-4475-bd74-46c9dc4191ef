import React, { useContext } from "react";
import PropTypes from "prop-types";
import { SearchContext } from "../../context/SearchContext";
import { defaultFacetDisplayLimit } from "../../utils/search-spring";
import { useFormContext } from "react-hook-form";

function FilterList({ parent_item, handleViewLimit, list }) {
  const { applyFilter } = useContext(SearchContext);
  const { register } = useFormContext(); // retrieve all hook methods

  return (
    <div className="sidebar-block">
      <h5 className="sidebar-heading">
        <span>{parent_item?.["label"].toUpperCase()}</span>
      </h5>
      <div className="sidebar-nav-list">
        {parent_item?.values
          .slice(
            0,
            list[parent_item["label"]] === true
              ? parent_item.values.length
              : defaultFacetDisplayLimit
          )
          .map((item, index) => (
            <div className="control-item" key={index}>
              <label className="form-label form-label-facet">
                <input
                  type="checkbox"
                  {...register(`${parent_item["field"]}`)}
                  value={item["value"]}
                  checked={item["active"]}
                  className="form-checkbox-facet"
                  onClick={(e) =>
                    applyFilter(
                      `${parent_item["field"]}`,
                      item["value"],
                      e.target.checked
                    )
                  }
                />
                <span className="checkbox-icon"></span>
                {item.label}
                {item.count && (
                  <span className="facet-count">{` (${item.count}) `}</span>
                )}
              </label>
            </div>
          ))}
      </div>

      <div className="facet-clear-button">
        {/* Implemented IIFE - Immediate Invoke Function expression for simplicity */}
        {(() => {
          // case - 1 : when responselength is less than default limit.
          if (parent_item.values.length <= defaultFacetDisplayLimit) {
            return null;
          }
          // case 2 - When response length is bigger than default limit.
          if (
            parent_item.values.length > defaultFacetDisplayLimit &&
            list[parent_item["label"]]
          ) {
            return (
              <span
                className="button button-small"
                onClick={() => handleViewLimit(parent_item)}
                role="button"
                tabIndex={0}
                onKeyDown={(e) => {
                  if (e.key === "Enter" || e.key === "Space") {
                    handleViewLimit(parent_item);
                  }
                }}
              >
                Show Less
              </span>
            );
          }
          // case 2 - When response length is bigger than default limit.
          if (parent_item.values.length > defaultFacetDisplayLimit) {
            return (
              <span
                className="button button-small"
                onClick={() => handleViewLimit(parent_item)}
                role="button"
                tabIndex={0}
                onKeyDown={(e) => {
                  if (e.key === "Enter" || e.key === "Space") {
                    handleViewLimit(parent_item);
                  }
                }}
              >
                Show More
              </span>
            );
          }
        })()}
      </div>
    </div>
  );
}

FilterList.prototype = {
  parent_item: PropTypes.object,
  handleViewLimit: PropTypes.func,
  list: PropTypes.object,
};

export default FilterList;

import React, { useContext } from "react";
// import IconList from "../../assets/icon_js/IconList";
// import IconGrid from "../../assets/icon_js/IconGrid";
import { SearchContext } from "../../context/SearchContext";
import { useLocation } from "@reach/router";

function ProductSortFilter() {
  const {
    products,
    sortingOptions,
    // view,
    handleSortingOptionChange,
    totalResults,
  } = useContext(SearchContext);

  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const search = searchParams.get("q");

  return sortingOptions.length ? (
    <>
      {location?.pathname.includes("search") ? (
        <h1 className="page-title">
          {/* {`${begin} - ${end} of ${totalResults} result "${search?.toUpperCase()}" `} */}

          {search
            ? `${totalResults} result for "${search?.toUpperCase()}" `
            : `${totalResults} Total Results `}
        </h1>
      ) : null}

      {products.length ? (
        <div className="flex vertical-middle search-sort-section">
          {/* <div className="product-view-list flex vertical-middle">
            <span
              className={`grid-view ${view === "grid" && "active"}`}
              onClick={() => setProductView("grid")}
            >
              <IconGrid />
            </span>
            <span
              className={`grid-view ${view === "list" && "active"}`}
              onClick={() => setProductView("list")}
            >
              <IconList />
            </span>
          </div> */}

          {products.length > 1 ? (<form className="actionBar-section">
            <div className="form-field">
              <label className="form-label" htmlFor="sortBy">
                Sort By:
              </label>
              <select
                className="form-select"
                id={"sort-by"}
                onChange={(event) =>
                  handleSortingOptionChange(event.target.value)
                }
                name={"sortBy"}
                placeholder={"Select"}
              >
                {sortingOptions.map((option) => (
                  <option
                    value={`${option.field}=${option.direction}`}
                    key={option.label}
                    selected={option.active}
                  >
                    {option.label}
                  </option>
                ))}
              </select>
            </div>
          </form>) : null}
        </div>
      ) : null}
    </>
  ) : null;
}

export default ProductSortFilter;

import React, { useState, useContext } from "react";
import { LIST_TYPES } from "../../utils/search-spring";
import { SearchContext } from "../../context/SearchContext";
import FilterList from "./FilterList";
import PriceRangeInput from "../products/PriceRangeInput";
import { useForm, FormProvider } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as Yup from "yup"; // Import Yup
import ActiveFilters from "./ActiveFilters";
import { includes } from "../../utils/functions";

const defaultValues = {
  minPrice: 0,
  maxPrice: 0,
  categories_hierarchy: [],
  brand: []
}

// Define a Yup schema for validation
const schema = Yup.object().shape({
  minPrice: Yup.number()
  .required("Minimum price is required"),
  maxPrice: Yup.number()
    .moreThan(Yup.ref('minPrice'), "Max Price must be greater than Min Price")
    .required("Maximum price is required"),
  });
  
  function Facets() {
    const { facets } = useContext(SearchContext);
    const [list, setViewLimit] = useState({});
    
    const methods = useForm({
      defaultValues: defaultValues,
      resolver: yupResolver(schema)
  });
  
  const handleViewLimit = (val) => {
    // following code is a simple toggle of particular value.
    if (list[val["label"]]) {
      setViewLimit((prevState) => ({
        ...prevState,
        [val["label"]]: false,
      }));
    } else {
      setViewLimit((prevState) => ({
        ...prevState,
        [val["label"]]: true,
      }));
    }
  };
  
  return (
    <nav>
      <FormProvider {...methods}>
        <div className="applied-filters">
          <h5 className="sidebar-heading">Refine by</h5>
          <ActiveFilters defaultValues={defaultValues} />
        </div>
        <form>
          {facets?.map((parent_item, index) => {
            // list components.
            if (includes(LIST_TYPES, parent_item["label"])) {
              return (
                <FilterList
                  key={parent_item["field"]}
                  parent_item={parent_item}
                  handleViewLimit={handleViewLimit}
                  list={list}
                />
              );
            }
            // range  component.
            if (parent_item["type"] === "slider") {
              return (
                parent_item?.min !== parent_item?.max ? (<div className="sidebar-block" key={parent_item["field"]}>
                  <h5 className="sidebar-heading">
                    <span>{parent_item["label"].toUpperCase()}</span>
                  </h5>
                  <PriceRangeInput min={parent_item.min} max={parent_item.max} />
                </div>) : null
              );
            } else {
              return null;
            }
          })}
        </form>
      </FormProvider>
    </nav >
  );
}

export default Facets;

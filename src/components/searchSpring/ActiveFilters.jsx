import React, { useContext } from "react";
import { SearchContext } from "../../context/SearchContext";
import Tag from "../badge/Tag";
import { dataToActiveFilters } from "../../utils/search-spring";
import { useFormContext } from "react-hook-form";

function ActiveFilters({ defaultValues }) {
  const { filterSummary, applyFilter, onClearAllFilter } =
    useContext(SearchContext);

  const methods = useFormContext();

  const onClearFilter = () => {
    methods.reset({ ...defaultValues });
    onClearAllFilter();
  };

  const onClose = (value) => {
    const { filter_key, filter_value } = value;
    applyFilter(filter_key, filter_value, false);
  };

  return filterSummary?.length ? (
    <div>
      {dataToActiveFilters(filterSummary).map((i) => {
        return (
          <Tag
            onClose={onClose}
            key={i["value"]}
            state={{
              filter_key: i["field"],
              filter_value: i["value"],
            }}
          >{`${i["filterLabel"]}: ${i["value"]}`}</Tag>
        );
      })}
      <span
        className="facet-clear-link"
        onClick={() => {
          onClearFilter();
        }}
        role="button"
        tabIndex={0}
        onKeyDown={(e) => {
          if (e.key === "Enter" || e.key === "Space") {
            onClearFilter();
          }
        }}
      >
        Clear all
      </span>
    </div>
  ) : (
    <p>No Filters applied</p>
  );
}

export default ActiveFilters;

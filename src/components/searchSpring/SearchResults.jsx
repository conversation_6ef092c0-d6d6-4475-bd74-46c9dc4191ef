import React from "react";
import { Link } from "gatsby";
import Image from '../../components/common/Image'

function SearchResults({
  elementRef,
  suggestions,
  autocompletes,
  setFieldValue,
  setQ,
  hide,
  isOpen,
}) {
  return (
    isOpen && (
      <div className="search-preview-block" ref={elementRef}>
        {suggestions?.length > 0 && (
          <p className="search-message">Product Suggestions</p>
        )}
        {suggestions?.map((val) => {
          return (
            <ul key={`list-${val.text}`} className="search-tag-list">
              <li
                onClick={() => {
                  setQ(val.text);
                  setFieldValue("q", "");
                }}
              >
                {val.text}
              </li>
            </ul>
          );
        })}
        {autocompletes?.length > 0 && (
          <p className="search-message">Product Matches</p>
        )}
        {autocompletes?.map((val, i) => {
          return (
            <div className="search-item" key={`list-${val.id}`}>
              <Link to={`${val.url}`} state={{ productId: val?.uid, type: 'product' }}>
                <div key={`product${i}`}>
                  <div className="search-item-inner flex align-center">
                    <div className="search-figure">
                      <Image
                        src={val.thumbnailImageUrl}
                        alt="product_img"
                        width={100}
                        height={50}
                      />
                    </div>
                    <div className="search-p-name">{val.name}</div>
                  </div>
                </div>
              </Link>
            </div>
          );
        })}
      </div >
    )
  );
}

export default SearchResults;

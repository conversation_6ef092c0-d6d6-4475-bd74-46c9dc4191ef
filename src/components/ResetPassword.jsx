import React from "react";
import { useFormik } from "formik";
import { object, string } from "yup";
import Button from "./form/button/Button";
import TextField from "./form/TextField";
import {
  emailRegExp,
  emailRegMsg,
  getFormError,
  requiredEmailMsg,
} from "../utils/form";
import { Link } from "gatsby";

const validationSchema = object().shape({
  email: string()
    .matches(emailRegExp, emailRegMsg)
    .email(requiredEmailMsg())
    .required(requiredEmailMsg("Email Address")),
});

function ResetPassword() {
  const formik = useFormik({
    initialValues: { email: "" },
    validationSchema: validationSchema,
    onSubmit: (values) => {
      console.log("values", values);
    },
  });

  return (
    <form className="container" onSubmit={formik.handleSubmit}>
      <p>
        Fill in your email below to request a new password. An email will be
        sent to the address below containing a link to verify your email
        address.
      </p>
      <div
        className="flex align-center"
        style={{ gap: "10px", alignItems: "center", marginBottom: "20px" }}
      >
        <TextField
          id={"email"}
          name={"email"}
          width={430}
          error={getFormError(formik.errors, "email")}
          label={"Email Address"}
          onChange={formik.handleChange}
          value={formik.values.email}
        />
        <Button variant="primary" type="submit" style={{ marginTop: "10px" }}>
          Reset Password
        </Button>

        <Link to="/login">
          Login
        </Link>
      </div>
    </form>
  );
}

export default ResetPassword;

import React from "react";
import Slider from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import { HomeTopProducts } from "../../../../config/slider";
import LazyImageDynamic from "../LazyImageDynamic";

function TopTenDynamicBanner({ data }) {
  const config = data["banners"];
  const isLazyDisable = true;

  return (
    <>
      <div
        className={`top-ten-banner-section page-block item-${config.length}`}
      >
        <div className="container">
          <h2 className="section-title">Top 10 Products</h2>
          <div className="banner-slider">
            <Slider {...HomeTopProducts} className={"slider-wrapper"}>
              {config?.map((item, index) =>
                item.visibility ? (
                  <div className="banner-item" key={item.image_url}>
                    <div className="item-inner col">
                      <div className="count-text">
                        {index + 1 === 10 ? "X" : index + 1}
                      </div>
                      <div className="banner-image">
                        <LazyImageDynamic
                          item={item}
                          is_mobile_visible={false}
                          isLazyDisable={isLazyDisable}
                        />
                      </div>
                    </div>
                  </div>
                ) : null
              )}
            </Slider>
          </div>
        </div>
      </div>
    </>
  );
}

export default TopTenDynamicBanner;

import React from "react";
import HeroBanner from "./hero-banner";
import TopBanner from "./top-banner";
import FullBanner from "./full-width-banner";
import TopTenBanner from "./top-ten-products";
import BrandLogoSlider from "./brand-logo-slider";
import FiveColBanner from "./five-column-banner";
import FourColBanner from "./four-column-banner";
import FourColTextBanner from "./four-column-text-banner";
import HTMLBlock from "./html-block";
import ProductListing from "./product-listing";
import TwoColBanner from "./two-column-banner";
import TopTenDynamicBanner from "./top-ten-dynamic-products";
import ThreeColBanner from "./three-column-banner";

function Components({ data, loadIframe, pageUrl, type }) {
  const { code, variant, visibility } = data;
  const config = variant["config"];
  config["type"] = type;

  const layout = variant["admin_layout"];
  if(visibility !== undefined && visibility !== null && visibility !== "website" && visibility !== "both") return <></>;
  switch (code) {
    case "top_banner":
      return <TopBanner layout={layout} data={data} />;

    case "hero_carousel":
      return <HeroBanner layout={layout} data={config} />;

    case "full_width_banner":
      return <FullBanner layout={layout} data={config} />;

    case "top_ten_products":
      return <TopTenBanner data={config} />;

    case "top_ten_dynamic_products":
      return <TopTenDynamicBanner data={config} />;

    case "brand_logo_slider":
      return <BrandLogoSlider data={config} />;

    case "two_column_banner":
      return <TwoColBanner layout={layout} data={config} />;

    case "five_column_banner":
      return <FiveColBanner data={config} />;

    case "four_column_banner":
      return <FourColBanner data={config} />;

    case "three_column_banner":
      return <ThreeColBanner data={config} />;

    case "four_column_text_banner":
      return <FourColTextBanner data={config} />;

    case "html_block":
      return (
        <HTMLBlock data={config} loadIframe={loadIframe} pageUrl={pageUrl} />
      );

    case "product_listing":
      return <ProductListing layout={layout} data={config} />;

    default:
      return <></>;
  }
}

export default Components;

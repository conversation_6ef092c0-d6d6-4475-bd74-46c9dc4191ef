import React from "react";
import SingleFullWidthSlider from "./variants/SingleFullWidthSlider";
import BannerWithFourSideImages from "./variants/BannerWithFourSideImages";
import BannerWithSideImage from "./variants/BannerWithSideImage";

const SelectVariant = ({ layout, data }) => {
  switch (layout) {
    case "style1":
      return <BannerWithFourSideImages data={data} />;

    case "style2":
      return <BannerWithFourSideImages data={data} />;

    case "style3":
      return <SingleFullWidthSlider data={data} />;

    case "style4":
      return <BannerWithSideImage data={data} />;

    default:
      return <></>;
  }
};

function HeroCarousel({ layout, data }) {
  return <SelectVariant layout={layout} data={data} />;
}

export default HeroCarousel;

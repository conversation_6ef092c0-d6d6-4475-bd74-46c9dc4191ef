import React from "react";
import Slider from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import { carousel_slider_settings } from "../../../../../config/slider";
import LazyImage from "../../LazyImage";

const isLazyDisable = true

function SingleFullWidthSlider({ data }) {
  const hero_carousel = data["slider"];
  return (
    <div className="home-hero-banner-section page-block">
      <div className="container">
        <div className="row flex align-self-start flex-wrap">
          <div className="col hero-banner-col">
            <div className="hero-carousel-section">
              {hero_carousel.length ? (
                <Slider
                  {...carousel_slider_settings}
                  className={"slider-wrapper"}
                >
                  {hero_carousel.map((item) =>
                    item.visibility ? (
                      <div className="hero-carousel-item">
                        <div className="hero-crousel-image">
                          <LazyImage item={item} is_mobile_visible={false} isLazyDisable={isLazyDisable} />
                        </div>
                      </div>
                    ) : null
                  )}
                </Slider>
              ) : (
                <></>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default SingleFullWidthSlider;

import React from "react";
import Slider from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import { carousel_slider_settings } from "../../../../../config/slider";
import LazyImage from "../../LazyImage";

const isLazyDisable = true;

const CarouselSlider = ({ hero_carousel }) => {
  return (
    <div className="col hero-banner-col">
      <div className="hero-carousel-section">
        <Slider {...carousel_slider_settings} className={"slider-wrapper"}>
          {hero_carousel?.length
            ? hero_carousel.map((item) => {
              return item.visibility ? (
                <div className="hero-carousel-item" key={item["image_url"]}>
                  <div className="hero-crousel-image">
                    <LazyImage
                      item={item}
                      is_mobile_visible={false}
                      isLazyDisable={isLazyDisable}
                    />
                  </div>
                </div>
              ) : null;
            })
            : null}
        </Slider>
      </div>
    </div>
  );
};

function BannerWithSideImage({ data }) {
  const hero_carousel = data["slider"];
  const side_images = data["side_images"];
  const type = data["type"];

  return (
    <div
      className={`home-hero-banner-section hero-banner-style-2 item-${side_images.length}`}
    >
      <div className="container">
        <div className="row-small flex flex-wrap align-self-start">
          <CarouselSlider hero_carousel={hero_carousel} type={type} />
          <div className="col hero-banner-single-banner">
            {side_images?.length ? (
              side_images?.[0].visibility ? (
                <div key={side_images?.[0]?.["image_url"]}>
                  <LazyImage
                    item={side_images?.[0]}
                    is_mobile_visible={false}
                    height={300}
                    width={188}
                    isLazyDisable={isLazyDisable}
                  />
                </div>
              ) : null
            ) : null}
          </div>
        </div>
      </div>
    </div>
  );
}

export default BannerWithSideImage;

import React from "react";
import Slider from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import { carousel_slider_settings } from "../../../../../config/slider";
import LazyImage from "../../LazyImage";

const isLazyDisable = true

const CarouselSlider = ({ hero_carousel }) => {
  return (
    <div className="col hero-banner-col 333">
      <div className="hero-carousel-section">
        <Slider {...carousel_slider_settings} className={"slider-wrapper"}>
          {hero_carousel?.length
            ? hero_carousel.map((item) => {
              return item.visibility ? (
                <div className="hero-carousel-item" key={item["image_url"]}>
                  <div className="hero-crousel-image">
                    <LazyImage item={item} is_mobile_visible={false} isLazyDisable={isLazyDisable} />
                  </div>
                </div>
              ) : null
            })
            : null}
        </Slider>
      </div>
    </div>
  );
};

function BannerWithFourSideImages({ data }) {
  const hero_carousel = data["slider"];
  const side_images = data["side_images"];
  const type = data["type"];

  return (
    <div
      className={`home-hero-banner-section item-${side_images.length}`}
    >
      <div className="container">
        <div className="row flex flex-wrap align-self-start">
          <CarouselSlider hero_carousel={hero_carousel} type={type} />
          <div className="col hero-small-banner-col">
            <div className="row flex flex-wrap">
              {side_images?.length ? (
                side_images.map((item, index) =>
                  item.visibility ? (
                    <div
                      className={`col banner-${index}`}
                      key={item["image_url"]}
                    >
                      <LazyImage item={item} is_mobile_visible={false} height={300} width={188} isLazyDisable={isLazyDisable} />
                    </div>
                  ) : null
                )
              ) : null}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default BannerWithFourSideImages;

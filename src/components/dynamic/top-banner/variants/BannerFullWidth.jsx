import React from "react";
import PropTypes from "prop-types";
import DefaultHeaderBanner from "../../../../img/header/header-full-width-banner.jpg";
import Image from "../../../common/Image";
function BannerFullWidth({ url, config }) {
  return (
    <div className="header-full-width-banner">
      {config?.visibility ? (
        <Image
          src={url || DefaultHeaderBanner}
          alt="top-banner"
          className="full-width"
        />
      ) : null}
    </div>
  );
}

BannerFullWidth.propTypes = {
  url: PropTypes.string,
};

export default BannerFullWidth;

import React from "react";
import <PERSON><PERSON>ullWidth from "./variants/BannerFullWidth";

const SelectVariant = ({ layout, url, config }) => {
  switch (layout) {
    case "style1":
      return <BannerFullWidth url={url} config={config} />;
      break;
    default:
      return <></>;
  }
};

function TopBanner({ layout, data }) {
  const url = data?.variant?.config?.image_url;
  const { variant } = data;
  const config = variant["config"];
  return <SelectVariant layout={layout} url={url} config={config} />;
}

export default TopBanner;
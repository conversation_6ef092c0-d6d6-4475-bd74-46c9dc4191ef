import React from "react";
import LazyImage from "../LazyImage";

function FiveColBanner({ data }) {
  const config = data["banners"];

  return (
    <>
      <div
        className={`five-col-banner-section banner-style-2 page-block item-${config.length}`}
      >
        <div className="container">
          <div className="row-small flex">
            {config?.map((item) =>
              item.visibility ? (
                <div className="col banner-item" key={item.image_url}>
                  <LazyImage item={item} is_mobile_visible={false} />
                </div>
              ) : null
            )}
          </div>
        </div>
      </div>
    </>
  );
}

export default FiveColBanner;

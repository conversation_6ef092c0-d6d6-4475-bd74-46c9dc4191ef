// import { Link } from "gatsby";
// import React from "react";
// import defaultImage from "../../../static/img/default-bc-product.png";
// import { LazyLoadImage } from "react-lazy-load-image-component";

// const VideoPlayer = ({ src, isMobile, isMuted, isLoop, className }) => (
//   <video autoPlay loop={isLoop} muted={isMuted} className={className}>
//     <source src={src} type="video/mp4" />
//     Your browser does not support the video tag.
//   </video>
// );
// const isVideo = /\.(mp4|mov|avi|mkv|wmv)$/.test(item.image_url);
// const isMobileVideo =
//   item.mobile_image_url &&
//   /\.(mp4|mov|avi|mkv|wmv)$/.test(item.mobile_image_url);

// const RenderAsset = ({
//   item,
//   is_mobile_visible,
//   isLazyDisable = false,
//   ...props
// }) => {
//   console.log(item, "item");
//   // if (
//   //   isVideo
//   //   // item.image_url.includes("mp4") ||
//   //   // item.image_url.includes("mov") ||
//   //   // item.image_url.includes("avi") ||
//   //   // item.image_url.includes("mkv") ||
//   //   // item.image_url.includes("wmv")
//   // ) {
//   return (
//     <>
//       {isVideo ? (
//         <>
//           {" "}
//           <VideoPlayer
//             src={item.image_url}
//             isMobile={is_mobile_visible}
//             isLoop={true}
//             isMuted={true}
//             className={is_mobile_visible ? "banner-for-desktop" : ""}
//           />
//           {/* <video
//               autoPlay
//               loop
//               muted
//               className={is_mobile_visible ? "banner-for-desktop" : ""}
//             >
//               <source src={item.image_url} type="video/mp4" />
//               Your browser does not support the video tag.
//             </video> */}
//           {item?.mobile_image_url ? (
//             // item?.mobile_image_url.includes("mp4") ||
//             // item?.mobile_image_url.includes("mov") ||
//             // item?.mobile_image_url.includes("avi") ||
//             // item?.mobile_image_url.includes("mkv") ||
//             // item?.mobile_image_url.includes("wmv")
//             isMobileVideo ? (
//               <VideoPlayer
//                 src={item.mobile_image_url}
//                 isMobile={is_mobile_visible}
//                 isLoop={true}
//                 isMuted={true}
//                 className={is_mobile_visible ? "banner-for-mobile" : ""}
//               />
//             ) : // <video
//             //   autoPlay
//             //   loop
//             //   muted
//             //   className={is_mobile_visible ? "banner-for-mobile" : ""}
//             // >
//             //   <source src={item?.mobile_image_url} type="video/mp4" />
//             //   Your browser does not support the video tag.
//             // </video>
//             isLazyDisable ? (
//               <img
//                 src={item.image_url}
//                 alt={item.title}
//                 className={
//                   is_mobile_visible
//                     ? "img-container banner-for-mobile"
//                     : "img-container"
//                 }
//               />
//             ) : (
//               <LazyLoadImage
//                 src={item.mobile_image_url}
//                 alt={item.title}
//                 title={item.title}
//                 className={
//                   is_mobile_visible
//                     ? "img-container banner-for-mobile"
//                     : "img-container"
//                 }
//                 {...props}
//               />
//             )
//           ) : (
//             <></>
//           )}
//         </>
//       ) : (
//         <>
//           {isLazyDisable ? (
//             <>
//               <img
//                 src={item.image_url}
//                 alt={item.title}
//                 className={
//                   is_mobile_visible
//                     ? "img-container banner-for-desktop"
//                     : "img-containerrr"
//                 }
//               />
//               {item?.mobile_image_url ? (
//                 // item?.mobile_image_url.includes("mp4") ||
//                 // item?.mobile_image_url.includes("mov") ||
//                 // item?.mobile_image_url.includes("avi") ||
//                 // item?.mobile_image_url.includes("mkv") ||
//                 // item?.mobile_image_url.includes("wmv")
//                 isMobileVideo ? (
//                   <VideoPlayer
//                     src={item.mobile_image_url}
//                     isMobile={is_mobile_visible}
//                     isLoop={true}
//                     isMuted={true}
//                     className={is_mobile_visible ? "banner-for-mobile" : ""}
//                   />
//                 ) : (
//                   // <video
//                   //   autoPlay
//                   //   loop
//                   //   muted
//                   //   className={is_mobile_visible ? "banner-for-mobile" : ""}
//                   // >
//                   //   <source src={item?.mobile_image_url} type="video/mp4" />
//                   //   Your browser does not support the video tag.
//                   // </video>
//                   <img
//                     src={item?.mobile_image_url}
//                     alt={item.title}
//                     className={
//                       is_mobile_visible
//                         ? "img-container banner-for-mobile"
//                         : "img-container"
//                     }
//                   />
//                 )
//               ) : (
//                 <></>
//               )}
//             </>
//           ) : (
//             <>
//               <LazyLoadImage
//                 src={item.image_url}
//                 alt={item.title}
//                 title={item.title}
//                 className={
//                   is_mobile_visible
//                     ? "img-container banner-for-desktop"
//                     : "img-container"
//                 }
//                 {...props}
//               />
//               {item?.mobile_image_url ? (
//                 isMobileVideo ? (
//                   <VideoPlayer
//                     src={item.mobile_image_url}
//                     isMobile={is_mobile_visible}
//                     isLoop={true}
//                     isMuted={true}
//                     className={is_mobile_visible ? "banner-for-mobile" : ""}
//                   />
//                 ) : (
//                   // item?.mobile_image_url.includes("mp4") ||
//                   // item?.mobile_image_url.includes("mov") ||
//                   // item?.mobile_image_url.includes("avi") ||
//                   // item?.mobile_image_url.includes("mkv") ||
//                   // item?.mobile_image_url.includes("wmv")
//                   // <video
//                   //   autoPlay
//                   //   loop
//                   //   muted
//                   //   className={is_mobile_visible ? "banner-for-mobile" : ""}
//                   // >
//                   //   <source src={item?.mobile_image_url} type="video/mp4" />
//                   //   Your browser does not support the video tag.
//                   // </video>
//                   <LazyLoadImage
//                     src={item?.mobile_image_url}
//                     alt={item.title}
//                     title={item.title}
//                     className={
//                       is_mobile_visible
//                         ? "img-container banner-for-mobile"
//                         : "img-container"
//                     }
//                     {...props}
//                   />
//                 )
//               ) : (
//                 <></>
//               )}
//             </>
//           )}
//         </>
//       )}
//     </>
//   );
// };

// function LazyImage({ item, is_mobile_visible, isLazyDisable, ...props }) {
//   if (!item || !item.url || !item.image_url) {
//     return (
//       <>
//         <LazyLoadImage
//           src={defaultImage}
//           className="img-container banner-for-desktop"
//         />
//         {item?.mobile_image_url && item?.mobile_image_url !== "" && (
//           <LazyLoadImage
//             src={defaultImage}
//             className="img-container banner-for-mobile"
//           />
//         )}
//       </>
//     );
//   } else {
//     return (
//       <Link to={item.url}>
//         <RenderAsset
//           item={item}
//           is_mobile_visible={is_mobile_visible}
//           isLazyDisable={isLazyDisable}
//           {...props}
//         />
//       </Link>
//     );
//   }
// }

// export default LazyImage;

import React from "react";
import { Link } from "gatsby";
import { LazyLoadImage } from "react-lazy-load-image-component";
import defaultImage from "../../../static/img/default-bc-product.png";

const VideoPlayer = ({ src, isMobile, isMuted, isLoop, className }) => (
  // <></>
  <video
    autoPlay
    loop={isLoop}
    muted={isMuted}
    preload="auto"
    playsInline
    className={className}
  >
    <source src={src} type="video/mp4" />
    Your browser does not support the video tag.
  </video>
);

const RenderAsset = ({
  item,
  is_mobile_visible,
  isLazyDisable = false,
  ...props
}) => {
  const isVideo = /\.(mp4|mov|avi|mkv|wmv)$/.test(item.image_url);
  const isMobileVideo =
    item.mobile_image_url &&
    /\.(mp4|mov|avi|mkv|wmv)$/.test(item.mobile_image_url);
  const newImageUrl = item.image_url.replace(
    "https://midwestgoods.b-cdn.net/storefront/api/static/images/banner",
    ""
  );

  return (
    <>
      {isVideo ? (
        <VideoPlayer
          src={item.image_url}
          isMobile={is_mobile_visible}
          isLoop={true}
          isMuted={true}
          className={is_mobile_visible ? "banner-for-desktop" : ""}
        />
      ) : isLazyDisable ? (
        <img
          src={newImageUrl}
          alt={item.title}
          className={
            is_mobile_visible
              ? "img-container banner-for-desktop"
              : "img-container"
          }
        />
      ) : (
        <LazyLoadImage
          src={newImageUrl}
          alt={item.title}
          title={item.title}
          className={
            is_mobile_visible
              ? "img-container banner-for-desktop"
              : "img-container"
          }
          {...props}
        />
      )}

      {item.mobile_image_url && (
        <>
          {isMobileVideo ? (
            <VideoPlayer
              src={item.mobile_image_url}
              isMobile={is_mobile_visible}
              isLoop={true}
              isMuted={true}
              className={is_mobile_visible ? "banner-for-mobile" : ""}
            />
          ) : isLazyDisable ? (
            <img
              src={item.mobile_image_url}
              alt={item.title}
              className={
                is_mobile_visible
                  ? "img-container banner-for-mobile"
                  : "img-container"
              }
            />
          ) : (
            <LazyLoadImage
              src={item.mobile_image_url}
              alt={item.title}
              title={item.title}
              className={
                is_mobile_visible
                  ? "img-container banner-for-mobile"
                  : "img-container"
              }
              {...props}
            />
          )}
        </>
      )}
    </>
  );
};

function LazyImage({ item, is_mobile_visible, isLazyDisable, ...props }) {
  const isMobileImage = item?.mobile_image_url && item?.mobile_image_url !== "";

  if (
    !item ||
    !item.url ||
    !item.image_url ||
    item.image_url.includes(
      "static/media/default-bc-product.728a582db5b00f82ba2b.png"
    )
  ) {
    return (
      <>
        <LazyLoadImage
          src={defaultImage}
          alt="default iamge"
          className={`img-container ${isMobileImage && "banner-for-desktop"}`}
        />
        {item?.mobile_image_url && item?.mobile_image_url !== "" && (
          <LazyLoadImage
            src={defaultImage}
            className="img-container banner-for-mobile"
          />
        )}
      </>
    );
  } else {
    return (
      <Link to={item.url}>
        <RenderAsset
          item={item}
          is_mobile_visible={is_mobile_visible}
          isLazyDisable={isLazyDisable}
          {...props}
        />
      </Link>
    );
  }
}

export default LazyImage;

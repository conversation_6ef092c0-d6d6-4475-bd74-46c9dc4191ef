import React from "react";
import LazyImage from "../LazyImage";

function FourColumnTextBanner({ data }) {
  const config = data["banners"];
  const title = data["title"];

  return (
    <div className={`flavor-banner-section page-block item-${config.length}`}>
      <div className="container">
        <h3 className="section-title text-center">{title}</h3>
        <ul className="row flex flex-wrap flavor-banner-list list-style-none">
          {config?.map((item) =>
            item.visibility ? (
              <li className="col" key={item["image_url"]}>
                <LazyImage item={item} is_mobile_visible={false} />
                <h4>{item.title}</h4>
              </li>
            ) : null
          )}
        </ul>
      </div>
    </div>
  );
}

export default FourColumnTextBanner;

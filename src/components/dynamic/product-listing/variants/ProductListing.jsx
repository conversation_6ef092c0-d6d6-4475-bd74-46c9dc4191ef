import React, { useEffect } from "react";
import Slider from "react-slick";
import ProductCard from "../../../products/ProductCard";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import { product_view_slider_settings } from "../../../../../config/slider";
import useGetProductListing from "../../../../hooks/products/useGetProductListing";
import useManageRecentlyViewedProductIds from "../../../../hooks/products/useManageRecentlyViewedProductIds";
import { useSelector } from "react-redux";
import { isUserLoggedIn } from "../../../../utils/auth";

function ProductListing({ data }) {
  const disableBg = data["disable_bg"];
  const title = data["title"];
  const apiUrl = data["api_endpoint"];
  const { currentLoginUser } = useSelector((state) => state.customer);

  const { products } = useGetProductListing(apiUrl);
  const manageRecentlyViewedProductIds = useManageRecentlyViewedProductIds();

  let recentlyViewedBeforeLogin = localStorage.getItem(
    "recentlyViewProductIdsBeforeLogin"
  );

  if (!recentlyViewedBeforeLogin) {
    recentlyViewedBeforeLogin = [];
  } else {
    recentlyViewedBeforeLogin = JSON.parse(recentlyViewedBeforeLogin);
  }

  useEffect(() => {
    if (currentLoginUser?.id && apiUrl === null) {
      manageRecentlyViewedProductIds(
        recentlyViewedBeforeLogin,
        currentLoginUser
      );
    }
  }, []);

  return products.length ? (
    <div
      className={`product-list-block page-block ${
        !disableBg && "section-with-bg"
      }`}
    >
      <div className="container">
        {title && <h2 className="section-title text-center">{`${title}`}</h2>}
        {
          <div className="product-list">
            <Slider
              {...product_view_slider_settings}
              className={"slider-wrapper"}
            >
              {products.map((product) => {
                product["bigcommerce_id"] = product.id;
                return (
                  <ProductCard
                    key={product.id}
                    product={product}
                    isPreorder={apiUrl === "preorder" ? true : false}
                  />
                );
              })}
            </Slider>
          </div>
        }
      </div>
    </div>
  ) : (
    <></>
  );
}

export default ProductListing;

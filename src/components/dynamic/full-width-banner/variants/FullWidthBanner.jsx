import React from "react";
import LazyImage from "../../LazyImage";

function FullWidthBanner({ data }) {
  return (
    <div className="full-width-banner-section page-block">
      <div className="container">
        {data.visibility ? (
          <div className="full-width-banner-item">
            <LazyImage item={data} is_mobile_visible={true} />
          </div>
        ) : null}
      </div>
    </div>
  );
}

export default FullWidthBanner;

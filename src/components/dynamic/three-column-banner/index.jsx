import React from "react";
import LazyImage from "../LazyImage";

function ThreeColBanner({ data }) {
  const config = data["banners"];

  return (
    <>
      <div
        className={`three-col-banner-section page-block item-${config.length}`}
      >
        <div className="container">
          <div className="row-small flex flex-wrap">
            {config?.map((item) =>
              item.visibility ? (
                <div className="col banner-item" key={item.image_url}>
                  <LazyImage item={item} is_mobile_visible={false} />
                </div>
              ) : null
            )}
          </div>
        </div>
      </div>
    </>
  );
}

export default ThreeColBanner;

import React from "react";
import Slider from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import { Link } from "gatsby";
import { HomeBrandSlider } from "../../../../config/slider";
import defaultImage from "../../../../static/img/default-bc-product.png";

function BrandLogoSlider({ data }) {
  const config = data["logos"];
  return (
    <div className={`brand-logo-section page-block item-${config.length}`}>
      <div className="container">
        <div className="banner-slider">
          <Slider {...HomeBrandSlider} className={"slider-wrapper"}>
            {config?.map((item, index) =>
              item.visibility ? (
                <div className="banner-item" key={item.image_url}>
                  <div className="item-inner col">
                    <Link to={item.url}>
                     {item.image_url ? (
                      item.image_url.includes('mp4') || item.image_url.includes('mov') || item.image_url.includes('avi') || item.image_url.includes('mkv') || item.image_url.includes('wmv') ? (                             
                        <video id={`product_video_${index}`} autoPlay loop preload="auto" playsInline muted>
                            <source src={item.image_url} type="video/mp4" />
                            Your browser does not support the video tag.
                        </video>
                      ) : (
                        <img
                          src={item.image_url}
                          alt={item.title}
                          className="img-container"
                          title={item.title}
                        />)
                      ) : (
                        <img
                          src={defaultImage}
                          alt={item.title}
                          title={item.title}
                        />
                      )}
                    </Link>
                  </div>
                </div>
              ) : null
            )}
          </Slider>
        </div>
      </div>
    </div>
  );
}

export default BrandLogoSlider;

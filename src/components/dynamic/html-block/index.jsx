import React, { useEffect, useState } from "react";
import Loader from "../../form/Loader";
import { navigate } from "gatsby";
import ParseHTML from "../../parse-html/ParseHTML";

function HTMLBlock({ data, loadIframe, pageUrl }) {
  const config_data = data["data"];
  const isloadIframe = loadIframe;

  const [iframeLoading, setIframeLoading] = useState(true);

  const handleIframeLoad = () => {
    setIframeLoading(false);
  };

  const initListener = () => {
    function resizeIFrameToFitContent(iFrame, height) {
      iFrame.style.height = height;
    }
    const eventMethod = window.addEventListener
      ? "addEventListener"
      : "attachEvent";
    const eventer = window[eventMethod];
    const messageEvent =
      eventMethod === "attachEvent" ? "onmessage" : "message";

    eventer(
      messageEvent,
      async function (e) {
        const key = e.message ? "message" : "data";
        const data = e[key];
        if (data.from === "bc" && data.type === "changeHeightDynamicPages") {
          const iFrame = document.getElementById("third-party-form-page");
          resizeIFrameToFitContent(iFrame, data.height);
        }
        if (data.from === "bc" && data.type === "DynamicPageIframeRedirect") {
          const url = data.redirect_url;
          navigate(url);
        }
      },
      false
    );
  };

  useEffect(() => {
    if (isloadIframe) {
      initListener();
    }
  }, []);

  return (
    <div className="page-block web-page-style">
      <div className="container">
        {isloadIframe ? (
          <>
            {iframeLoading ? <Loader /> : null}
            <iframe
              src={`${process.env.GATSBY_BC_STORE_URL}${pageUrl}`}
              width="100%"
              height="0"
              onLoad={handleIframeLoad}
              title="Dynamic Page"
              id="third-party-form-page"
              style={{
                display: iframeLoading ? "none" : "block",
              }}
            />
          </>
        ) : (
          <ParseHTML
            html={`${config_data}`}
            className={"web-page-inner-wrapper"}
          />
        )}
      </div>
    </div>
  );
}

export default HTMLBlock;

import React from "react";
import TowColumnBannerStyle1 from "./TowColumnBannerStyle1";
import TowColumnBannerStyle2 from "./TowColumnBannerStyle2";

const SelectBannerVariant = ({ layout, config, isLazyDisable }) => {
  switch (layout) {
    case "style1":
      return (
        <TowColumnBannerStyle1 config={config} isLazyDisable={isLazyDisable} />
      );

    case "style2":
      return (
        <TowColumnBannerStyle2 config={config} isLazyDisable={isLazyDisable} />
      );

    default:
      return <></>;
  }
};

function TwoColBanner({ layout, data }) {
  const config = data["banners"];
  const isLazyDisable = true;

  return (
    <>
      <SelectBannerVariant
          layout={layout}
          config={config}
          isLazyDisable={isLazyDisable}
        />
    </>
  );
}

export default TwoColBanner;

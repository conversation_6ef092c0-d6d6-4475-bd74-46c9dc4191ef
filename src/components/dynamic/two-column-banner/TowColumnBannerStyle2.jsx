import React from "react";
import LazyImage from "../LazyImage";

function TowColumnBannerStyle2({ config, isLazyDisable }) {
  return (
    <div className={`two-col-banner-section banner-style-2 mb-0`}>
      <div className="container">
        <div className="row-small flex flex-wrap align-self-start">
          <div className="col banner-item">
            {config[0].visibility ? (
              <LazyImage
                item={config[0]}
                is_mobile_visible={false}
                isLazyDisable={isLazyDisable}
              />
            ) : null}
          </div>
          <div className="col banner-item">
            {config[1].visibility ? (
              <LazyImage
                item={config[1]}
                is_mobile_visible={false}
                isLazyDisable={isLazyDisable}
              />
            ) : null}
          </div>
        </div>
      </div>
    </div>
  );
}

export default TowColumnBannerStyle2;

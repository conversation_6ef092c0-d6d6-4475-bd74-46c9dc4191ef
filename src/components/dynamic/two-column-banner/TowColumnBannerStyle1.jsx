import React from "react";
import LazyImage from "../LazyImage";

function TowColumnBannerStyle1({ config,isLazyDisable }) {

  return (
    <div className={`home-large-mid-banner-section page-block mb-0`}>
      <div className="container">
        <div className="row flex flex-wrap align-self-start">
          <div className="col home-large-col-banner">
            {config[0].visibility ? (
              <LazyImage
                item={config[0]}
                is_mobile_visible={true}
                isLazyDisable={isLazyDisable}
              />
            ) : null}
          </div>
          <div className="col home-mid-col-banner">
            {config[1].visibility ? (
              <LazyImage
                item={config[1]}
                is_mobile_visible={true}
                isLazyDisable={isLazyDisable}
              />
            ) : null}
          </div>
        </div>
      </div>
    </div>
  );
}

export default TowColumnBannerStyle1;

import React, { useState } from "react";
import { Link } from "gatsby";
import {
  default_categories_sidebar_navigation,
  default_brands_sidebar_navigation,
} from "../../../config/navigation";

const RecursiveNavItem = ({ item, level = 0 }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const hasChildren = item.children && item.children.length > 0;

  const handleExpand = (e) => {
    e.stopPropagation();
    setIsExpanded(!isExpanded);
  };

  return (
    <li>
      <div>
        <Link
          to={item.url}
          className="sd-link"
          style={{ paddingLeft: `${level * 20}px` }}
        >
          {item.label}
        </Link>
        {hasChildren && (
          <button
            onClick={handleExpand}
            className={isExpanded ? 'active' : ''}
          >
            {isExpanded ? '−' : '+'}
          </button>
        )}
      </div>
      {hasChildren && isExpanded && (
        <ul>
          {item.children.map((child, index) => (
            <RecursiveNavItem
              key={index}
              item={child}
              level={level + 1}
            />
          ))}
        </ul>
      )}
    </li>
  );
};

function SidebarList() {
  const category_nav = process.env.GATSBY_sidebar_navigation2_id
    ? JSON.parse(process.env.GATSBY_sidebar_navigation1_id || "{}")[
        "navigations"
      ]
    : default_categories_sidebar_navigation;

  const brands_nav = process.env.GATSBY_sidebar_navigation3_id
    ? JSON.parse(process.env.GATSBY_sidebar_navigation2_id || "{}")[
        "navigations"
      ]
    : default_brands_sidebar_navigation;

  return (
    <>
      <div className="sidebar-block-bottom">
        <h5 className="sidebar-heading">Categories</h5>
        <ul className="sidebar-nav-list">
          {category_nav.map((item, index) => (
            <RecursiveNavItem
              key={index}
              item={item}
            />
          ))}
        </ul>
      </div>

      <div className="sidebar-block-bottom">
        <h5 className="sidebar-heading">Brands</h5>
        <ul className="sidebar-nav-list">
          {brands_nav.map((item, index) => (
            <RecursiveNavItem
              key={index}
              item={item}
            />
          ))}
        </ul>
      </div>
    </>
  );
}

export default SidebarList;

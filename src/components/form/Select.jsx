import React from "react";
import PropTypes from "prop-types";
import ErrorMessage from "./ErrorMessage";

const Select = ({
  id,
  name,
  options,
  placeholder,
  label,
  labelFor,
  required,
  error,
}) => {
  return (
    <>
      {label && (
        <label className="form-label" htmlFor={labelFor}>
          {label}
          {required && <small>REQUIRED</small>}
        </label>
      )}
      <select
        className="form-select"
        id={id}
        name={name}
        placeholder={placeholder}
        required={required}
      >
        {options &&
          options.map((option) => {
            return (
              <option value={option.value} key={option.value}>
                {option.label}
              </option>
            );
          })}
      </select>
      {error && <ErrorMessage>{error}</ErrorMessage>}
    </>
  );
};

export const Option = PropTypes.shape({
  label: PropTypes.string,
  value: PropTypes.any,
});

Select.propTypes = {
  id: PropTypes.string.isRequired,
  name: PropTypes.string,
  options: PropTypes.arrayOf(Option).isRequired,
  placeholder: PropTypes.string,
  label: PropTypes.string,
  labelFor: PropTypes.string,
  required: PropTypes.bool,
  error: PropTypes.string,
};

Select.displayName = "Select";

export default Select;

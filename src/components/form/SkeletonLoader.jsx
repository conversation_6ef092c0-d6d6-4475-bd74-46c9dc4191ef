import React from "react";

function SkeletonLoader() {
  return (
    <div className="skeleton-product-listing">
      
      <div className="skeleton-item">
        <div className="skeleton-inner">
          <div className="skeleton-large has-bg"></div>
          <div className="skeleton-line has-bg"></div>
          <div className="skeleton-small has-bg"></div>
        </div>
      </div>

      <div className="skeleton-item">
        <div className="skeleton-inner">
          <div className="skeleton-large has-bg"></div>
          <div className="skeleton-line has-bg"></div>
          <div className="skeleton-small has-bg"></div>
        </div>
      </div>

      <div className="skeleton-item">
        <div className="skeleton-inner">
          <div className="skeleton-large has-bg"></div>
          <div className="skeleton-line has-bg"></div>
          <div className="skeleton-small has-bg"></div>
        </div>
      </div>

      <div className="skeleton-item">
        <div className="skeleton-inner">
          <div className="skeleton-large has-bg"></div>
          <div className="skeleton-line has-bg"></div>
          <div className="skeleton-small has-bg"></div>
        </div>
      </div>

      <div className="skeleton-item">
        <div className="skeleton-inner">
          <div className="skeleton-large has-bg"></div>
          <div className="skeleton-line has-bg"></div>
          <div className="skeleton-small has-bg"></div>
        </div>
      </div>

      <div className="skeleton-item">
        <div className="skeleton-inner">
          <div className="skeleton-large has-bg"></div>
          <div className="skeleton-line has-bg"></div>
          <div className="skeleton-small has-bg"></div>
        </div>
      </div>

      <div className="skeleton-item">
        <div className="skeleton-inner">
          <div className="skeleton-large has-bg"></div>
          <div className="skeleton-line has-bg"></div>
          <div className="skeleton-small has-bg"></div>
        </div>
      </div>

      <div className="skeleton-item">
        <div className="skeleton-inner">
          <div className="skeleton-large has-bg"></div>
          <div className="skeleton-line has-bg"></div>
          <div className="skeleton-small has-bg"></div>
        </div>
      </div>

      <div className="skeleton-item">
        <div className="skeleton-inner">
          <div className="skeleton-large has-bg"></div>
          <div className="skeleton-line has-bg"></div>
          <div className="skeleton-small has-bg"></div>
        </div>
      </div>

      <div className="skeleton-item">
        <div className="skeleton-inner">
          <div className="skeleton-large has-bg"></div>
          <div className="skeleton-line has-bg"></div>
          <div className="skeleton-small has-bg"></div>
        </div>
      </div>
      
    </div>
  );
}

export default SkeletonLoader
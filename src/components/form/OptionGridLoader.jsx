import React from "react";

function OptionGridLoader() {
  return (
    <div className="loading-option-grid">
      <div className="pog__row-item">
        <div className="table-cell table-cell-first">
          <div className="has-bg"></div>
        </div>
        <div className="table-cell table-cell-input">
          <div className="has-bg"></div>
        </div>
        <div className="table-cell table-cell-input">
          <div className="has-bg"></div>
        </div>
        <div className="table-cell table-cell-input">
          <div className="has-bg"></div>
        </div>
      </div>
      <div className="pog__row-item">
        <div className="table-cell table-cell-first">
          <div className="has-bg"></div>
        </div>
        <div className="table-cell table-cell-input">
          <div className="has-bg"></div>
        </div>
        <div className="table-cell table-cell-input">
          <div className="has-bg"></div>
        </div>
        <div className="table-cell table-cell-input">
          <div className="has-bg"></div>
        </div>
      </div>
      <div className="pog__row-item">
        <div className="table-cell table-cell-first">
          <div className="has-bg"></div>
        </div>
        <div className="table-cell table-cell-input">
          <div className="has-bg"></div>
        </div>
        <div className="table-cell table-cell-input">
          <div className="has-bg"></div>
        </div>
        <div className="table-cell table-cell-input">
          <div className="has-bg"></div>
        </div>
      </div>
      <div className="pog__row-item">
        <div className="table-cell table-cell-first">
          <div className="has-bg"></div>
        </div>
        <div className="table-cell table-cell-input">
          <div className="has-bg"></div>
        </div>
        <div className="table-cell table-cell-input">
          <div className="has-bg"></div>
        </div>
        <div className="table-cell table-cell-input">
          <div className="has-bg"></div>
        </div>
      </div>
    </div>
  );
}

export default OptionGridLoader;

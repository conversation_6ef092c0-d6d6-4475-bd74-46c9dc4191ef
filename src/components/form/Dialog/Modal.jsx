import React, { useEffect, useRef } from "react";
import ReactDOM from "react-dom";
import PropTypes from "prop-types";
import "./modal.css";
import IconClose from "../../../assets/icon_js/IconClose";

const Modal = ({ isShowing, hide, children, dialogHead }) => {
  const ref = useRef(null);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (ref.current && !ref.current.contains(event.target)) {
        hide && hide();
      }
    };
    document.addEventListener("click", handleClickOutside, true);
    document.addEventListener(27, handleClickOutside, true);

    return () => {
      document.removeEventListener("click", handleClickOutside, true);
      document.removeEventListener(27, handleClickOutside, true);
    };
  }, [hide]);

  return isShowing
    ? ReactDOM.createPortal(
      <div>
        <div className="modal-overlay" />
        <div
          className="modal-wrapper"
          aria-modal
          tabIndex={-1}
          role="dialog"
        >
          <div className="modal" ref={ref}>
            <div className="modal-header">
              <DialogHead>{dialogHead}</DialogHead>
              <button
                type="button"
                className="modal-close-button"
                data-dismiss="modal"
                aria-label="Close"
                onClick={hide}
              >
                <span>
                  <IconClose />
                </span>
              </button>
            </div>
            <div className="modal-body">
              {children}
            </div>
          </div>
        </div>
      </div>,
      document.body
    )
    : null;
};

export default Modal;

const DialogHead = ({ children }) => {
  return <div className="modal-title">{children}</div>;
};

DialogHead.propTypes = {
  chidlren: PropTypes.node,
};

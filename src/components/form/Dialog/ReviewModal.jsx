import React, { useRef } from "react";
import "./modal.css";
import { useFormik } from "formik";
import {
  RATING_OPTIONS,
  review_form_initial_values,
  review_validation_schema,
} from "../../../utils/review";
import TextField from "../TextField";
import { Link, navigate } from "gatsby";
import useToastify from "../../../hooks/ui/useToastify";
import Button from "../button/Button";
import { getFormError, emailRegMsg } from "../../../utils/form";
import useAddProductReview from "../../../hooks/review/useAddProductReview";
import useShowReviewModal from "../../../hooks/products/useShowReviewModal";
import Image from "../../common/Image";
import ReCAPTCHA from "react-google-recaptcha";
import ErrorMessage from "../ErrorMessage";

export default function ReviewModal({ onClose, images, productid, name }) {
  const { closeReviewModal } = useShowReviewModal();
  const { addProductReview } = useAddProductReview();
  const recaptchaRef = useRef();
  const { toastMessage } = useToastify();
  const formik = useFormik({
    initialValues: review_form_initial_values,
    validationSchema: review_validation_schema,
    onSubmit: (values) => {
      const recaptchaValue = recaptchaRef.current.getValue();
      if (recaptchaValue) {
        addProductReview(values, productid);
        onClose();
        closeReviewModal();
        formik.resetForm();
      } else {
        toastMessage("error", "Please Check re-captcha first to login");
      }
    },
  });

  return (
    <div className="review-modal-wrapper">
      <div className="review-image-section">
        <div className="review-product-wrap">
          <div className="review-product-image">
            <Image src={images?.url_standard} alt="review image" />
          </div>
          <div className="review-product-name">{name}</div>
        </div>
      </div>
      <div className="review-form-section">
        <div className="review-form">
          <form id="review-form" onSubmit={formik.handleSubmit}>
            <div className="form-field">
              <label className="form-label" htmlFor="rating">
                Rating *
              </label>
              <select
                className="form-select"
                id={"rating"}
                onChange={formik.handleChange}
                name={"rating"}
                placeholder={"Select"}
              >
                {RATING_OPTIONS.map((option) => (
                  <option
                    value={option.value}
                    key={option.label}
                    selected={option.active}
                  >
                    {option.label}
                  </option>
                ))}
              </select>
              {getFormError(formik.errors, "rating") &&
                formik.touched["rating"] && (
                  <ErrorMessage>
                    {getFormError(formik.errors, "rating")}
                  </ErrorMessage>
                )}
            </div>
            <div className="form-field">
              <TextField
                id="name"
                label={"Name *"}
                labelFor={"Name"}
                name={"name"}
                error={getFormError(formik.errors, "name")}
                type={"text"}
                value={formik.values.name}
                onChange={formik.handleChange}
                touched={formik.touched["name"]}
              />
            </div>
            <div className="form-field">
              <TextField
                id="user-email"
                label={"Email Address *"}
                labelFor={"Email Address"}
                name={"email"}
                error={getFormError(formik.errors, "email")}
                type={"email"}
                value={formik.values.email}
                onChange={formik.handleChange}
                touched={formik.touched["email"]}
              />
            </div>
            <div className="form-field">
              <TextField
                id="reviewSubject"
                label={"Review Subject *"}
                labelFor={"Review Subject"}
                name={"reviewSubject"}
                error={getFormError(formik.errors, "reviewSubject")}
                type={"text"}
                onChange={formik.handleChange}
                touched={formik.touched["reviewSubject"]}
              />
            </div>
            <div className="form-field">
              <TextField
                id="comments"
                label={"Comments *"}
                labelFor={"Comments"}
                name={"comments"}
                error={getFormError(formik.errors, "comments")}
                type={"text"}
                onChange={formik.handleChange}
                touched={formik.touched["comments"]}
              />
            </div>
            <div className="gcaptcha-wrapper">
              <ReCAPTCHA
                ref={recaptchaRef}
                sitekey={`${process.env.GATSBY_RECAPTCHA_SITE_KEY}`}
              />
            </div>

            <Button type="submit" className="button">
              SUBMIT REVIEW
            </Button>
          </form>
        </div>
      </div>
    </div>
  );
}

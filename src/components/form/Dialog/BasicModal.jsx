import React from "react";
import "./modal.css";
import Image from "../../common/Image";

function BasicModal({
  show,
  onClose,
  title,
  body,
  button,
  icon,
}) {
  if (!show) {
    return null;
  }
  return (
    <div className="basic-modal">
      <div className="basic-modal-content">
        {icon && (
          <div className="basic-modal-icon">
            <Image
              src={icon}
              alt="icon"
            />
          </div>
        )}
        {title && (
          <div className="basic-modal-header">
            <div className="basic-modal-title">{title}</div>
          </div>
        )}
        {body && <div className="basic-modal-body">{body}</div>}
        <div className="basic-modal-footer">
          <button className="basic-modal-button" onClick={onClose}>
            {button}
          </button>
        </div>
      </div>
    </div>
  );
}

export default BasicModal
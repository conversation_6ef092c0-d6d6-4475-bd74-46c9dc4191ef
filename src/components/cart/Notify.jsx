import React, { useContext, useEffect } from "react";
import { Link } from "gatsby";
import { CartContext } from "../../context/CartContext";
import useCart from "../../hooks/cart/useCart";
import SlideCart from "./SlideCart";

const Notify = () => {
  const { notifications } = useContext(CartContext);
  const hasNotifications = Array.isArray(notifications) && notifications.length;

  return hasNotifications ? (
    <section className="Notify">
      {notifications.map((note) => (
        <Notification key={note.id} {...note} />
      ))}
    </section>
  ) : null;
};

const Notification = ({ id, text, type }) => {
  const { cart, removeNotification } = useCart();

  useEffect(() => {
    const timer = setTimeout(() => {
      removeNotification(id);
    }, 5000);
    return () => clearTimeout(timer);
    // eslint-disable-next-line
  }, []);

  return (
    <article className="Notification Animate ad-notification">
      <div className="Content">
        <div className="Message">
          <div className="Title">
            <h3 className="Text">Your Cart</h3>
            <div className="Icon" onClick={() => removeNotification(id)}>
              <span className="cart-close-icon"></span>
            </div>
          </div>
          <div className="">
            <p className="bc-alert bc-alert--success">{text}</p>
          </div>
          <SlideCart cartType={type} />
          <div className="Actions">
            <div className="flex vertical-middle row">
              <div className="col">
                <Link
                  to="/cart"
                  className="bc-btn button button--secondary"
                  onClick={() => removeNotification(id)}
                >
                  View Cart
                </Link>
              </div>
              <div className="col">
                <a href={cart.checkout_url} className="bc-btn button">
                  Checkout
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </article>
  );
};

export default Notify;

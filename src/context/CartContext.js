import { createContext } from 'react'

export const CartContext = createContext({
  lineItemsLength: 0,
  checkoutLoading: {},
  setCheckoutLoadingState: () => { },
  addToCart: () => { },
  clearCart: () => { },
  removeCartItem: () => { },
  updateCartItem: () => { },
  applyCouponCode: () => { },
  removeCouponCode: () => { },
  removeMultipleCartItems: () => { },
  isCartOperationRunning: false,
  updatingItem: false,
  removing: '',
})

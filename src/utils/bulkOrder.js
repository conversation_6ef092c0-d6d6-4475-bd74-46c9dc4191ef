export const calculateTotal = (orders, orderIndex) => {
  let total = 0;
  orders[orderIndex]?.variants?.forEach((rowData) => {
    total += parseInt(rowData?.requested_qty) || 0;
  });
  return total;
};

export const calculateGrandTotal = (orders) => {
  let grandTotal = 0;
  orders?.forEach((order, index) => {
    grandTotal += calculateTotal(orders, index);
  });
  return grandTotal;
};

export const createPutPayload = (
  representative,
  changedOrders,
  type,
  user,
  orders
) => {
  const { customer_id, customer_name, customer_email, admin_name } = user;
  const payload = {
    customer_id: String(customer_id) || "",
    customer_name: customer_name || "",
    customer_rep_id: null,
    customer_rep_name: representative?.name || "",
    customer_email: customer_email || "",
    customer_rep_email: representative?.email || "",
    created_by: admin_name ? admin_name : customer_name,
    type: type,
    line_items: changedOrders
      .slice()
      .reverse()
      .filter((entry, index, self) => {
        return index === self.findIndex((t) => t.bop_id === entry.bop_id);
      })
      .filter((entry) => entry.changedRows.length > 0)
      .map((entry) => ({
        bop_id: entry.bop_id,
        product_name: entry.product_name,
        total: calculateTotal(orders, entry.orderIndex),
        variants: entry.changedRows.map((row) => ({
          variant_id: row.variant_id,
          bc_variant_id: row.bc_variant_id,
          bc_sku: row.bc_sku,
          bc_upc: row.bc_upc,
          bo_upc: row.bo_upc == null ? null : Number(row.bo_upc),
          flavor: row.flavor,
          po_option: row.po_option,
          requested_qty: Number(row.requested_qty),
        })),
      }))
      .reverse(),
  };
  return payload;
};

export const getFirstPathSegment = (location) => {
  const pathnameArray = location?.pathname?.split("/");
  const modifiedArray = pathnameArray?.filter((item) => item !== "");
  const lastSegment = modifiedArray?.[modifiedArray.length - 1];
  const firstSegment = lastSegment?.split("-")?.[0];
  return firstSegment;
};

export const calculateDetailsPageTotal = (item) => {
  if (!item || !item.variants || item.variants.length === 0) {
    return {
      totalRequestedQty: "-",
      totalFullfilledQty: "-",
      totalBackOrderd: "-",
      totalCancelledQty: "-",
    };
  }

  let totalRequestedQty = 0;
  let totalFullfilledQty = 0;
  let totalBackOrderd = 0;
  let totalCancelledQty = 0;

  item.variants.forEach((rowData) => {
    totalRequestedQty += rowData.requested_qty || 0;
    totalFullfilledQty += rowData.fullfilled_qty || 0;
    totalBackOrderd +=
      rowData.status === "backorder" ? rowData.remaining_qty : 0;
    totalCancelledQty +=
      rowData.status === "cancelled" ? rowData.remaining_qty : 0;
  });

  return {
    totalRequestedQty,
    totalFullfilledQty,
    totalBackOrderd,
    totalCancelledQty,
  };
};

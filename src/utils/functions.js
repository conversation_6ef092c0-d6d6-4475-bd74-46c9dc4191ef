/**
 * convert string to start case where first letter of the
 * each word will be capitalize
 * @param {string} str
 * @returns {string} converted string
 */
export function startCase(str) {
  if (typeof str !== 'string') {
    return new String(str).toLowerCase()
  }

  str = str.replaceAll(/([A-Z-])/g, ' $1')

  let result = str.replace(/\w\S*/g, function (txt) {
    return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
  })

  return result.replaceAll('- ', '')
}

/**
 * Convert string to camelcase type string
 * @param {string} str
 * @returns {string} converted camelcase string
 */
export function camelCase(str) {
  str = str.replaceAll('-', ' ')
  return str
    .replace(/(?:^\w|[A-Z]|\b\w)/g, function (word, index) {
      return index === 0 ? word.toLowerCase() : word.toUpperCase()
    })
    .replace(/\s+/g, '')
}

/**
 * @param {*} data
 * @returns {boolean} Returns true if value is empty, else false.
 *
 * isEmpty(null) => true
 *
 * sEmpty(true) => true
 *
 * isEmpty(1) => true
 *
 * isEmpty([1, 2, 3]) => false
 *
 * isEmpty({ 'a': 1 }) => false
 */
export function isEmpty(data) {
  if (data instanceof Array) return data.length === 0
  if (data instanceof Set) return data.size === 0
  if (data instanceof Map) return data.size === 0
  if (data instanceof Object) return Object.keys(data).length === 0

  return true
}

/**
 * Remove dublicate element from array or array like object
 * @param {array} arrA The array to inspect.
 * @param {*} iteretee (Function): The iteratee invoked per element.
 * @returns {array} Returns the new duplicate free array.
 *
 * uniqBy([2.1, 1.2, 2.3], Math.floor)
 * => [2.1, 1.2]
 *
 * uniqBy([{ 'x': 1 }, { 'x': 2 }, { 'x': 1 }], 'x');
 * => [{ 'x': 1 }, { 'x': 2 }]
 */

export function uniqBy(arrA, iteretee) {
  if (!Array.isArray(arrA)) return []

  let uniq = new Set()
  let arr2return = []
  let eval_val = ''

  if (typeof iteretee === 'function') {
    if (arrA.length === 0) return []

    arrA.forEach((el) => {
      if (!arr2return.includes(el)) {
        eval_val = iteretee.call(null, el)
        if (!uniq.has(eval_val)) {
          arr2return.push(el)
          uniq.add(eval_val)
        }
      }
    })
  } else if (typeof iteretee === 'string' || typeof iteretee === 'number') {
    let key = iteretee
    arrA.forEach((el) => {
      if (el instanceof Object) {
        let isExistkey = el.hasOwnProperty(key)
        if (isExistkey) {
          if (!uniq.has(el[key])) {
            arr2return.push(el)
            uniq.add(el[key])
          }
        } else {
          if (!uniq.has(key)) {
            arr2return.push(el)
            uniq.add(key)
          }
        }
      } else {
        if (arrA.includes(iteretee) && !arr2return.includes(iteretee)) {
          arr2return.push(iteretee)
        }
      }
    })
  }
  return arr2return
}

/**
 * Invokes the iteratee n times, returning an array of the results of each invocation
 * @param {number} n The number of times to invoke iteratee.
 * @param {function} iteratee The function invoked per iteration
 * @returns {array} Returns the array of results.
 *
 * times(3, String)
 * => ['0', '1', '2']
 */
export function times(n, iteratee) {
  const numTimes = parseInt(n)

  if (isNaN(numTimes)) throw new Error('Repeatation count must be integer')

  const arr = []

  for (let i = 0; i < numTimes; i++) {
    if (typeof iteratee === 'function') {
      arr.push(iteratee.apply(null, [i]))
    } else {
      arr.push(iteratee)
    }
  }
  return arr
}

/**
 * Check value in collection or not
 * @param {Array|Object|string} collection The collection to inspect
 * @param {*} value The value to search for.
 * @param {number} fromIndex The index to search from.
 * @returns {boolean} Returns true if value is found, else false.
 *
 * includes([1, 2, 3], 1) => true
 *
 * includes([1, 2, 3], 1, 2) => false
 *
 * includes({ 'a': 1, 'b': 2 }, 1) => true
 *
 * includes('abcd', 'bc') => true
 */

export function includes(collection, value, fromIndex = 0) {
  if (value === '' || value === null || value === undefined) return false

  // let indx = parseInt(fromIndex, 10)

  if (typeof collection === 'object') {
    if (Array.isArray(collection)) {
      if (collection.length === 0) return false

      let arrToCheck = collection.slice(fromIndex)
      return arrToCheck.includes(value)
    } else {
      let objToCheck = Object.values(collection)

      if (objToCheck.length === 0) return false
      return objToCheck.slice(fromIndex).includes(value)
    }
  }

  if (typeof collection === 'string') {
    const re = new RegExp(`${value}`, 'gm')
    return re.test(collection)
  }
}

/**
 * Creates a debounced function that delays invoking func until
 * after wait milliseconds have elapsed since the last time the
 * debounced function was invoked.
 * @param {function} func The function to debounce.
 * @param {number} timeout The number of milliseconds to delay.
 * @returns {function} Returns the new debounced function
 */
export function debounce(func, delay) {
  let timeoutId

  return function (...args) {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => {
      func.apply(this, args)
    }, delay)
  }
}

/**
 * Adds two numbers.
 * @param {number} num1 The first number in an addition.
 * @param {number} num2 The second number in an addition.
 * @returns {number} Returns the total.
 */
export function add(num1, num2) {
  const number1 = Number(num1)
  const number2 = Number(num2)

  if (isNaN(number1) || isNaN(number2)) return 0

  return number1 + number2
}

/**
 * Return rounded number to its uperbound
 * @param {*} num
 * @returns
 */

export function ceil(num) {
  if (isNaN(num)) return NaN
  return Math.ceil(num)
}

/**
 * Gets the first element of array.
 * @param {array} arr  The array to query.
 * @returns {*} Returns the first element of array
 *
 * first([1, 2, 3]) => 1
 *
 * first([]) => undefined
 */
export function first(arr) {
  return Array.isArray(arr) ? (arr.length > 0 ? arr[0] : undefined) : undefined
}

/**
 * Creates an array of unique values that are included in all given arrays
 * @param {array} arrays The arrays to inspect.
 * @returns {array} Returns the new array of intersecting values.
 *
 * intersection([2, 1], [2, 3]) => [2]
 */
export function intersection(arrays) {
  arrays = Array.from(arguments)
  if (!Array.isArray(arrays) || arrays.length === 0) return []
  return arrays.reduce((a, b) => a.filter((c) => b.includes(c)))
}

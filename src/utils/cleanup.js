import { removePOFromSessionStorage } from "./purchaseOrder";
import store from "../redux/store";
import { logout } from "../features/userSlice";
import { resetCart } from "../features/cartSlice";
import { resetCustomer } from "../features/customerSlice";
import { navigate } from "gatsby";

// General cleanup function (existing functionality)
const cleanup = () => {
  // [1] remove session storage product quick-view popup data ...
  sessionStorage.removeItem("visitorData");

  // [2] remove checkout-iframe from ___gatsby DOM element ...
  const checkoutIframe = document.querySelector("#checkout-iframe");

  if (checkoutIframe) {
    checkoutIframe.parentNode.removeChild(checkoutIframe);
  }

  // [3] remove PO items from localStorage data ...
  removePOFromSessionStorage();

  localStorage.removeItem("recentlyViewProductIds");

  localStorage.removeItem("recentlyViewProductIdsBeforeLogin");


  return null;
};

// Authentication cleanup function - SINGLE SOURCE OF TRUTH
export const authCleanup = (options = {}) => {
  const {
    triggerCrossTab = true,
    redirectToLogin = true,
    clearSessionData = true
  } = options;

  console.log('🧹 Starting auth cleanup...');

  // [1] Clear Redux state
  store.dispatch(logout());
  store.dispatch(resetCart());
  store.dispatch(resetCustomer());

  // [2] Clear localStorage auth data
  localStorage.removeItem("persist:root");

  // [3] Clear session data if requested
  if (clearSessionData) {
    const user = store.getState()?.auth?.user;
    if (user?.customer_id) {
      sessionStorage.removeItem(`${user.customer_id}`);
    }
  }

  // [4] Trigger cross-tab logout signal if requested
  if (triggerCrossTab) {
    const logoutSignal = {
      action: 'LOGOUT',
      timestamp: Date.now(),
      source: 'authCleanup'
    };
    localStorage.setItem('auth_action', JSON.stringify(logoutSignal));

    // Remove the signal after a short delay
    setTimeout(() => {
      localStorage.removeItem('auth_action');
    }, 1000);
  }

  // [5] Navigate to login if requested
  if (redirectToLogin && typeof window !== 'undefined') {
    navigate('/login');
  }

  console.log('✅ Auth cleanup completed');
  return true;
};

// Helper function to get token from localStorage
export const getTokenFromStorage = () => {
  if (typeof window === 'undefined') return null;
  try {
    const persistedState = localStorage.getItem('persist:root');
    if (!persistedState) return null;
    const parsed = JSON.parse(persistedState);
    const authState = JSON.parse(parsed.auth || '{}');
    return authState.user?.accessToken || null;
  } catch (error) {
    return null;
  }
};

// Helper function to check if user is authenticated
export const isAuthenticated = () => {
  const token = getTokenFromStorage();
  const reduxUser = store.getState()?.auth?.user;
  return !!(token || reduxUser?.accessToken);
};

export default cleanup;

import { includes } from './functions'
import { formatPrice } from './money'

export const defaultFacetDisplayLimit = 5

export function dataToBreadcrumbsInfo(breadcrumbinfo, pagination) {
  const breadcrumbs = {
    breadcrumbTitle:
      pagination?.['totalResults'] && breadcrumbinfo?.[0]?.['filterValue']
        ? `${pagination?.['totalResults']} RESULTS FOR ${breadcrumbinfo[0][
            'filterValue'
          ].toUpperCase()}`
        : '',
  }

  return breadcrumbs
}

export const LIST_TYPES = ['Category', 'Brand']

export function dataToFacets(data = []) {
  const facets = []

  data.forEach((i) => {
    if (includes(LIST_TYPES, i['label']) && i['type'] === 'list') {
      const facet_values = []

      // crating label, value pair for side facets.
      i['values'].forEach((j) => {
        facet_values.push(j)
      })

      facets.push({
        active: i['active'],
        field: i['field'],
        label: i['label'],
        type: i['type'],
        values: facet_values,
      })
    }

    // when price and type is slider
    if (i['label'] === 'Price' && i['type'] === 'slider') {
      facets.push({
        field: i['field'],
        label: i['label'],
        type: i['type'],
        min: i['range'][0],
        max: i['range'][1],
      })
    }
  })

  return facets
}

export const updateParams = (params, filter_key) => {
  // create copy of object.
  const params_copy = { ...params }

  // remove targeted key from object.
  delete params_copy[filter_key]

  return params_copy
}

export const dataToActiveFilters = (data) => {
  const fitlers = []

  if (data.length) {
    for (let i = 0; i < data.length; i++) {
      // format low and high price for price range...
      if (data[i]['field'] === 'calculated_price') {
        fitlers.push({
          field: data[i]['field'],
          value: `${formatPrice(
            data[i]?.['value']?.['rangeLow'] || 0
          )}-${formatPrice(data[i]?.['value']?.['rangeHigh'] || 0)}`,
          filterLabel: data[i]['filterLabel'],
        })
      }
      // condition for brand, categories etc...
      else {
        fitlers.push({
          field: data[i]['field'],
          value: `${data[i]['value']}`,
          filterLabel: data[i]['filterLabel'],
        })
      }
    }
  }

  return fitlers
}

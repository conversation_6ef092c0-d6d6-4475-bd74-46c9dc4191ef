import format from 'date-fns/format'

export const DATE_FORMAT_PRIMARY = 'd LLL, yyyy'
export const DATE_FORMAT_SEONDARY = "yyyy-MM-dd";

export function formatDate(date, formatPattern) {
  return format(new Date(date), formatPattern);
}

export function formatDateNew(dateStr, toCst = false) {
  if (dateStr) {
    const months = [
      "January",
      "February",
      "March",
      "April",
      "May",
      "June",
      "July",
      "August",
      "September",
      "October",
      "November",
      "December",
    ];
    let date = new Date(dateStr);

    if (toCst) {
      const options = { timeZone: "America/Chicago" };
      const cstDateStr = date.toLocaleString("en-US", options);
      date = new Date(cstDateStr);
    }

    const day = date.getDate();
    const monthIndex = date.getMonth();
    const year = date.getFullYear().toString();

    return `${day} ${months[monthIndex]} ${year}`;
  } else {
    return "";
  }
}

export const getCurrentDate = () => {
  const currentDate = new Date();

  const year = currentDate.getFullYear();

  // Months are zero-based
  const month = currentDate.getMonth() + 1;

  const day = currentDate.getDate();

  return `${day}-${month}-${year}`;
};

function convertUTCToCST(utcDateStr) {
  // Parse the UTC date string into a Date object
  const utcDate = new Date(utcDateStr);

  // Get the UTC offset for Central Time (CST/CDT), which is -6 hours CST or -5 hours CDT
  const options = {
    timeZone: "America/Chicago",
    hour12: false,
    timeZoneName: "short",
  };
  const cstDateStr = utcDate.toLocaleString("en-US", options);

  return cstDateStr;
}

// Example usage
const utcDateStr = "Thu, 17 Apr 2025 01:29:27 +0000";
console.log(convertUTCToCST(utcDateStr));

import { createOption } from "./components";
import * as Yup from "yup";
import {
  requiredMsg,
  requiredSelectMsg,
  requiredEmailMsg,
  emailRegExp,
  emailRegMsg,
} from "../utils/form";
import { getUser } from "./auth";
const user = getUser();
export const review_form_initial_values = {
  rating: "",
  name: user ? user.customer_name : "",
  email: user ? user.customer_email : "",
  reviewSubject: "",
  comments: "",
};

export const RATING_OPTIONS = [
  createOption(0, "Select Rating"),
  createOption(1, "1 star (worst)"),
  createOption(2, "2 stars"),
  createOption(3, "3 stars (average)"),
  createOption(4, "4 stars"),
  createOption(5, "5 stars (best)"),
];

export const review_for_validation = Yup;

export const review_validation_schema = Yup.object().shape({
  rating: Yup.string().required(requiredSelectMsg("rating")),
  reviewSubject: Yup.string().required(requiredMsg("review subject")),
  comments: Yup.string().required(requiredMsg("comments")),
  name: Yup.string().required(requiredMsg("name")),
  email: Yup.string()
    .matches(emailRegExp, emailRegMsg)
    .email(requiredEmailMsg())
    .required(requiredEmailMsg("Email Address")),
});

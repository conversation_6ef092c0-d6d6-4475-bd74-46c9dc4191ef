export function parseCurrencyString(str) {
  const parsedStr = str || str === 0 ? str.toString().replace(/\D/g, "") : "";

  return parsedStr;
}

export function formatInputNumber(n, symbol = "$") {
  if (n !== null) {
    const strNumber = n.toString();
    const isNegative = strNumber.includes("-");
    const hasDecimal = strNumber.includes(".");
    const fullNumber = hasDecimal ? strNumber.split(".")[0] : n;
    const decimal = hasDecimal ? strNumber.split(".")[1] : "";
    const res = parseCurrencyString(fullNumber)
      .replace("-", "")
      .replace(/\B(?=(\d{3})+(?!\d))/g, ",");

    const formattedDecimal = hasDecimal
      ? `.${decimal.padEnd(2, "0").substring(0, 2)}`
      : ".00";

    const negativeRes = isNegative ? `-${symbol}${res}` : `${symbol}${res}`;

    return `${negativeRes}${formattedDecimal}`;
  }
}
export function formatPrice(n = "") {
  return isNaN(!n) ? "-" : formatInputNumber(n);
}

export function calculatePrices(sale_price, price, retail_price) {
  let result = {
    defaultPrice: {
      value: 0,
      class: "",
    },
    retailPrice: {
      value: 0,
      class: "",
    },
    salePrice: {
      value: 0,
      class: "",
    },
  };

  // Case - 1: Show only sale price when sale price is greater than all ...
  if (sale_price !== 0 && sale_price >= price && sale_price >= retail_price) {
    result["salePrice"]["value"] = sale_price;
  }

  // Case - 2: Show all price when sale price is less than retail and base price ...
  if (sale_price !== 0 && sale_price <= price && sale_price <= retail_price) {
    result["defaultPrice"] = {
      value: price,
      class: "strike-price",
    };
    result["retailPrice"] = {
      value: retail_price,
      class: "strike-price",
    };
    result["salePrice"]["value"] = sale_price;
  }

  // Case - 3: Show retail price and sale price when retail price is greater than sale price ...
  if (sale_price !== 0 && sale_price >= price && sale_price < retail_price) {
    result["retailPrice"] = {
      value: retail_price,
      class: "strike-price",
    };
    result["salePrice"]["value"] = sale_price;
  }

  // Case - 4: Show base price and sale price when base price is greater than sale price ...
  if (sale_price !== 0 && sale_price <= price && sale_price > retail_price) {
    result["defaultPrice"] = {
      value: price,
      class: "strike-price",
    };
    result["salePrice"]["value"] = sale_price;
  }

  // Case - 5: Show retail price and sale price when retail price is greater than sale price ...
  if (sale_price !== 0 && sale_price >= price && sale_price < retail_price) {
    result["retailPrice"] = {
      value: retail_price,
      class: "strike-price",
    };
    result["salePrice"]["value"] = sale_price;
  }

  // Case - 6: Show default price and sale price when retail price is zero...
  if (sale_price !== 0 && sale_price < price && sale_price > retail_price) {
    result["defaultPrice"] = {
      value: price,
      class: "strike-price",
    };
    result["salePrice"]["value"] = sale_price;
  }

  // Case - 7: Show default price when retail price and default price is greater than sale price ...
  if (sale_price !== 0 && sale_price < price && sale_price < retail_price) {
    result["defaultPrice"] = {
      value: price,
      class: "strike-price",
    };
    result["retailPrice"] = {
      value: retail_price,
      class: "strike-price",
    };
    result["salePrice"]["value"] = sale_price;
  }

  // Case - 8: When default price is greater than retail price ...
  if (sale_price === 0 && price >= retail_price) {
    result["defaultPrice"] = {
      value: price,
      class: "current-price",
    };
  }

  // Case - 9: When default price is less than retail price ...
  if (sale_price === 0 && price < retail_price) {
    result["retailPrice"] = {
      value: retail_price,
      class: "strike-price",
    };
    result["defaultPrice"] = {
      value: price,
      class: "current-price",
    };
  }

  // Case - 10: When only price is available ...
  if (price && !sale_price && !retail_price) {
    result["defaultPrice"]["value"] = price;
  }

  return result;
}

/**
 * Calculates the minimum price among the given prices.
 * other than 0.
 * If no prices are given, returns 0.
 *
 * @param {number} price - The regular price.
 * @param {number} sale_price - The sale price.
 * @param {number} retail_price - The retail price.
 * @returns {number} The minimum price.
 */
export const getMinimumPrice = (price, sale_price, retail_price) => {
  const prices = [price, sale_price, retail_price].filter(
    (p) => typeof p === "number" && p !== 0
  );

  return prices.length > 0 ? Math.min(...prices) : 0;
};

export const isPromoProduct = (price) => {
  // convert to number if not
  const numPrice = Number(price);

  return (
    numPrice === 0 ||
    numPrice === null ||
    numPrice === undefined ||
    numPrice === "" ||
    numPrice === "0"
  );
};

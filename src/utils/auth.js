import store from '../redux/store'

export function getUser() {
  const user = store.getState()?.['auth']?.['user']
  return user
}

export function isUserLoggedIn(domain) {
  const user = getUser()
  return user?.customer_email && user?.customer_id ? true : false
}


export function getCustomerId(){
  const user = getUser()
  return user?.customer_email && user?.customer_id ? user?.customer_id : false
}
import React, { Component } from 'react'

export default class IconSupport extends Component {
  render() {
    return (
      <svg
        width="22"
        height="23"
        viewBox="0 0 22 23"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M5 17.4029C5 18.134 4.4 18.7321 3.66667 18.7321H2.33333C1.6 18.7321 1 18.134 1 17.4029V13.4153C1 12.6842 1.6 12.0861 2.33333 12.0861H3.66667C4.4 12.0861 5 12.6842 5 13.4153V17.4029Z"
          stroke="#052244"
          strokeWidth="1.5"
          strokeMiterlimit="10"
          strokeLinecap="round"
          strokeLinejoin="round"
        ></path>
        <path
          d="M21 17.4029C21 18.134 20.4 18.7321 19.6667 18.7321H18.3333C17.6 18.7321 17 18.134 17 17.4029V13.4153C17 12.6842 17.6 12.0861 18.3333 12.0861H19.6667C20.4 12.0861 21 12.6842 21 13.4153V17.4029Z"
          stroke="#052244"
          strokeWidth="1.5"
          strokeMiterlimit="10"
          strokeLinecap="round"
          strokeLinejoin="round"
        ></path>
        <path
          d="M1 13.4153V11.4215C1 5.90529 5.46667 1.45244 11 1.45244C16.5333 1.45244 21 5.90529 21 11.4215V13.4153"
          stroke="#052244"
          strokeWidth="1.5"
          strokeMiterlimit="10"
          strokeLinecap="round"
          strokeLinejoin="round"
        ></path>
        <path
          d="M11.6663 20.726H10.333V21.3906H11.6663V20.726Z"
          stroke="#052244"
          strokeWidth="1.5"
          strokeMiterlimit="10"
          strokeLinecap="round"
          strokeLinejoin="round"
        ></path>
        <path
          d="M21.0003 12.0861V18.7321L14.3337 21.3906H11.667"
          stroke="#052244"
          strokeWidth="1.5"
          strokeMiterlimit="10"
          strokeLinecap="round"
          strokeLinejoin="round"
        ></path>
      </svg>
    )
  }
}

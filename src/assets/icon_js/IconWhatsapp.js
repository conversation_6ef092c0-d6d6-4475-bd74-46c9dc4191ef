import React, { Component } from "react";

export default class IconWhatsapp extends Component {
  render() {
    return (
      <svg
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        width="896"
        height="1024"
        viewBox="0 0 896 1024"
      >
        <title></title>
        <g id="icomoon-ignore"></g>
        <path
          fill="#000"
          d="M764.628 193.514c-84.011-84.107-195.731-130.445-314.763-130.494-245.258 0-444.872 199.598-444.968 444.931-0.034 78.426 20.455 154.974 59.396 222.452l-63.127 230.577 235.883-61.876c64.991 35.449 138.167 54.134 212.639 54.159h0.181c0.014 0-0.011 0 0.004 0 245.232 0 444.859-199.618 444.962-444.957 0.043-118.896-46.198-230.69-130.207-314.793zM449.867 878.116h-0.15c-66.361-0.025-131.451-17.856-188.235-51.551l-13.503-8.014-139.977 36.717 37.363-136.474-8.794-13.995c-37.020-58.883-56.573-126.941-56.544-196.822 0.079-203.911 165.992-369.805 369.99-369.805 98.782 0.031 191.642 38.552 261.468 108.458s108.258 162.827 108.218 261.647c-0.083 203.928-165.99 369.84-369.836 369.84z"
        ></path>
        <path
          fill="#000"
          d="M652.731 601.127c-11.117-5.565-65.781-32.458-75.971-36.171-10.191-3.71-17.603-5.565-25.014 5.565-7.411 11.128-28.719 36.173-35.205 43.595-6.484 7.416-12.972 8.35-24.088 2.78-11.117-5.565-46.942-17.304-89.408-55.182-33.054-29.483-55.366-65.889-61.854-77.023-6.484-11.128-0.69-17.145 4.877-22.69 5.001-4.983 11.119-12.985 16.679-19.478 5.558-6.49 7.411-11.132 11.117-18.546 3.706-7.422 1.855-13.915-0.927-19.478-2.78-5.565-25.014-60.29-34.278-82.555-9.027-21.676-18.192-18.74-25.018-19.085-6.475-0.323-13.897-0.39-21.308-0.39s-19.458 2.783-29.647 13.912c-10.191 11.132-38.915 38.032-38.915 92.751 0 54.727 39.84 107.595 45.399 115.013 5.558 7.424 78.399 119.72 189.929 167.879 26.526 11.457 47.236 18.297 63.382 23.419 26.635 8.464 50.872 7.268 70.029 4.405 21.362-3.192 65.781-26.895 75.045-52.863 9.266-25.973 9.266-48.237 6.484-52.872-2.778-4.637-10.191-7.42-21.308-12.987z"
        ></path>
      </svg>
    );
  }
}

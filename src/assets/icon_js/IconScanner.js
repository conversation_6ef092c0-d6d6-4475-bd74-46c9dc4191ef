import React, { Component } from 'react'
export default class IconScanner extends Component {
  render() {
    return (
      <svg version="1.1" xmlns="http://www.w3.org/2000/svg" width="928" height="1024" viewBox="0 0 928 1024">
        <title></title>
        <g id="icomoon-ignore">
        </g>
        <path fill="none" strokeLinejoin="round" strokeLinecap="round" strokeMiterlimit="4" strokeWidth="64" stroke="#09254A" d="M37.333 384v-106.667c0-106.24 85.76-192 192-192h106.667"></path>
        <path fill="none" strokeLinejoin="round" strokeLinecap="round" strokeMiterlimit="4" strokeWidth="64" stroke="#09254A" d="M592 85.333h106.667c106.24 0 192 85.76 192 192v106.667"></path>
        <path fill="none" strokeLinejoin="round" strokeLinecap="round" strokeMiterlimit="4" strokeWidth="64" stroke="#09254A" d="M890.667 682.667v64c0 106.24-85.76 192-192 192h-64"></path>
        <path fill="none" strokeLinejoin="round" strokeLinecap="round" strokeMiterlimit="4" strokeWidth="64" stroke="#09254A" d="M336 938.667h-106.667c-106.24 0-192-85.76-192-192v-106.667"></path>
        <path fill="none" strokeLinejoin="round" strokeLinecap="round" strokeMiterlimit="4" strokeWidth="64" stroke="#09254A" d="M677.333 426.667v213.333c0 85.333-42.667 128-128 128h-170.667c-85.333 0-128-42.667-128-128v-213.333c0-85.333 42.667-128 128-128h170.667c85.333 0 128 42.667 128 128z"></path>
        <path fill="none" strokeLinejoin="round" strokeLinecap="round" strokeMiterlimit="4" strokeWidth="64" stroke="#09254A" d="M762.667 512h-597.333"></path>
      </svg>
    )
  }
}

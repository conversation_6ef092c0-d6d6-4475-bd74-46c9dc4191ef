import React, { Component } from 'react'

export default class IconCall extends Component {
  render() {
    return (
      <svg
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        width="1024"
        height="1024"
        viewBox="0 0 1024 1024"
      >
        <title></title>
        <g id="icomoon-ignore"></g>
        <path d="M809.283 634.332c-20.964-21.828-46.25-33.499-73.049-33.499-26.583 0-52.085 11.454-73.914 33.283l-68.295 68.078c-5.619-3.026-11.238-5.835-16.641-8.645-7.78-3.89-15.129-7.564-21.396-11.454-63.972-40.631-122.109-93.581-177.869-162.092-27.015-34.147-45.17-62.892-58.353-92.068 17.722-16.209 34.147-33.067 50.14-49.276 6.051-6.051 12.103-12.319 18.154-18.37 45.386-45.386 45.386-104.171 0-149.557l-59.001-59.001c-6.7-6.7-13.616-13.616-20.099-20.532-12.967-13.4-26.583-27.231-40.631-40.199-20.964-20.748-46.034-31.77-72.401-31.77s-51.869 11.022-73.482 31.77c-0.216 0.216-0.216 0.216-0.432 0.432l-73.482 74.13c-27.664 27.664-43.441 61.379-46.899 100.497-5.187 63.108 13.4 121.893 27.664 160.363 35.012 94.445 87.313 181.975 165.333 275.772 94.662 113.032 208.558 202.29 338.664 265.182 49.708 23.557 116.058 51.437 190.188 56.192 4.539 0.216 9.293 0.432 13.616 0.432 49.924 0 91.852-17.938 124.703-53.598 0.216-0.432 0.648-0.648 0.864-1.081 11.238-13.616 24.206-25.935 37.821-39.118 9.293-8.861 18.803-18.154 28.096-27.88 21.396-22.261 32.634-48.195 32.634-74.778 0-26.799-11.454-52.518-33.283-74.13l-118.651-119.083zM886.654 861.908c-0.216 0-0.216 0.216 0 0-8.429 9.077-17.074 17.29-26.367 26.367-14.048 13.4-28.312 27.448-41.712 43.224-21.828 23.341-47.547 34.363-81.262 34.363-3.242 0-6.7 0-9.942-0.216-64.188-4.106-123.838-29.176-168.575-50.573-122.325-59.217-229.738-143.289-318.996-249.837-73.698-88.826-122.974-170.953-155.608-259.131-20.099-53.814-27.448-95.742-24.206-135.293 2.161-25.286 11.887-46.25 29.825-64.188l73.698-73.698c10.59-9.942 21.828-15.345 32.851-15.345 13.616 0 24.638 8.213 31.554 15.129 0.216 0.216 0.432 0.432 0.648 0.648 13.183 12.319 25.719 25.070 38.902 38.686 6.7 6.916 13.616 13.832 20.532 20.964l59.001 59.001c22.909 22.909 22.909 44.089 0 66.998-6.268 6.268-12.319 12.535-18.587 18.587-18.154 18.587-35.444 35.876-54.247 52.734-0.432 0.432-0.864 0.648-1.081 1.081-18.587 18.587-15.129 36.741-11.238 49.060 0.216 0.648 0.432 1.297 0.648 1.945 15.345 37.173 36.957 72.185 69.807 113.896l0.216 0.216c59.65 73.482 122.541 130.754 191.917 174.627 8.861 5.619 17.938 10.158 26.583 14.48 7.78 3.89 15.129 7.564 21.396 11.454 0.864 0.432 1.729 1.081 2.593 1.513 7.348 3.674 14.264 5.403 21.396 5.403 17.938 0 29.176-11.238 32.851-14.912l73.914-73.914c7.348-7.348 19.019-16.209 32.634-16.209 13.4 0 24.422 8.429 31.122 15.777 0.216 0.216 0.216 0.216 0.432 0.432l119.083 119.083c22.261 22.044 22.261 44.737 0.216 67.646z"></path>
        <path d="M553.394 243.583c56.624 9.509 108.061 36.309 149.124 77.372s67.646 92.5 77.372 149.124c2.377 14.264 14.696 24.206 28.744 24.206 1.729 0 3.242-0.216 4.971-0.432 15.993-2.593 26.583-17.722 23.99-33.715-11.671-68.511-44.089-130.97-93.581-180.462s-111.951-81.91-180.462-93.581c-15.993-2.593-30.905 7.997-33.715 23.773s7.564 31.122 23.557 33.715z"></path>
        <path d="M1022.811 451.708c-19.235-112.816-72.401-215.474-154.095-297.168s-184.352-134.86-297.168-154.095c-15.777-2.81-30.689 7.997-33.499 23.773-2.593 15.993 7.997 30.905 23.99 33.715 100.713 17.074 192.565 64.837 265.614 137.67 73.049 73.049 120.596 164.901 137.67 265.614 2.377 14.264 14.696 24.206 28.744 24.206 1.729 0 3.242-0.216 4.971-0.432 15.777-2.377 26.583-17.506 23.773-33.283z"></path>
      </svg>
    )
  }
}

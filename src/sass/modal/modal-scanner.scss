.scanner-modal-wrapper {
    width: 1000px;
    max-width: 100%;
}

.order-form-popup {
    .bc-cart {
        .cart-col-name {
            width: calc(100% - 340px);

            @include tablet-small {
                padding: 0;
            }

            @include mobile {
                width: calc(100% - 140px);
                padding-left: 10px;
            }

            @include x-small {
                width: 100%;
            }
        }

        .cart-item-lable {
            display: none;
        }
    }

    .bc-cart-item {
        @include justify-content(flex-start);
    }

    .cart-bottom-action-section {
        margin-bottom: 0;
        padding-bottom: 0;
        border-bottom: none;

        .bc-cart-actions {
            margin-top: 0;
        }
    }
}

.order-form-popup-item {

    +.order-form-popup-item {
        border-top: 1px solid $border-color;
        padding-top: 10px;
        margin-top: 10px;
    }

    >.form-check-input {
        display: none;
        width: 0;
        height: 0;
        opacity: 0;
        visibility: hidden;

        &:checked {
            +.form-check-label {
                .cart-item-mark {
                    .form-label-facet {
                        .checkbox-icon {
                            &:after {
                                opacity: 1;
                            }
                        }
                    }
                }
            }
        }
    }

    .cart-invertory-count {
        @include border-radius(4px);
        color: $success-color;
        font-weight: 600;
        font-size: 14px;
        line-height: 1;

        &.bc-cart-item-error {
            color: $warning-color;
        }
    }

    .item-zero-inventory {
        border: none;
    }
}
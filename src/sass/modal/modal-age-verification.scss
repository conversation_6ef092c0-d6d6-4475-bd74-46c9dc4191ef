.quickview-age-popup {
    width: 540px;
    max-width: 100%;
}

.age-popup {

    .main-content {
        border: 1px solid #ddd;
        width: 100%;
        border-radius: 5px;
        padding: 10px;
        box-sizing: border-box;

        .main-box {
            max-width: 400px;
            width: 100%;
            margin: 0 auto;

            .logo-pan {
                margin-bottom: 20px;
                img {
                    width: 250px;
                    max-width: 100%;

                    @include mobile{
                        max-width: 200px;
                    }
                }
            }

            .age-image {
                margin-bottom: 20px;
            }

            .text-panel {
                font-size: 12px;
                color: #333;
                line-height: 16px;
                text-align: center;
                margin-bottom: 20px;
            }

            .text-panel-bottom{
                font-size: 12px;
                font-weight: 600;
                color: black;
                line-height: 16px;
                text-align: center;
                margin-bottom: 20px;
                
                .green-text {
                    color: #3f9000;
                }
            }
            
            .blue-text {
                margin-bottom: 20px;
                text-align: center;
                color: #00588e;
                font-size: 14px;
            }

            .age-button {
                margin-bottom: 20px;
                a {
                    font-size: 16px;
                    color: #fff;
                    border-radius: 5px;
                    display: inline-block;
                    padding: 10px 25px;

                    @include mobile{
                        width: 100%;
                    }

                    + a{
                        margin-left: 15px;

                        @include mobile{
                            margin-left: 0;
                            margin-top: 10px;
                        }
                    }
                }

                .green-button {
                    background: #4fad05;
                    border-bottom: 3px solid #3f9000;
                    
                    cursor: pointer;

                    &:hover {
                        background: #3f9000;
                        border-bottom: 3px solid #4fad05;
                    }
                }

                .blue-button {
                    background: #0063a0;
                    border-bottom: 3px solid #04466e;

                    &:hover {
                        background: #04466e;
                        border-bottom: 3px solid #0063a0
                    }

                }
            }

            .age-social-pan {
                clear: both;
                margin-bottom: 20px;
                text-align: center;

                .social-icon-list{
                    margin: 0;
                    list-style: none;
                    @include justify-content(center);
                }
            }
        }
    }

}
// Fraud Alert Popup Styles
.fraud-alert-popup {
  .fraud-alert-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    padding: 20px;
  }

  .fraud-alert-content {
    max-width: 500px;
    width: 100%;
  }

  .fraud-alert-box {
    background: linear-gradient(135deg, #ffebee 0%, #fce4ec 100%);
    border: 4px solid #ff0000;
    border-radius: 20px;
    padding: 40px 30px;
    text-align: center;
    position: relative;
    box-shadow: 0 10px 30px rgba(255, 0, 0, 0.3);
    
    // Background pattern with exclamation marks
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-image: 
        radial-gradient(circle at 20% 20%, rgba(255, 0, 0, 0.1) 2px, transparent 2px),
        radial-gradient(circle at 80% 80%, rgba(255, 0, 0, 0.1) 2px, transparent 2px),
        radial-gradient(circle at 40% 60%, rgba(255, 0, 0, 0.1) 2px, transparent 2px),
        radial-gradient(circle at 60% 40%, rgba(255, 0, 0, 0.1) 2px, transparent 2px);
      background-size: 50px 50px, 60px 60px, 40px 40px, 70px 70px;
      border-radius: 16px;
      pointer-events: none;
      z-index: 1;
    }

    // Add exclamation mark pattern
    &::after {
      content: '! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! !';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      color: rgba(255, 0, 0, 0.1);
      font-size: 14px;
      font-weight: bold;
      line-height: 20px;
      word-spacing: 15px;
      overflow: hidden;
      pointer-events: none;
      z-index: 1;
      padding: 20px;
      border-radius: 16px;
    }
  }

  .fraud-alert-icon {
    margin-bottom: 20px;
    position: relative;
    z-index: 2;
    
    svg {
      filter: drop-shadow(0 4px 8px rgba(255, 0, 0, 0.3));
    }
  }

  .fraud-alert-title {
    font-size: 32px;
    font-weight: 900;
    color: #ff0000;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
    letter-spacing: 2px;
    position: relative;
    z-index: 2;
  }

  .fraud-alert-message {
    font-size: 18px;
    font-weight: 700;
    color: #000000;
    line-height: 1.3;
    margin-bottom: 20px;
    position: relative;
    z-index: 2;
  }

  .fraud-alert-phone {
    font-size: 24px;
    font-weight: 900;
    color: #ff0000;
    margin-bottom: 10px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 2;
  }

  .fraud-alert-confirm {
    font-size: 18px;
    font-weight: 700;
    color: #000000;
    margin-bottom: 30px;
    position: relative;
    z-index: 2;
  }

  .fraud-alert-button {
    position: relative;
    z-index: 2;
    
    .acknowledge-button {
      background: linear-gradient(135deg, #ff0000 0%, #cc0000 100%);
      color: white;
      border: none;
      padding: 15px 30px;
      font-size: 16px;
      font-weight: 700;
      border-radius: 25px;
      cursor: pointer;
      transition: all 0.3s ease;
      text-transform: uppercase;
      letter-spacing: 1px;
      box-shadow: 0 4px 15px rgba(255, 0, 0, 0.3);
      
      &:hover {
        background: linear-gradient(135deg, #cc0000 0%, #990000 100%);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(255, 0, 0, 0.4);
      }
      
      &:active {
        transform: translateY(0);
        box-shadow: 0 2px 10px rgba(255, 0, 0, 0.3);
      }
    }
  }
}

// Mobile responsiveness
@media (max-width: 768px) {
  .fraud-alert-popup {
    .fraud-alert-container {
      padding: 10px;
    }
    
    .fraud-alert-box {
      padding: 30px 20px;
    }
    
    .fraud-alert-title {
      font-size: 24px;
    }
    
    .fraud-alert-message {
      font-size: 16px;
    }
    
    .fraud-alert-phone {
      font-size: 20px;
    }
    
    .fraud-alert-confirm {
      font-size: 16px;
    }
  }
}

@media (max-width: 480px) {
  .fraud-alert-popup {
    .fraud-alert-box {
      padding: 25px 15px;
    }
    
    .fraud-alert-title {
      font-size: 20px;
      letter-spacing: 1px;
    }
    
    .fraud-alert-message {
      font-size: 14px;
    }
    
    .fraud-alert-phone {
      font-size: 18px;
    }
    
    .fraud-alert-confirm {
      font-size: 14px;
    }
    
    .fraud-alert-button {
      .acknowledge-button {
        padding: 12px 25px;
        font-size: 14px;
      }
    }
  }
}

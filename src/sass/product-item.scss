.product-list-block {
    .section-title {
        @include mobile {
            text-align: left;
        }
    }
}

.product-list {
    @include display-flex(flex);
    @include flex-wrap(wrap);
    // @include align-item(flex-start);
    @include row;

    @include mobile {
        margin-left: -6px;
        margin-right: -6px;
    }

    .slick-slider {
        .slick-arrow {
            top: -50px;
            width: 24px;
            height: 24px;
            transform: none;
            opacity: 0.8;

            @include mobile {
                width: 18px;
                height: 18px;
                top: -35px;
            }

            &.slick-next {
                right: 10px;
            }

            &.slick-prev {
                left: auto;
                right: 40px;

                @include mobile {
                    right: 30px;
                }
            }

            &:after {
                background-size: 70% auto;
            }

            &:hover {
                opacity: 1;
            }
        }
    }
}

.product-item {
    @include col;
    margin-bottom: 24px;

    @include mobile {
        padding-left: 6px;
        padding-right: 6px;
        margin-bottom: 12px;
    }

    &:hover {
        .product-action-overlay {
            opacity: 1;
        }
    }

    .product-price-wrap {
        text-align: center;

        .product-price-item {
            font-weight: 600;
        }
    }
}

.slick-slider {
    .product-item {
        margin-bottom: 0;
    }
}

.product-item-detail {
    margin-top: 20px;
}

.product-item-name {
    font-size: 14px;
    font-weight: $medium;
    line-height: 20px;
    margin-bottom: 15px;

    @include mobile {
        font-size: 12px;
        line-height: 1.5;
    }

    a {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
}

.product-item-inner {
    border: 1px solid $border-color;
    padding: 15px;
    @include border-radius(5px);
    height: 100%;

    @include mobile {
        padding: 12px;
    }
}

.card-preorder-tag {
    background: #7000CF;
    color: $white-color;
    padding: 5px 8px;
    line-height: 1;
    font-size: 12px;
    @include border-radius(4px);
    display: inline-block;
    margin-bottom: 5px;
    
}

.card-badges-section{
    position: absolute;
    top: 10px;
    left: 10px;

    p{
        margin-bottom: 0;
    }
}

.card-badge {
    padding: 5px 8px;
    line-height: 1;
    font-size: 12px;
    @include border-radius(4px);
    display: inline-block;
    margin-bottom: 5px;
    text-transform: uppercase;
}

.product-detail-badges-section{
    position: absolute;
    top: 10px;
    left: 10px;
    z-index: 5;

    p{
        margin-bottom: 0;
    }
}

.product-item-image {
    position: relative;

    .product-action-overlay {
        position: absolute;
        top: 50%;
        left: 50%;
        width: 100%;
        text-align: center;
        @include transform(translate(-50%, -50%));
        opacity: 0;
        @include transition(opacity 0.4s ease);

        @include tablet {
            display: none !important;
        }

        div {
            +div {
                margin-top: 15px;
            }
        }

        .button {
            min-width: 80%;
        }
    }

    img {
        // display: inline-block;
        // object-fit: cover;
        max-width: 100%;
        max-height: 100%;
    }

    .bc-product-card__featured-image {
        aspect-ratio: 1/1.20;
        overflow: hidden;
        @include display-flex(flex);
        @include align-item(center);
        @include justify-content(center);
        @include flex-wrap(wrap);
    }
    
}

.product-price-item {
    font-size: 16px;
    color: $body-font-color;
    margin-left: -5px;
    margin-right: -5px;
    margin-bottom: 0;
    font-weight: 600;

    @include mobile {
        margin-left: -3px;
        margin-right: -3px;
    }

    span {
        padding: 0 5px;

        @include mobile {
            padding: 0 3px;
        }
    }
}

.strike-price {
    text-decoration: line-through;
    font-size: 14px;
}

.current-price {
    color: $primary-color;
}

.price-login-link {
    font-size: 14px;
    text-decoration: underline;

    @include mobile {
        font-size: 12px;
    }
}

.signin-link-product-card{
    display: none;
}
.product-item {
    .signin-link-common{
        display: none;
    }
    .signin-link-product-card{
        display: inline-block;
    }
    .price-login-link {
        text-decoration: none;

        @include mobile {
            padding: 0;
        }

        strong {
            padding: 5px 15px;
            text-decoration: none;
            cursor: pointer;
            line-height: 19px;
            border-radius: 4px;
            overflow: hidden;
            font-size: 13px;
            border: 1px solid #ddd;
            display: inline-block;
            font-weight: 600;

            &:hover {
                background: $primary-color;
                color: $white-color;
            }
        }
    }
}

.card-review {
    .review-star {
        margin-top: 10px;

        span {
            width: 18px;
            height: 18px;

            +span {
                margin-left: 0;
            }
        }
    }
}

.short-description {
    display: none;
    font-size: 12px;
    line-height: 22px;
}

.page-content{
    width: 100%;
}
.page-sidebar {
    +.page-content {
        .style-list-view {
            .product-item {
                width: 50%;
                margin-bottom: 20px;

                .product-price-wrap {
                    text-align: left;
                }
            }

            .product-item-inner {
                @include display-flex(flex);
                border: 1px solid $section-border-color;
            }

            .product-item-image {
                width: 200px;
            }

            .short-description {
                display: block;
            }

            .product-item-detail {
                width: calc(100% - 200px);
                margin: 0;
                padding: 20px;
                text-align: left;
            }

            .product-price-wrap {
                margin-top: 30px;
            }
        }
    }
}

.style-list-view {
    .product-price-item {
        text-align: center;
    }
}
.cc-container {
    position: fixed;
    overflow: hidden;
    box-sizing: border-box;
    font-family: Helvetica, Calibri, Arial, sans-serif;
    font-size: 16px;
    line-height: 1.5em;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: nowrap;
    flex-wrap: nowrap;
    z-index: 9999999999999999 !important;
    bottom: 0;
    color: rgb(255, 255, 255);
    background-color: rgb(0, 99, 160);
    width: 100%;
    padding: 1.2em 1.8em;
    align-items: center;

    .cc-message {
        flex: 1;
    }

    .cc-link {
        color: rgb(255, 255, 255);
        padding: .2em;
        text-decoration: underline;

        &:visited {
            color: rgb(255, 255, 255);
            padding: .2em;
            text-decoration: underline;
        }
    }

    .cc-okbtn {
        display: -ms-flexbox;
        display: flex;
        -ms-flex-align: center;
        align-items: center;
        -ms-flex-line-pack: justify;
    }

    .cc-btn {
        display: block;
        padding: 0.4em 3.8em;
        font-size: .9em;
        font-weight: 700;
        border-width: 2px;
        border-style: solid;
        text-align: center;
        white-space: nowrap;
        color: rgb(255, 255, 255);
        border-color: transparent;
        background-color: rgb(80, 173, 6);
        transition: color .15s ease;

        &:hover {
            color: rgb(255, 255, 255);
            background-color: rgb(93 199 9);
        }
    }

}
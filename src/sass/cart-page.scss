.bc-cart-header {
    @include display-flex(flex);
    @include align-item(center);
    @include flex-wrap(nowrap);
    text-transform: uppercase;
    font-weight: $semi-bold;
    font-size: 14px;
    margin-bottom: 10px;
    background: #eff6ff;
    color: $primary-color;
    @include border-radius(4px);
    @include justify-content(space-between);

    @include tablet-small {
        display: none;
    }

    @include tablet-small {
        margin-bottom: 20px;
    }

    >div {
        padding: 15px;
    }
}

.cart-item-inner {
    @include mobile-small {
        @include display-flex(flex);
        @include align-item(center);
    }
}

.cart-item-lable {
    display: none;

    @include tablet-small {
        display: block;
        text-align: left;
        margin-bottom: 10px;
        font-size: 14px;
    }

    @include mobile-small {
        margin-bottom: 0;
        width: 100px;
        font-size: 12px;
    }
}


.item-zero-inventory {
    &.bc-cart-item {
        &::after {
            @include box-shadow(0px 0px 0 2px $warning-color);
        }
    }
}

.item-shipping-restriction {
    &.bc-cart-item {
        &::after {
            @include box-shadow(0px 0px 0 2px $warning-color);
        }
    }
}

.item-not-purchasable {
    &.bc-cart-item {
        &::after {
            @include box-shadow(0px 0px 0 2px $warning-color);
        }
    }
}

.item-less-inventory {
    &.bc-cart-item {
        &::after {
            @include box-shadow(0px 0px 0 2px #f0932b);
        }
    }
}

.item-less-minpurchaseqty {
    &.bc-cart-item {
        &::after {
            @include box-shadow(0px 0px 0 2px #f9ca24);
        }
    }
}

.shipping-restriction-label {
    position: absolute;
    margin: 0;
    font-size: 13px;
    font-weight: normal;
    line-height: 1;
    padding: 8px 10px;
    right: 0;
    top: 8px;
    color: $warning-color;
    font-weight: $semi-bold;
    background: #FFF0F0;
    border: 2px solid $warning-color;

    @include mobile{
        padding: 6px 8px;
        font-size: 10px;
        left: 0;
        right: auto;
    }
}

.bc-cart-item-error {
    color: red;
}

.error {
    border: 1px solid red;
}

.bc-cart-item {
    @include display-flex(flex);
    @include align-item(center);
    @include justify-content(space-between);
    font-size: 16px;
    font-weight: $semi-bold;
    position: relative;
    padding: 20px 0;

    &.item-shipping-restriction{
        @include mobile{
            padding-top: 40px;
        }
    }

    &::after {
        position: absolute;
        top: 10px;
        bottom: 10px;
        left: 2px;
        right: 2px;
        content: " ";
        z-index: -1;
    }

    @include tablet-small {
        @include flex-wrap(wrap);
        @include align-item(flex-start);
    }

    @include mobile {
        @include align-item(flex-start);
    }

    @include mobile-small {
        @include justify-content(flex-start);
    }

    +.bc-cart-item {
        border-top: 1px solid $section-border-color;
    }

    >div {
        padding: 0 15px;

        @include tablet {
            padding: 0 10px;
        }
    }
}

.bc-cart-item-less-quantity-error {
    margin: 10px 0;

    p {
        margin: 0;
        font-size: 14px;
        color: $warning-color;
        font-weight: $bold;
    }
}

.bc-cart-item__remove-button {
    background-position: center center;
    background-size: 64% auto;
    background-repeat: no-repeat;
    width: 25px;
    height: 25px;
    display: inline-block;
    padding: 0;
    @include border-radius(50%);
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='%23ffffff' style='%26%2310%3B'%3E%3Cpath d='M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z'/%3E%3C/svg%3E");
    background-color: $primary-color;
    border: none;
    outline: none;
    cursor: pointer;
    position: relative;

    &.cart-remove-loading {
        .spinner-container {
            height: 35px;
            width: 35px;
            top: -5px;
            left: -5px;
            position: absolute;

            .loading-spinner {
                width: 35px;
                height: 35px;
            }
        }
    }

    &:hover {
        background-color: $button-bg;
    }
}

.cart-item-action {
    @include tablet-small {
        position: absolute;
        top: 15px;
        right: 5px;
    }
}

.cart-item-option-text {
    font-size: 13px;
    font-weight: normal;
    margin-bottom: 0;

    strong {
        margin-right: 5px;
        font-weight: 600;
    }

    &:first-child {
        margin-top: 10px;
    }
}

.bc-cart-item__product-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 5px;

    @include tablet {
        font-size: 14px;
    }
}

.bc-cart-item__product-brand {
    color: $primary-color;
    font-size: 12px;
}

.cart-item-mark {
    .form-label-facet {
        white-space: nowrap;
        padding-right: 0;
        margin-bottom: 0;
        @include display-flex(flex);
        @include flex-wrap(wrap);
        @include align-item(center);
        font-size: 12px;

        .checkbox-icon {
            width: 20px;
            height: 20px;
            border-width: 1px;

            &:after {
                width: 20px;
                height: 20px;
                left: -1px;
                top: -1px;
            }
        }
    }

    p {
        margin-bottom: 0;
        width: 100%;
        line-height: 14px;
    }
}

// cart col width

.bc-cart {
    .cart-col-tprice {
        width: 170px;

        @include tablet {
            width: 150px;
        }

        @include mobile-small {
            width: 100%;
            margin-bottom: 0;
        }
    }

    .cart-col-qty {
        width: 240px;
        @include display-flex(flex);
        @include flex-wrap(wrap);
        @include justify-content(center);

        @include tablet {
            width: 160px;
        }

        @include mobile-small {
            width: 100%;
            margin-bottom: 20px;
            @include justify-content(flex-start);
        }
    }

    .cart-col-price {
        text-align: center;
        width: 200px;
        position: relative;

        @include tablet-small {
            width: 185px;
        }

        @include mobile {
            width: 165px;
        }

        @include mobile-small {
            width: 100%;
            margin-bottom: 20px;
        }

        .cart-price-wrapper {
            line-height: 40px;
        }
    }

    .cart-col-image {
        width: 100px;

        @include tablet-small {
            margin-bottom: 30px;
        }

        @include mobile-small {
            margin-bottom: 15px;
        }
    }

    .cart-col-mark {
        width: 40px;
    }

    .cart-col-name {
        width: calc(100% - 750px);

        @include tablet {
            width: calc(100% - 650px);
        }

        @include tablet-small {
            width: calc(100% - 140px);
            padding-left: 40px;
            margin-bottom: 30px;
            padding-right: 30px;
        }

        @include mobile {
            padding-left: 10px;
        }

        @include mobile-small {
            width: 100%;
            margin-bottom: 15px;
            padding-right: 0;
        }
    }
}

.bc-cart-item {
    .cart-col-qty {
        margin-top: 25px;

        @include tablet-small {
            margin-top: 0;
        }
    }

    .cart-col-mark {
        height: 20px;

        @include tablet-small {
            margin-bottom: 30px;
        }

        @include mobile {
            margin-bottom: 0;
            margin-top: 20px;
        }
    }
}

.cart-top-action-section {
    position: absolute;
    top: 0;
    right: 0;

    @include mobile {
        position: relative;
        top: auto;
        right: auto;
        width: 100%;
        margin-top: 20px;
    }

    .button {
        @include mobile {
            width: 100%;
        }

        +.button {
            margin-left: 20px;

            @include mobile {
                margin-left: 0;
                margin-top: 10px;
            }
        }
    }
}

.cart-bottom-action-section {
    width: 100%;
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid $border-color;

    .bc-cart-actions {
        margin-top: 10px;
    }

    .button {
        @include mobile {
            width: 100%;
        }

        +.button {
            margin-left: 20px;

            @include mobile {
                margin-left: 0;
                margin-top: 10px;
            }
        }
    }
}

.cart-footer-section {
    margin-top: 20px;
    padding-top: 20px;
    @include display-flex(flex);
    @include justify-content(flex-end);
    @include flex-wrap(wrap);
    border-top: 1px solid $section-border-color;
}

.bc-cart-footer {
    width: 300px;

    @include mobile-small {
        width: 100%;
    }
}

.bc-cart-subtotal {
    margin-bottom: 20px;
    min-width: 250px;
    display: inline-block;
    text-align: center;
}

.bc-cart-subtotal__label {
    font-weight: 600;
    font-size: 16px;
    padding-right: 10px;
    display: inline-block;
    vertical-align: middle;
}

.bc-cart-subtotal__amount {
    font-size: 24px;
    font-weight: $bold;
    display: inline-block;
    vertical-align: middle;
}

.bc-cart-actions-bottom {
    .cart-col-action {
        width: 100%;
        margin-bottom: 20px;
    }

    .button-loader {
        width: 100%;
    }

    .bc-cart-actions__checkout-button {
        width: 100%;
        text-transform: uppercase;
        font-weight: $semi-bold;
    }
}

.bc-cart__empty {
    text-align: center;
}

.bc-cart__title--empty {
    text-transform: uppercase;
    font-weight: $bold;
    margin-bottom: 10px;
    margin-top: 100px;
}

.cart-totals {
    margin-bottom: 30px;
}

.cart-total {
    list-style: none;
    margin: 0;
}

.coupon-code-wrap {
    width: 100%;
}

.cart-invertory-count {
    margin-top: 10px;
    font-size: 12px;
    display: inline-block;
    color: $success-color;

    @include mobile-small {
        display: block;
        text-align: left;
    }

    &.bc-cart-item-error {
        color: $warning-color;
    }
}

.has-low-inventory {
    .cart-invertory-count {
        color: $warning-color;
    }

    .form-increment {
        border-color: $warning-color;
    }
}

.cart-brand-name {
    margin-bottom: 5px;
    font-size: 12px;
    line-height: 1;
}

.remove-coupon-link {
    display: inline-block;
}

.coupon-remove-wrap {
    width: 100%;

    p {
        margin-bottom: 0;
        padding: 5px 0;
        margin-top: 10px;
        text-decoration: underline;
        cursor: pointer;

        &:hover {
            color: $primary-color;
        }
    }
}

.cart-total {
    li {
        @include display-flex(flex);
        @include align-item(center);
        @include flex-wrap(wrap);

        .col {
            width: 50%;
            @include display-flex(flex);
            padding: 0;
        }
    }

    .grand-total-price {
        font-size: 20px;
        font-weight: $bold;
    }
}

.cart-from {
    width: 100%;
    margin-top: 20px;

    .form-row {
        &.form-row--half {
            .form-field {
                width: calc(100% - 150px);

                &.form-button {
                    width: 150px;

                    .button {
                        width: 100%;
                    }
                }
            }
        }
    }

    .form-field {
        margin: 0;
        width: 100%;
    }
}

.ad-notification {
    position: fixed;
    top: 133px;
    height: calc(100vh - 160px);
    right: 30px;
    width: 500px;
    padding: 30px;
    background: $white-color;
    z-index: 20;
    box-shadow: 0px 0px 15px 4px rgba(0, 0, 0, 0.10);
}

.Title {
    margin-bottom: 40px;
    @include display-flex(flex);
    @include justify-content(space-between);
    @include align-item(center);

    .Text {
        margin-bottom: 0;
        font-size: 24px;
        font-weight: $bold;
        text-transform: uppercase;
    }
}

.cart-close-icon {
    width: 30px;
    height: 30px;
    display: inline-block;
    vertical-align: middle;
    cursor: pointer;
    background-repeat: no-repeat;
    background-position: center center;
    @include border-radius(6px);
    background-size: 18px;
    background-color: $black-color;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='%23ffffff'%3E%3Cpath d='M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z' /%3E%3C/svg%3E");

    &:hover {
        background-color: $primary-color;
    }
}

.ad-notification {

    .bc-cart-item {
        @include align-item(flex-start);
        width: 100%;
        max-width: 100%;
        @include row;

        >div {
            @include col;
        }
    }

    .Actions {
        margin-top: 20px;
        padding-top: 20px;
        border-top: 1px solid $dark-border-color;

        .col {
            width: 50%;

            .button {
                width: 100%;
            }
        }
    }

    .bc-cart-body {
        max-height: calc(100vh - 450px);
        overflow: auto;
    }

    .slide-cart-footer {
        margin-top: 20px;
        padding-top: 20px;
        border-top: 1px solid $dark-border-color;
    }

    .cart-totals {
        margin-bottom: 0;
    }

    .cart-total {
        li {
            padding: 0;
        }
    }

    .bc-cart-item__remove-button {
        left: -13px;
        top: -13px;
    }
}

.slide-cart-image {
    min-width: 100px;
    width: 100px;
}

.slide-cart-item-image {
    position: relative;
}

.slide-cart-detail {
    padding-right: 40px;
    width: calc(100% - 100px);
}

.slide-cart-item__product-title {
    font-size: 14px;
    letter-spacing: normal;
    margin-bottom: 5px;
}

.bc-cart-item__product-brand {
    margin-left: 10px;
}

.slide-cart-action {
    margin-top: 15px;

    .col {
        width: auto;
    }

    .bc-cart-item-quantity {
        width: auto;
    }

    .form-increment {

        .ais-RangeInput-submit,
        .button {
            width: 30px;
            height: 30px;
        }

        .ais-RangeInput-input,
        .form-input {
            width: 50px;
            padding: 5px;
            line-height: 18px;
        }
    }

    .bc-cart-item-total-price {
        font-weight: $bold;
        font-size: 14px;
    }
}

.slide-cart-subtotal {
    font-weight: $bold;
    font-size: 16px;
}

@keyframes fadeIn {
    0% {
        opacity: 0;
    }

    100% {
        opacity: 1;
    }
}

@keyframes example {
    from {
        transform: translateX(560px);
    }

    to {
        transform: translateX(0);
    }
}

.ad-notification {
    &.Animate {
        animation-name: example;
        animation-duration: 0.3s;
    }
}

.line-item-warning {
    width: 100%;
    height: 50px;
    background-color: #f7d6d6;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 10px;
    color: rgb(206, 86, 86);
}

.cart-shipping-restriction-message, .checkout-shipping-restriction-message{
    background: #FDF8E4;
    font-size: 16px;
    font-weight: $medium;
    text-align: center;
    padding: 10px 20px;
    line-height: 1.5;
    border: 2px solid #F2E8C3;
    border-radius: 6px;
    margin-bottom: 20px;

    @include tablet{
        font-size: 14px;
        padding: 10px 18px;
    }
    @include mobile{
        font-size: 13px;
        padding: 10px 15px;
    }

    span{
        cursor: pointer;
        text-decoration: underline;
    }
}
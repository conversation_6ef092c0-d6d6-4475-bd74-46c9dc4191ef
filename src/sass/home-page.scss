.home-hero-banner-section {
    margin-bottom: 18px;
}

.home-hero-banner-section {
    .row {
        margin-left: -9px;
        margin-right: -9px;

        @include mobile {
            margin-left: -8px;
            margin-right: -8px;
        }
    }

    .col {
        padding-left: 9px;
        padding-right: 9px;

        @include mobile {
            padding-left: 8px;
            padding-right: 8px;
        }
    }
}

.hero-banner-col {
    width: 62%;

    @include tablet-small {
        width: 100%;
        margin-bottom: 20px;
    }

    @include mobile {
        margin-bottom: 15px;
    }
}

.hero-carousel-section {
    position: relative;
    border-radius: 6px;
    overflow: hidden;
    height: 396px;

    @include desktop {
        height: 362px;
    }

    @include desktop-small {
        height: 324px;
    }

    @include laptop {
        height: 285px;
    }

    @include tablet {
        height: 225px;
    }

    @include tablet-small {
        height: 35vw;
    }

    div {
        height: 100% !important;
    }
}

.hero-carousel-item {
    position: relative;

    .hero-crousel-image {

        div {
            width: 100% !important;
            max-width: 100% !important;
        }

        picture {
            width: 100%;
        }

        img {
            width: 100%;
            object-fit: cover;
            height: 100%;
        }
    }
}

.heroCarousel-content {
    background-color: $white-color;
    background-color: rgba(255, 255, 255, .7);
    width: 50%;
    padding: 50px 30px;
    left: 0;
    margin: 0 auto;
    transform: translateY(-50%);
    text-align: center;
    position: absolute;
    right: 0;
    top: 50%;

    .button {
        margin-top: 20px;
    }
}

.heroCarousel-title {
    text-transform: uppercase;
    font-weight: $bold;
    font-size: 40px;
}

.heroCarousel-description {
    font-size: 15px;
}

.hero-small-banner-col {
    width: 38%;

    @include tablet-small {
        width: 100%;
    }

    .col {
        width: 50%;
        margin-bottom: 18px;
        height: 188px;

        @include desktop {
            height: 172px;
        }

        @include desktop-small {
            height: 153px;
        }

        @include laptop {
            height: 133px;
        }

        @include tablet {
            height: 103px;
        }

        @include tablet-small {
            height: auto;
        }

        @include mobile {
            margin-bottom: 16px;
        }

        >div {
            border-radius: 6px;
            overflow: hidden;
        }

        &:nth-child(3),
        &:nth-child(4) {
            margin-bottom: 0;
        }
    }

    img {
        @include border-radius(6px);
        height: auto;
        max-width: 100%;
        width: 100%;
    }

    video {
        @include border-radius(6px);
        height: 100%;
        object-fit: cover;
        object-position: center;
        width: 100%;
    }
}

.home-large-mid-banner-section {
    margin-bottom: 10px;

    .row {
        margin-left: -9px;
        margin-right: -9px;

        @include mobile {
            margin-left: -8px;
            margin-right: -8px;
        }
    }

    .col {
        padding-left: 9px;
        padding-right: 9px;

        @include mobile {
            padding-left: 8px;
            padding-right: 8px;
        }
    }

    video {
        width: 100%;
        @include border-radius(6px);
    }

    img {
        @include border-radius(6px);
    }

    .home-large-col-banner {
        width: 62%;
    }

    .home-mid-col-banner {
        width: 38%;
    }
}

.home-about-content {
    h3 {
        font-size: 24px;
        font-weight: 600;
        margin-top: 0;
        margin-bottom: 40px;

        @include desktop {
            font-size: 22px;
            margin-bottom: 30px;
        }

        @include laptop {
            font-size: 20px;
            margin-bottom: 20px;
        }

        @include tablet {
            font-size: 18px;
        }

        @include mobile {
            font-size: 18px;
        }
    }

    h4 {
        margin-top: 0;
        font-size: 20px;
        line-height: 26px;
        font-weight: normal;

        @include desktop {
            font-size: 18px;
        }

        @include laptop {
            font-size: 16px;
        }
    }
}

.flavor-banner-list {
    margin-bottom: 0;

    .col {
        width: 25%;
        margin-bottom: 28px;
        text-align: center;

        @include tablet-small {
            width: 33.33%;
        }

        @include mobile {
            margin-bottom: 20px;
        }

        @include mobile-small {
            width: 50%;
        }

        .banner-image {
            margin-bottom: 16px;

            @include tablet {
                margin-bottom: 10px;
            }

            img {
                @include border-radius(6px);
                width: 100%;
            }

            video {
                @include border-radius(6px);
                width: 100%;
            }
        }

        h4 {
            margin-top: 0;
            text-align: center;
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 0;

            @include desktop {
                font-size: 20px;
            }

            @include laptop {
                font-size: 18px;
            }

            @include tablet {
                font-size: 16px;
            }

            @include mobile {
                font-size: 14px;
            }
        }
    }
}

.full-width-banner-item {
    margin-bottom: 16px;
    @include border-radius(4px);
    overflow: hidden;

    video {
        width: 100%;
        @include border-radius(4px);
        overflow: hidden;
    }

    img {
        width: 100%;
        @include border-radius(4px);
        overflow: hidden;
    }
}

.two-col-banner-section {
    margin-bottom: 10px;

    .banner-item {
        width: 50%;

        @include mobile {
            width: 100%;
        }

        + .banner-item {
            @include mobile{
                margin-top: 10px
            }
        }

        img {
            @include border-radius(4px);
            width: 100%;
        }

        video {
            @include border-radius(4px);
            width: 100%;
        }
    }
}

.five-col-banner-section {
    .banner-item {
        width: 20%;

        @include mobile {
            width: 150px;
            min-width: 150px;
        }

        img {
            @include border-radius(4px);
            width: 100%;
        }

        video {
            @include border-radius(4px);
            width: 100%;
        }
    }

    .row {
        @include mobile {
            overflow: scroll;
            padding-bottom: 5px;
        }
    }
}

.four-col-banner-section {
    .banner-item {
        width: 25%;

        @include tablet-small {
            width: 50%;
            margin-bottom: 20px;
        }

        @include mobile {
            margin-bottom: 16px;
        }

        img {
            @include border-radius(4px);
            width: 100%;
        }

        video {
            @include border-radius(4px);
            width: 100%;
        }
    }
}


.three-col-banner-section {
    .banner-item {
        width: 33.33%;

        @include mobile-small{
            width: 100%;
        }

        + .banner-item {
            @include mobile-small{
                margin-top: 10px;
            }
        }

        img {
            @include border-radius(4px);
            width: 100%;
        }

        video {
            @include border-radius(4px);
            width: 100%;
        }
    }

    &.page-block {
        margin-bottom: 10px;
    }
}


.top-ten-banner-section {
    @include mobile-small{
        margin-bottom: 20px;
    }
    .banner-slider {
        padding: 0 50px;
        overflow: hidden;

        @include tablet {
            padding: 0 30px;
        }

        @include mobile {
            padding: 0;
        }
    }

    .banner-item {
        width: 200px;
    }

    .item-inner {
        @include display-flex(flex);
        @include justify-content(center);
        @include align-item(center);

        @media screen and (max-width:666px) {
            padding-left: 20px;
            padding-right: 20px;
        }

        @include mobile-small {
            padding-left: 0;
            padding-right: 0;
        }

        @include mobile-small {
            padding-left: 10px;
            padding-right: 10px;
        }

        @include x-small {
            padding-left: 5px;
            padding-right: 5px;
        }
    }

    .banner-image {
        width: 100px;

        @include tablet {
            width: 90px;
        }

        @include mobile-small {
            width: 80px;
        }

        @include x-small {
            width: 70px;
        }

        img {
            height: auto;
            width: 100%;
        }
    }

    .count-text {
        font-weight: 400;
        font-size: 132px;
        text-align: center;
        line-height: 104px;
        color: #fff;
        -webkit-text-stroke-width: 2px;
        -webkit-text-stroke-color: black;
        width: calc(100% - 120px);
        font-family: 'Fira Sans', sans-serif;

        @include desktop-small {
            width: calc(100% - 100px);
        }

        @include tablet {
            width: calc(100% - 90px);
            font-size: 120px;
        }

        @include mobile {
            -webkit-text-stroke-width: 1px;
        }

        @include mobile-small {
            font-size: 90px;
        }

        @include x-small {
            width: calc(100% - 70px);
        }
    }

    .slick-slider {
        .slick-arrow {
            width: 32px;
            height: 32px;
            @include border-radius(50%);
            background: $primary-color;
            opacity: 1;

            @include tablet {
                width: 26px;
                height: 26px;
            }

            &:after {
                height: 14px;
                width: 14px;
                transform: rotate(0);
                top: 50%;
                left: 50%;
                display: inline-block;
                position: absolute;
                margin-top: -7px;
                margin-left: -7px;
                background-image: url("data:image/svg+xml,%3Csvg version='1.1' xmlns='http://www.w3.org/2000/svg' width='1024' height='1024' viewBox='0 0 1024 1024'%3E%3Ctitle%3E%3C/title%3E%3Cg id='icomoon-ignore'%3E%3C/g%3E%3Cpath fill='%23fff' d='M102.4 512l512 512 102.4-102.4-409.6-409.6 409.6-409.6-102.4-102.4z'%3E%3C/path%3E%3C/svg%3E");

                @include tablet {
                    width: 12px;
                    height: 12px;
                    margin-top: -6px;
                    margin-left: -6px;
                }

            }

            &.slick-prev {
                left: -50px;

                @include tablet {
                    left: -30px;
                }
            }

            &.slick-next {
                right: -50px;

                @include tablet {
                    right: -30px;
                }

                &:after {
                    transform: rotate(-180deg);
                }
            }
        }
    }
}

.brand-logo-section {
    .banner-slider {
        padding: 0 100px;

        @include tablet {
            padding: 0 30px;
        }

        @include tablet-small {
            padding: 0;
        }
    }

    .item-inner {
        text-align: center;

        img {
            max-height: 58px;
            width: auto;
            display: inline-block;
        }
    }
}

.banner-for-mobile {
    display: none;

    @include mobile {
        display: block;
    }
}

.banner-for-desktop {
    @include mobile {
        display: none;
    }
}

// home page layout style 2

.home-hero-banner-section {
    &.hero-banner-style-2 {
        margin-bottom: 30px;

        @include mobile-small{
            margin-bottom: 20px;
        }

        .col {
            padding-left: 5px;
            padding-right: 5px;
        }

        .hero-carousel-section {
            height: 484px;

            @include desktop {
                height: 26vw;
            }
            @include mobile-small{
                height: auto;
            }
        }

        .hero-banner-col {
            width: calc(100% - 33.33%);

            @include mobile-small{
                width: 100%;
                margin-bottom: 10px;
            }
        }

        .hero-banner-single-banner {
            width: 33.33%;
            overflow: hidden;
            height: 484px;
            @include border-radius(4px);

            @include desktop {
                height: 26vw;
            }

            @include mobile-small{
                width: 100%;
                height: auto;
            }

            >div {
                height: 100%;

                a {
                    display: block;
                    height: 100%;
                }
            }

            img,
            video {
                width: 100%;
                height: 100%;
                object-fit: cover;
                @include border-radius(4px);
                overflow: hidden;
            }
        }
    }
}

.full-width-banner-section {
    &.page-block {
        margin-bottom: 30px;
    }
}

.brand-logo-section {
    &.page-block {
        margin-bottom: 30px;
    }
}

.five-col-banner-section{
    &.banner-style-2{

        .banner-item{
            @include mobile{
                width: 50%;
                margin-bottom: 10px;
            }

            @include x-small{
                width: 100%;
            }
        }

        .row-small {
            @include mobile {
                overflow: hidden;
                padding-bottom: 0;
                flex-wrap: wrap;
                justify-content: center;
            }
        }
    }
}
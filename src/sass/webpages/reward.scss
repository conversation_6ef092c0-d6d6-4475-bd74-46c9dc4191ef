.page-reward {
    .page-sidebar {
        width: 300px;

        @include mobile {
            width: 100%;
        }

        +.page-content {
            width: calc(100% - 300px);

            @include mobile {
                width: 100%;
            }
        }
    }
}

.sidebar-heading {
    .icon {
        width: 24px;
        height: 24px;
        margin-right: 8px;
    }
}

.page-reward {
    .breadcrumb-list {
        +h2 {
            display: none;
        }
    }
}

.reward-app-sidebar {
    .sidebar-block {
        margin-top: 0;
        padding-top: 0;
        border-top: none;

        +.sidebar-block {
            margin-top: 24px;
        }
    }

    .page-sidebar-inner-wrap {
        padding: 24px;
        @include border-radius(5px);
        background: $grey-bg;
    }

    .sidebar-heading {
        @include justify-content(flex-start);
        text-transform: none;
        margin-bottom: 10px;
    }

    .sidebar-nav-list {
        margin-left: 32px;

        li {
            +li {
                border: none;
            }
        }

        a {
            &[aria-current="page"] {
                font-weight: $bold;
                color: #005B99;
            }
        }
    }
}

.reward-point-block {
    margin-bottom: 30px;

    p {
        font-size: 16px;
        margin-bottom: 6px;
    }

    h4 {
        font-size: 32px;
        font-weight: $semi-bold;

        @include tablet {
            font-size: 28px;
        }
    }
}

.reward-table-block {

    @include tablet {
        font-size: 14px;
    }

    table {
        width: 100%;
        border-spacing: 0;
    }

    thead {
        background: #F4F4F4;
    }

    th {
        padding: 13px 16px;
    }

    td {
        padding: 8px 16px;

        @include tablet-small {
            padding: 5px 0;
        }

        &:first-child {
            @include tablet-small {
                padding-top: 20px;
            }
        }

        &:last-child {
            @include tablet-small {
                padding-bottom: 20px;
            }
        }

        p {
            margin: 0;
        }

        .button {
            padding: 10px 16px;
            line-height: 12px;
            text-transform: none;
            font-size: 13px;
        }
    }

    tr {
        td {
            border-bottom: 1px solid #E4E4E4;

            @include tablet-small {
                border: none;
            }

            &:first-child {
                @include tablet-small {
                    border-top: 1px solid #E4E4E4;
                }
            }
        }
    }

    .red {
        color: $warning-color;
        font-weight: $bold;
    }

    .green {
        font-weight: $bold;
        color: $success-color;
    }

    thead {
        @include tablet-small {
            display: none;
        }
    }

    tbody {
        tr {
            @include tablet-small {
                @include display-flex(flex);
                @include flex-wrap(wrap);

            }

            td {
                @include tablet-small {
                    display: block;
                    width: 100%;
                }

                .text-right,
                .text-center {
                    @include tablet-small {
                        text-align: left;
                    }
                }
            }
        }
    }
}

.reward-success-section {
    h6 {
        margin-bottom: 30px;
        font-size: 24px;
        font-weight: $bold;

        @include tablet-small{
            font-size: 20px;
        }
    }

    p {
        font-size: 15px;
        color: #474747;

        @include tablet-small{
            font-size: 14px;
        }

        a {
            font-weight: $semi-bold;
            text-decoration: underline;
        }
    }

    .code-copy-block {
        margin-bottom: 30px;
        cursor: pointer;

        button {
            font-size: 24px;
            padding: 20px 30px;
            border: 2px dotted #005B99;
            @include border-radius(8px);
            background: none;
            width: 280px;
            max-width: 100%;
            outline: none;
            font-weight: $semi-bold;
            cursor: pointer;
        }
    }

    .button {
        text-transform: none;
    }
}

.reward-success-inner {
    max-width: 340px;
    margin: 0 auto;
    text-align: center;
}

@include tablet-small {

    .table-coupon-value-before,
    .table-coupon-code-before,
    .table-coupon-expiry-before,
    .table-coupon-value-before,
    .table-reward-point-before,
    .table-date-before,
    .table-description-before,
    .table-balance-before {
        &::before {
            margin-right: 10px;
            font-size: 13px;
            font-weight: $bold;
            color: $grey-font-color;
            text-decoration: underline;
        }
    }

    .table-coupon-value-before {
        &::before {
            content: "Coupon Value:";
        }
    }

    .table-coupon-code-before {
        &::before {
            content: "Coupon Code:";
        }
    }
    
    .table-coupon-expiry-before{
        &::before {
            content: "Expiry Date:";
        }
    }

    .table-reward-point-before {
        &::before {
            content: "Reward Point:";
        }
    }

    .table-date-before {
        &::before {
            content: "Date:";
        }
    }

    .table-description-before {
        &::before {
            content: "Description:";
        }
    }

    .table-balance-before {
        @include tablet-small {
            font-weight: $bold;
        }

        &::before {
            content: "Balance:";
        }
    }
}
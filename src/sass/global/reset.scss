/* ==================== BROWSER RESET STYLE ==================== */

html {
  -webkit-font-smoothing: antialiased;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
}

body {
  margin: 0;
  padding: 0;
  -webkit-font-smoothing: subpixel-antialiased;
  -webkit-text-stroke-width: 0.1px;
  -moz-osx-font-smoothing: auto;
  padding: 0;
  background: $body-background;
  color: $body-font-color;
  font-size: $body-font-size;
  font-weight: 400;
  font-family: $body-font;

  @include mobile {
    font-size: 14px;
  }
}

* {
  outline: none;
  padding: 0;
  margin: 0;
}

input,
textarea {
  font-size: $body-font-size;
  outline: none;
}

select {
  outline: none;
}

select option {
  padding: 4px 8px;
  font-size: 13px;
  outline: none;
}

textarea {
  resize: none;
  line-height: 18px;
}

h1,
.h1,
h2,
.h2,
h3,
.h3,
h4,
.h4,
h5,
.h5,
h6,
.h6 {
  margin: 0 0 30px 0;
  padding: 0;
  font-weight: $medium;
  color: $body-font-color;
  text-decoration: none;
  font-family: inherit;
  line-height: 1.5;
}

h1,
.h1 {
  font-size: 30px;

  @include tablet {
    font-size: 28px;
  }

  @include mobile {
    font-size: 26px;
  }
}

h2,
.h2 {
  font-size: 26px;

  @include tablet {
    font-size: 24px;
  }

  @include mobile {
    font-size: 24px;
  }
}

h3,
.h3 {
  font-size: 24px;

  @include tablet {
    font-size: 22px;
  }

  @include mobile {
    font-size: 22px;
  }
}

h4,
.h4 {
  font-size: 22px;

  @include tablet {
    font-size: 20px;
  }

  @include mobile {
    font-size: 20px;
  }
}

h5,
.h5 {
  font-size: 20px;

  @include tablet {
    font-size: 18px;
  }

  @include mobile {
    font-size: 18px;
  }
}

h6,
.h6 {
  font-size: 18px;

  @include tablet {
    font-size: 16px;
  }

  @include mobile {
    font-size: 16px;
  }
}

a {
  outline: none;
  text-decoration: none;
  color: $link-color;
  @include transition(all 0.4s ease);
  cursor: pointer;

  &:hover,
  &:focus {
    color: $link-hover-color;
  }

  &.reverse-link-style {
    color: $link-hover-color;

    &:hover {
      color: $link-color;
    }
  }
}

.link-style {
  text-decoration: underline;
  font-weight: 500;
  cursor: pointer;
}

strong,
b {
  font-weight: $bold;
}

p {
  margin-bottom: 20px;
  line-height: 24px;

  small {
    line-height: 18px;
    font-size: 12px;
    display: inline-block;
  }
}

ul,
ol {
  margin-left: 30px;
  margin-bottom: 30px;
  padding: 0;
}

img {
  max-width: 100%;
  vertical-align: middle;
}

hr {
  border: none;
  border-top: 1px solid $border-light-color;
  margin: 30px 0;
}

address {
  font-style: normal;
  margin-bottom: 20px;
}

iframe {
  max-width: 100%;
  vertical-align: middle;
  outline: none;
  border: none;
}

.text-uppercase {
  text-transform: uppercase;
}

/* ==================== COMMON CLASS ==================== */

.flex {
  @include display-flex(flex);
}

.vertical-middle {
  @include align-item(center);
}

.vertical-top {
  @include align-item(flex-start);
}

.vertical-bottom {
  @include align-item(flex-end);
}

.align-self-start {
  @include align-self(flex-start);
}

.align-left {
  @include justify-content(flex-start);
}

.align-center {
  @include justify-content(center);
}

.align-right {
  @include justify-content(flex-end);
}

.justify-space {
  @include justify-content(space-between);
}

.flex-wrap {
  @include flex-wrap(wrap);
}

.flex-direction {
  @include flex-direction(column);
}

.list-style-none {
  list-style: none;
}

*,
:after,
:before {
  box-sizing: border-box;
}

.clearfix,
.clearfix {

  &:before,
  &:after {
    display: table;
    content: " ";
  }

  &:after {
    clear: both;
  }
}

.clear {
  clear: both;
}

.full-width {
  width: 100%;
}

.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.container {
  padding: 0 40px;
  max-width: calc($container + 80px);
  margin-left: auto;
  margin-right: auto;

  @include desktop {
    max-width: 1600px;
    padding: 0 40px;
  }

  @include desktop-small {
    max-width: 1440px;
  }

  @include laptop {
    max-width: 1240px;
    padding: 0 20px;
  }

  @include tablet {
    max-width: 984px;
    padding: 0 20px;
  }

  @include tablet-small {
    max-width: 100%;
    padding-left: 30px;
    padding-right: 30px;
  }

  @include mobile {
    padding: 0 12px;
  }
}

.container-small{
  @extend .container;
  max-width: 1388px;

  @include laptop {
    max-width: 1240px;
    padding: 0 20px;
  }

  @include tablet {
    max-width: 984px;
    padding: 0 20px;
  }

  @include tablet-small {
    max-width: 100%;
    padding-left: 30px;
    padding-right: 30px;
  }

  @include mobile {
    padding: 0 12px;
  }
}

.container-x-small{
  @extend .container;
  max-width: 800px;
}

.row {
  @include row;

  @include mobile {
    margin-left: -8px;
    margin-right: -8px;
  }
}

.col {
  @include col;

  @include mobile {
    padding-left: 8px;
    padding-right: 8px;
  }
}

.row-small{
  margin-left: -5px;
  margin-right: -5px;

  .col{
    padding-left: 5px;
    padding-right: 5px;
  }
}

.padding-none {
  padding: 0 !important;
}

.margin-none {
  margin: 0 !important;
}

.m-t-0 {
  margin-top: 0 !important;
}

.m-b-0 {
  margin-bottom: 0 !important;
}

.m-b-50 {
  margin-bottom: 50px !important;
}

.body {
  margin-top: 30px;
  margin-bottom: 80px;
}

.page-block {
  margin-bottom: 50px;

  @include tablet {
    margin-bottom: 40px;
  }

  @include mobile {
    margin-bottom: 30px;
  }
}

.hide {
  display: none;
}

.section-with-bg {
  background: $section-bg;
  padding: 80px 0;
}

.font-formal {
  font-weight: normal;
}

.font-medium {
  font-weight: 500;
}

.font-semi-bold {
  font-weight: 600;
}

.font-bold {
  font-weight: 700;
}

big,
.big {
  font-size: 16px;
}

.icon {
  display: inline-block;
  vertical-align: middle;

  svg {
    display: inline-block;
    vertical-align: middle;
    max-width: 100%;
    max-height: 100%;
  }
}

@media screen and (min-width: 1260px) {
  ::-webkit-scrollbar {
    width: 4px;
  }


  /* Track */
  ::-webkit-scrollbar-track {
    background: #f1f1f1;
  }

  /* Handle */
  ::-webkit-scrollbar-thumb {
    background: #888;
  }

  /* Handle on hover */
  ::-webkit-scrollbar-thumb:hover {
    background: #555;
  }

}

.gatsby-image-wrapper {
  max-width: 100% !important;
  width: 100% !important;

  div {
    max-width: 100% !important;
    width: 100% !important;
  }
}

.show-mobile {
  display: none;

  @include mobile {
    display: block;
  }
}

.hide-mobile {
  @include mobile {
    display: none;
  }
}

.hide-tablet {
  @include tablet {
    display: none;
  }
}

.hide-tablet-small {
  @include tablet-small {
    display: none;
  }
}

.show-tablet {
  display: none;

  @include tablet {
    display: block;
  }
}

.show-tablet-small {
  display: none;

  @include tablet-small {
    display: block;
  }
}


.w-50px{
  width: 50px;
}
.w-100px{
  width: 100px;
}
.w-125px{
  width: 125px;
}
.w-150px{
  width: 150px;
}
.w-175px{
  width: 175px;
}
.w-200px{
  width: 200px;
}
.w-250px{
  width: 250px;
}
.w-300px{
  width: 300px;
}
.w-350px{
  width: 350px;
}
.w-400px{
  width: 400px;
}
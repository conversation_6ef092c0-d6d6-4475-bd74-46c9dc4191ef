@mixin col {
  padding-left: $gutter-space;
  padding-right: $gutter-space;

  @include mobile {
    padding-left: 10px;
    padding-right: 10px;
  }
}

@mixin row {
  margin-left: -($gutter-space);
  margin-right: -($gutter-space);

  @include mobile() {
    margin-left: -10px;
    margin-right: -10px;
  }
}

@mixin border-radius($radius) {
  -webkit-border-radius: $radius;
  -moz-border-radius: $radius;
  border-radius: $radius;
}

@mixin box-shadow($shadow) {
  -webkit-box-shadow: $shadow;
  -moz-box-shadow: $shadow;
  box-shadow: $shadow;
}

@mixin transition($trans) {
  -webkit-transition: $trans;
  -moz-transition: $trans;
  transition: $trans;
}

@mixin transform($transforms) {
  -webkit-transform: $transforms;
  -moz-transform: $transforms;
  -ms-transform: $transforms;
  transform: $transforms;
}

@mixin rotate ($deg) {
  @include transform(rotate(#{$deg}deg));
}

@mixin translate ($x, $y) {
  @include transform(translate($x, $y));
}

@mixin flex-basis($flex-basis) {
  -webkit-flex-basis: ($flex-basis);
  flex-basis: ($flex-basis);
}

@mixin display-flex($flex) {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: $flex;
}

@mixin align-item($align-item) {
  -webkit-box-align: $align-item;
  -moz-box-align: $align-item;
  -ms-flex-align: $align-item;
  -webkit-align-items: $align-item;
  align-items: $align-item;
}

@mixin align-self($align-self) {
  -webkit-align-self: $align-self;
  align-self: $align-self;
}

@mixin justify-content($justify-content) {
  -webkit-justify-content: $justify-content;
  justify-content: $justify-content;
}

@mixin flex-wrap($flex-wrap) {
  flex-wrap: $flex-wrap;
  -webkit-flex-wrap: $flex-wrap;
}

@mixin flex-direction($flex-direction) {
  flex-direction: $flex-direction;
  -webkit-flex-direction: $flex-direction;
}

/* =============== MEDIA MIXIN =============== */

// 1600 view
@mixin desktop {
  @media screen and (max-width:1750px) {
    @content;
  }
}

// 1440 view
@mixin desktop-small {
  @media screen and (max-width:1599px) {
    @content;
  }
}

// 1280 view
@mixin laptop {
  @media screen and (max-width:1439px) {
    @content;
  }
}

// 1024 view
@mixin tablet {
  @media screen and (max-width:1259px) {
    @content;
  }
}

// 768 view
@mixin tablet-small {
  @media screen and (max-width:1023px) {
    @content;
  }
}

// 667 view
@mixin mobile {
  @media screen and (max-width:767px) {
    @content;
  }
}

// 480 view
@mixin mobile-small {
  @media screen and (max-width:567px) {
    @content;
  }
}

// 360 view
@mixin x-small {
  @media screen and (max-width:479px) {
    @content;
  }
}
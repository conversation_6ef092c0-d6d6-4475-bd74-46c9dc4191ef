.category-description-block {
    margin-top: 50px;
    font-family: $body-font !important;

    * {
        font-family: $body-font !important;
        background: none !important;
    }
}

.headline {
    font-size: 18px;
    margin: 0 0 25px;
    line-height: 30px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e9e9e9;
    text-transform: uppercase;
    text-align: center;
    font-weight: 600;
}

.sub-category-list {
    display: flex;
    list-style: none;
    margin: 0 0 30px 0;
}

.sub-category-list li {
    padding-right: 20px;
    width: 20%;
    margin-bottom: 10px;

    a {
        background-image: url("data:image/svg+xml,%3Csvg version='1.1' xmlns='http://www.w3.org/2000/svg' width='576' height='1024' viewBox='0 0 576 1024'%3E%3Ctitle%3E%3C/title%3E%3Cg id='icomoon-ignore'%3E%3C/g%3E%3Cpath d='M554.936 475.452l-460.488-460.335c-20.206-20.155-52.943-20.155-73.2 0-20.206 20.155-20.206 52.892 0 73.047l423.963 423.81-423.912 423.81c-20.206 20.155-20.206 52.892 0 73.098 20.206 20.155 52.994 20.155 73.2 0l460.488-460.335c19.9-19.947 19.9-53.198-0.051-73.093z'%3E%3C/path%3E%3C/svg%3E");
        padding-left: 12px;
        background-size: 5px auto;
        background-position: left center;
        background-repeat: no-repeat;
    }
}

.product-item {
    width: 20%;

    @include desktop {
        width: 20%;
    }

    @include tablet {
        width: 25%;
    }

    @include tablet-small {
        width: 33.33%;
    }

    @include mobile {
        width: 33.33%;
    }

    @include mobile-small {
        width: 50%;
    }
}

.slick-slider {
    .product-item {
        width: 100%;
    }
}

.page-sidebar {
    width: 300px;
    padding-right: 15px;

    @include tablet{
        padding-right: 12px;
    }

    @include laptop {
        width: 260px;
    }

    @include tablet-small {
        width: 240px;
    }

    @include mobile {
        width: 100%;
        margin-bottom: 30px;
        padding-left: 10px;
        padding-right: 10px;
    }

    +.page-content {
        width: calc(100% - 300px);
        padding-left: 15px;

        @include tablet{
            padding-left: 12px;
        }

        @include laptop {
            width: calc(100% - 260px);
        }

        @include tablet-small {
            width: calc(100% - 240px);
        }

        @include mobile {
            width: 100%;
            padding-left: 10px;
            padding-right: 10px;
        }

        .product-item {
            width: 20%;

            @include desktop {
                width: 20%;
            }

            @include laptop {
                width: 25%;
            }

            @include tablet {
                width: 33.33%;
            }

            @include tablet-small {
                width: 50%;
            }

            @include mobile {
                width: 33.33%;
            }

            @include mobile-small {
                width: 50%;
            }
        }
    }
}

.page-sidebar {
    &.page-sidebar-toggle {
        @include mobile {
            display: none;
            margin-bottom: 20px;
        }
    }
}

.category-toggle-active {
    .page-sidebar {
        &.page-sidebar-toggle {
            @include mobile {
                display: block;
            }
        }
    }
}

.page-sidebar-inner-wrap {
    @include mobile {
        padding: 15px;
        @include border-radius(4px);
        border: 1px solid $border-color;
    }
}

.mobile-sidebar-toggle {
    @include col;
    border: none;
    background: none;
    outline: none;
    width: 100%;
    margin-bottom: 20px;

    span {
        text-transform: uppercase;
        font-size: 14px;
        padding: 12px 18px;
        @include border-radius(4px);
        background: $primary-color;
        display: block;
        width: 100%;
        color: $white-color;
        position: relative;

        &:after {
            position: absolute;
            top: 50%;
            right: 15px;
            width: 12px;
            height: 12px;
            margin-top: -6px;
            content: " ";
            background-image: url("data:image/svg+xml,%3Csvg version='1.1' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 640 1024' %3E%3Ctitle%3E%3C/title%3E%3Cg id='icomoon-ignore'%3E%3C/g%3E%3Cpath fill='%23fff' d='M601.2 320l38.8 41.4-320 342.6-320-342.6 38.6-41.4 281.4 301z' %3E%3C/path%3E%3C/svg%3E");
            background-position: center center;
            background-size: 12px auto;
        }

        &.hide-filter {
            display: none;

            &:after {
                @include transform(rotate(-180deg));
            }
        }
    }
}

.category-toggle-active {
    .mobile-sidebar-toggle {
        span {
            &.hide-filter {
                display: block;
            }

            &.show-filter {
                display: none;
            }
        }
    }
}

/* ==================== SIDEBAR STYLE ==================== */

.sidebar-heading {
    font-weight: $bold;
    font-size: 16px;
    text-transform: uppercase;
    margin-bottom: 20px;
    @include display-flex(flex);
    @include justify-content(space-between);
    cursor: pointer;
}

.price-range-field {
    .form-input {
        margin-bottom: 10px;
    }

    .button {
        width: 100%;
    }

    .col {
        width: 50%;
    }

    .filter-price-reset {
        margin-top: 5px;
    }
}

.sidebar-block {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid $border-color;
}

.sidebar-nav-list {
    .control-item {
        position: relative;
        padding-right: 30px;

        span {
            position: absolute;
            right: 0;
            font-size: 11px;
        }

        +.control-item {
            margin-top: 12px;
        }
    }
}

.facet-clear-button {
    margin-top: 20px;
}

.facet-clear-link {
    text-decoration: underline;
    cursor: pointer;
    font-weight: 500;
    color: $link-color;

    &:hover {
        color: $link-hover-color;
    }
}

.selected-facet {
    margin-bottom: 20px;

    span {
        background-color: rgba(0, 0, 0, 0.1);
        padding: 5px 12px 5px 10px;
        border-radius: 4px;
        margin-right: 5px;
        margin-bottom: 5px;
        cursor: pointer;
        font-size: 12px;
        background-size: 12px auto;
        background-position: right 5px center;
        background-repeat: no-repeat;

        .close-icon {
            font-size: 12px;
            margin: 0px 0px 0px 10px;
            display: inline-block;
        }
    }


}

.page-action-bar {
    margin-bottom: 24px;
    border: 1px solid #e4e4e4;
    @include border-radius(4px);
    padding: 12px 24px;
    @include flex-wrap(wrap);

    @include tablet {
        padding: 15px;
    }

    &:empty {
        display: none;
    }

    .page-title {
        border-bottom: none;
        margin: 0;
        padding: 0;
        font-style: normal;
        font-weight: 600;
        font-size: 20px;
        line-height: 2;

        @include desktop-small {
            width: 100%;
            margin-bottom: 12px;
            padding-bottom: 12px;
            border-bottom: 1px solid $border-color;
            line-height: 1;
        }

        &.small {
            font-size: 16px;
        }
    }

    .pagination-container {
        &.pagination-bar {
            margin-top: 0;

            @include tablet-small {
                width: 100%;
                @include justify-content(flex-start);
            }

            @include tablet-small {
                margin-bottom: 15px;
            }
        }
    }

    &.search-page-action-bar {
        .page-title {
            order: 1;
        }

        .pagination-container {
            order: 2;
        }

        .search-sort-section {
            order: 3;
        }
    }
}

.actionBar-section {
    .form-label {
        padding-left: 0;
        padding-right: 8px;
        color: $body-font-color;
        font-weight: 500;
    }

    .form-select {
        width: auto;
    }

    .form-field {
        @include display-flex(flex);
        @include align-item(center);
        margin-bottom: 0;
    }

    .form-label {
        margin-bottom: 0;
    }
}

// sidebar list
.sidebar-nav-list {
    list-style: none;
    margin: 0;

    li {
        position: relative;
        border-bottom: 1px solid #e0e0e0;

        &:last-child{
            border-bottom: none;
        }

        // +li {
        //     border-top: 1px solid #e0e0e0;
        // }

        a {
            padding: 8px 0;
            display: block;
        }

        > div{
            position: relative;

            button{
                position: absolute;
                top: 0;
                right: 0;
                width: 32px;
                height: 32px;
                background-color: #fff;
                border: none;
                cursor: pointer;
                line-height: 32px;
                text-align: center;
                font-size: 20px;
                color: #000000;
                z-index: 1;
            }
        }

        ul{
            margin: 0;
            padding: 0;
            list-style: none;
            border-top: 1px solid #e5e5e5;

            li{
                &:last-child{
                    border-bottom: none;
                }
            }
        }
    }
}

.sidebar-block-bottom {
    @include mobile {
        display: none;
    }

    +.sidebar-block-bottom {
        border-top: 1px solid $border-color;
        margin-top: 30px;
        padding-top: 30px;
    }
}

.page-has-filter {
    .sidebar-block-bottom {
        border-top: 1px solid $border-color;
        margin-top: 30px;
        padding-top: 30px;
    }
}

// list view
.product-view-list {
    margin-right: 30px;

    @include tablet {
        display: none;
    }

    @include laptop {
        margin-right: 20px;
    }

    span {
        width: 20px;
        height: 20px;
        display: inline-block;
        vertical-align: middle;
        cursor: pointer;
        fill: $black-color;

        &:hover,
        &.active {
            fill: $primary-color;
        }

        svg {
            max-width: 100%;
            max-height: 100%;
            display: inline-block;
            vertical-align: middle;
        }

        +span {
            margin-left: 15px;
        }
    }
}
.order-invoice-template {

  max-width: 1200px;
  margin: 0 auto;

  body {
    background: #fff;
    color: #000;
    margin: 10px;
  }

  h1,
  h1 a {
    color: #000;
    text-decoration: none;
  }

  .invoice-header-logo{
    padding: 15px;
    text-align: center;

    img{
      max-height: 50px;
    }
  }

  .Invoice,
  .PackingSlip {
    border: 2px solid #cacaca;
    padding: 10px;
  }

  .InvoiceTitle,
  .PackingSlipTitle {
    font-size: 15px;
    font-weight: bold;
    background: #000;
    color: #fff;
    padding: 10px 20px;
    margin-bottom: 20px;
  }

  .StoreAddress {
    font-weight: bold;
    margin-bottom: 10px;
  }

  .InvoiceHeading,
  .PackingSlipHeading {
    font-size: 15px;
    font-weight: bold;
    margin-bottom: 10px;
  }

  .CustomerDetails,
  .AddressRow,
  .InvoiceItems,
  .InvoiceDetails,
  .PackingSlipItems,
  .PackingSlipDetails {
    margin-bottom: 30px;

    li {
      list-style-type: none;
    }
  }

  .InvoiceTable,
  .PackingSlipTable {
    border-collapse: collapse;
    width: 100%;
  }

  .InvoiceTable th,
  .PackingSlipTable th {
    font-weight: bold;
    padding: 5px;
    text-align: left;
  }


  .InvoiceTable td,
  .PackingSlipTable td {
    padding: 5px;
    vertical-align: top;
    text-align: left;
  }

  .InvoiceTotalRow:last-child {
    font-weight: bold;
  }

  .AddressRow,
  .InvoiceDetails,
  .PackingSlipDetails {
    overflow: hidden;
    width: 100%;
  }

  .ShippingAddress,
  .BillingAddress,
  .InvoiceDetailsLeft,
  .InvoiceDetailsRight,
  .PackingSlipDetailsLeft,
  .PackingSlipDetailsRight {
    float: left;
    width: 48%;

    ul {
      padding-left: 0;
    }
  }

  .PackingSlipItems {
    border-top: 1px solid #cacaca;
    padding-top: 10px;
  }

  .InvoiceItemList {
    border-bottom: 1px solid #cacaca;
  }

  .InvoiceItemDivider td {
    padding-top: 10px;
    border-top: 1px solid #cacaca;
  }

  .DetailRow,
  .ConfigurableProductRow {
    clear: left;
    margin-top: 6px;
    padding-left: 140px;
  }

  .DetailRow .Label,
  .ConfigurableProductRow .Label {
    margin: 0 0 6px -140px;
    float: left;
    width: 130px;
    padding-top: 1px;
    display: inline;
    position: relative;

  }

  .DetailRow .Value,
  .ConfigurableProductRow .Value {
    display: inline;
  }

  .InvoiceDetails .DetailRow .Label,
  .PackingSlipDetails .DetailRow .Label {
    font-weight: bold;
  }

  .ConfigurableProductRow {
    font-size: 11px;
    margin-left: 10px;
  }

  .InvoiceTotals .InvoiceTotal td {
    font-weight: bold;
  }

  .ProductQuantity {
    width: 50px;
  }

  .ProductCost,
  .ProductTotal {
    width: 150px;
  }

  .PageBreak {
    page-break-after: always;
  }

  .ProductPreOrder {
    font-size: 11px;
  }

  // invoice custom style

  .invoice-heading{
    line-height: 1;
    padding-bottom: 8px;
    margin-bottom: 15px;
    text-transform: uppercase;
    font-weight: bold;
    font-size: 15px;
    border-bottom: 1px solid #cacaca;
  }

  .AddressRow{
    padding: 20px 10px;
    background: #fafafa;
    border-radius: 4px;
    border: 1px solid #e4e4e4;
  }
  .AddressCol{
    padding: 0 15px;
  }

  .AddressCol{
    ul{
      margin: 0;
      line-height: 1.5;
    }
  }

  .invoice-store-address{
    margin-bottom: 30px;
    p{
      margin-bottom: 5px;
    }
  }

  .InvoiceDetails{
    margin-left: -15px;
    margin-right: -15px;

    .DetailRow{
      + .DetailRow{
        margin-top: 10px;
        padding-top: 10px;
        border-top: 1px solid #cacaca;
      }
    }
  }
  .InvoiceDetailsLeft, .InvoiceDetailsRight{
    padding: 0 15px;
  }

  .InvoiceItems-table{
    padding: 20px 10px;
    background: #fafafa;
    border-radius: 4px;
    border: 1px solid #e4e4e4;
    margin-bottom: 40px;
  }

  .InvoiceTotals{
    .total-Title{
      width: calc(100% - 100px);
    }
    .Total{
      width: 100px;
    }
  }
}
.footer-section {
  position: relative;
  // z-index: 5;
  padding: 0;
  color: $grey-font-color;
  margin-top: 100px;

  @include mobile {
    margin-top: 60px;
  }

  ul {
    margin-left: 0;
    margin-bottom: 0;
    list-style: none;
  }
}

.footer-col {
  width: 25%;

  @include laptop{
    width: 20%;
  }
  @include tablet{
    width: 30%;
  }
  @include tablet-small{
    width: 40%;
  }

  @include mobile {
    width: 100%;
  }

  &.footer-col-large {
    width: 50%;

    @include laptop{
      width: 60%;
    }
    @include tablet{
      width: 70%;
    }
    @include tablet-small{
      width: 60%;
    }

    >.col {
      @include mobile {
        padding-left: 0;
        padding-right: 0;
      }
    }

    @include mobile {
      width: 100%;
    }
  }

  &.footer-col-business{
    margin-top: 34px;

    @include tablet{
      width: 100%;
      padding-top: 20px;
      margin-top: 20px;
      border-top: 1px solid $border-color;  
    }
  }
}


.footer-support-hours{
  @include tablet{
    @include display-flex(flex);
  }
  @include tablet-small{
    display: block;
  }

  > p{
    @include tablet{
      width: 50%;
      padding-right: 30px;
    }

    @include tablet-small{
      width: 100%;
    }
  }
}


.footer-col-title {
  font-size: 16px;
  font-weight: 600;
  text-transform: uppercase;
  margin-bottom: 10px;
}

.footer-top-section {
  background: $section-bg;
  padding: 40px 0;
}

.footer-nav-list {
  li {
    +li {
      margin-top: 7px;
    }
  }

  a {
    color: #333333;

    &:hover {
      color: $body-font-color;
    }
  }
}

.footer-nav-col {
  margin-top: 34px;
  width: 50%;

  @include mobile {
    width: 100%;
  }
}

.footer-bottom-section {
  padding: 20px 0;
  font-size: 12px;

  @include tablet {
    padding: 20px 0;
  }
}

.footer-copyright-text {
  margin-bottom: 10px;
}

.warning-text {
  p {
    margin-bottom: 0;
  }
}

.footer-address-col {
  color: #333333;
  font-size: 14px;

  > div{
    + div{
      margin-top: 20px;
    }

    p{
      margin-bottom: 0;
    }
  }
}

.footer-logo {
  margin-bottom: 20px;

  img {
    mix-blend-mode: multiply;
    max-height: 50px;

    @include mobile {
      max-height: 40px;
    }
  }
}

.footer-nav-title {
  font-weight: 600;
  font-size: 15px;
  line-height: 18px;
  text-transform: uppercase;
  margin-bottom: 33px;
  font-family: Inter, sans-serif;
  position: relative;

  @include mobile {
    margin-bottom: 15px;
  }
}

.footer-nav-list {
  column-count: 2;
  column-gap: 0;

  @include tablet-small {
    column-count: auto;
  }

  li {
    padding: 0 20px 0 0;
    margin-bottom: 9px;

    a {
      font-weight: 400;
      font-size: 14px;
      line-height: 20px;
      color: #333;
      font-family: Inter, sans-serif;
    }
  }
}

.footer-payment-col {
  margin-bottom: 30px;
  width: 100%;

  @include mobile{
    margin-bottom: 20px;
  }

  ul {
    @include display-flex(flex);
    @include align-item(center);
    @include justify-content(center);

    @include tablet-small{
      margin: 0 -5px;
    }

    li {

      @include tablet-small{
        padding: 0 5px;
      }

      @include mobile {
        margin-bottom: 10px;
      }

      div {
        @include display-flex(flex);
        @include align-item(center);
        @include justify-content(center);
        background: #fff;
        border-radius: 5px;
        filter: drop-shadow(0px 2px 4px rgba(0, 0, 0, .1));
        width: 100px;
        height: 50px;

        @include tablet-small{
          width: 85px;
          height: 40px;
        }

        @include mobile{
          width: 50px;
          height: 28px;
        }
      }

      &:nth-child(1) {
        img {
          max-height: 34px;

          @include tablet-small{
            max-height: 28px;
          }
          @include mobile{
            max-height: 18px;
          }
        }
      }

      &:nth-child(2) {
        img {
          max-height: 20px;

          @include tablet-small{
            max-height: 16px;
          }
          @include mobile{
            max-height: 12px;
          }
        }
      }

      &:nth-child(3) {
        img {
          max-height: 30px;

          @include tablet-small{
            max-height: 26px;
          }
          @include mobile{
            max-height: 20px;
          }
        }
      }

      &:nth-child(4) {
        img {
          max-height: 12px;

          @include tablet-small{
            max-height: 10px;
          }
          @include mobile{
            max-height: 6px;
          }
        }
      }

      &:nth-child(5) {
        img {
          max-height: 40px;

          @include tablet-small{
            max-height: 34px;
          }
          @include mobile{
            max-height: 25px;
          }
        }
      }
    }
  }
}

.footer-social-logo{
  ul{
    @include display-flex(flex);

    li{
      + li{
        margin-left: 20px;
      }
    }

    a{
      display: inline-block;
      vertical-align: middle;
      opacity: 0.6;

      &:hover{
        opacity: 0.8;
      }
    }

    .icon{
      width: 20px;
      height: 20px;
    }
  }
}
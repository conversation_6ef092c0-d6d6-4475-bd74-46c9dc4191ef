@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Fira+Sans:wght@500&display=swap");

// global
@import "global/variables";
@import "global/mixin";
@import "global/reset";

// components
@import "component/form";
@import "component/component";
@import "component/spinner";
@import "component/range-slider";
// @import "component/modal";
@import "component/skeleton";
@import "component/modal_style";

// pages / section
@import "header";
@import "component/header_slider_nav";
@import "footer";
@import "banner";
@import "product-item";
@import "home-page";
@import "product-list";
@import "brands";
@import "cart-page";
@import "product-page";
@import "account";
@import "compare";
@import "order-form";
@import "blog";
@import "webpages";
@import "webpages/reward";

@import "sitemap";


// modal section
.modal-action {
    .button {
        +.button {
            margin-left: 20px;
        }
    }
}

@import "modal/modal-review";
@import "modal/modal-reward";
@import "modal/modal-quickview";
@import "modal/modal-scanner";
@import "modal/modal-age-verification";
@import "modal/modal-login";
@import "modal/modal-pdp-question";

div.cc-container {
    @include mobile {
        padding: 15px;
        display: block;
    }

    .cc-message {
        @include mobile {
            width: 100%;
            margin-bottom: 10px;
            font-size: 13px;
            line-height: 1.5;
            display: block;
        }
    }

    .cc-okbtn {
        .cc-btn {
            @include mobile {
                padding: 2px 15px;
                font-size: 12px;
                font-weight: 600;
            }
        }
    }
}

.ReactModal__Overlay {
    z-index: 100;
}

.warning-color {
    color: $warning-color;
}

// job page style provided by carol

.job-opportunities .my-group {
    width: 100%;
    max-width: 1656px;
    margin: auto;
    display: flex;
    flex-wrap: wrap;
    padding: 20px 0px 20px 0px
}

.job-opportunities .my-1-1 {
    width: 100%;
    padding: 20px;
}

.job-opportunities .my-1-2 {
    width: 49%;
    padding: 20px;
    box-sizing: border-box;
    display: inline-block;
    vertical-align: top;
}

.job-opportunities .my-3-5 {
    width: 59%;
    padding: 20px;
    box-sizing: border-box;
    display: inline-block;
    vertical-align: top;
}

.job-opportunities .my-2-5 {
    width: 39%;
    padding: 20px;
    box-sizing: border-box;
    display: inline-block;
    vertical-align: top;
}

.job-opportunities .my-image {
    width: 49%;
    padding: 20px;
    box-sizing: border-box;
}

.my-img-center {
    text-align: center;
}

.my-img-center img {
    width: 90%;
    max-width: 400px
}

.job-opportunities .my-arrow-left {
    background-image: url(https://cdn11.bigcommerce.com/s-964anr/images/stencil/original/image-manager/light-blue-arrow-right.png);
    background-repeat: no-repeat;
    background-position: center right;
    height: 101px;
    width: 100%;
    font-size: 26px;
    line-height: 101px;
    text-align: center;
}

.job-opportunities .my-arrow-right {
    background-image: url(https://cdn11.bigcommerce.com/s-964anr/images/stencil/original/image-manager/light-blue-arrow-left.png);
    background-repeat: no-repeat;
    background-position: center left;
    height: 101px;
    width: 100%;
    font-size: 26px;
    line-height: 101px;
    text-align: center;
}


@media only screen and (max-width: 760px) {

    .job-opportunities .my-1-2,
    .job-opportunities .my-3-5,
    .job-opportunities .my-2-5 {
        width: 100%;
    }
}

@media only screen and (max-width: 510px) {
    .job-opportunities .my-image {
        width: 100%;
        padding: 10px 0px;
    }
}

.order-listing-payment-link {
    .link-style {
        color: $warning-color;
    }
}

// user restriction style

.guest-user-mode,
.customer-group-73,
.customer-group-74,
.customer-group-75,
.customer-group-72,
.customer-group-32,
.customer-group-2 {

    .notice-banner {
        display: none !important;
    }

    .header-full-width-banner {
        display: none !important;
    }

    .header-primary-nav-section {
        display: none !important;
    }

    .hemburger-link {
        display: none !important;
    }

    .header-logo {
        margin-left: 0
    }

    .promo-text {
        display: none !important;
    }

    .search-form {
        display: none !important;
    }

    .header-right-nav {
        // display: none !important;

        >li {
            display: none !important;

            &.header-account-link,
            &.header-chat-link {
                display: block !important;
            }

            &.header-account-link {
                ul {
                    li {
                        display: none !important;

                        &:last-child {
                            display: block !important;
                            border-top: none !important;
                        }
                    }
                }
            }
        }
    }

    .header-middle-section {
        >.container {
            >.justify-space {
                // justify-content: center;
            }
        }
    }
}

.state-class-section {
    .tabcontent {
        display: none;
    }

    h2 {
        font-size: 20px;
    }

    .tablinks {
        @extend .button;
        margin: 0 5px 5px 0;
        font-size: 12px;
        padding: 8px 16px;

        &.active {
            background: $button-bg-hover;
            border: none;
            color: $button-text-color;
        }
    }

    .tabcontent {
        h2 {
            font-size: 20px;
        }

        h4 {
            font-size: 16px;
            line-height: 1.5;
        }

        .grid-container {
            display: grid;
            grid-template-columns: 30% 15%;

            @include tablet {
                grid-template-columns: 50% 30%;
            }

            @include mobile {
                display: block;
            }
        }

        .grid-item {
            padding: 5px 5px 5px 0;

            @include mobile {
                padding: 2px 0;
                &:nth-child(odd) {
                    margin-top: 15px;
                }
            }
        }
    }
}

.sub-category-section{
    margin-bottom: 30px;

    .section-title{
        margin-bottom: 15px;
        padding-bottom: 10px;
        line-height: 1;
        border-bottom: 1px solid #e5e5e5;
    }
}
.sub-category-list-view{
    display: flex;
    flex-wrap: wrap;

    .sub-category-link{
        margin: 0 5px 5px 0;
    }
}
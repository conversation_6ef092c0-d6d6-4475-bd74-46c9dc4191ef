/* ==================== PRODUCT IMAGE SECTION STYLE ==================== */

.product-detail-wrap {
  .breadcrumb-list {
    @include mobile {
      display: none;
    }
  }
}

.product-image-section {
  width: 35%;

  @include laptop {
    width: 45%;
  }

  @include tablet-small {
    width: 100%;
    margin-bottom: 30px;
  }

  .bc-product-single__top {
    @include tablet-small {
      max-width: 500px;
      margin-left: auto;
      margin-right: auto;
    }
  }
}

.product-main-image {
  aspect-ratio: 1/1;
  border: 1px solid $section-border-color;
  @include border-radius(4px);
  overflow: hidden;
  transition: transform .25s, visibility .25s ease-in;
  display: flex;
  justify-content: center;
  align-items: center;

  img {
    transition: 0.5s all ease-in-out;
  }

  >figure {
    height: 100%;
    width: 100%;

    >div {
      height: 100%;
      width: 100%;
      text-align: center;
      @include display-flex(flex);
      @include align-item(center);
      @include justify-content(center);


      >img {
        max-height: 100%;
        display: inline-block;
        width: 100%;
      }
    }
  }
}

.product-thumb-slider {
  margin-top: 20px;
  @include display-flex(flex);
  @include align-item(center);
  @include justify-content(center);

  +.product-main-image {
    width: calc(100% - 120px);
  }
}

.product-zoom-outer {
  background: none !important;
  background-repeat: no-repeat;
  max-width: 100%;
  width: 100%;
  max-height: 100%;
  height: 100%;
  padding: 10px;
  @include display-flex(flex);
  @include align-item(center);
  @include justify-content(center);
}

.skeleton-product-main-image {
  aspect-ratio: 1/1;

  .has-bg {
    height: 100%;
    width: 100%;
  }
}

.product-image-wrap {
  @include display-flex(flex);
  @include row;
}

.product-thumb-slider-replica {
  @include col;
  gap: 15px;
  margin-top: 15px;
}

.product-main-image-replica {
  @include col;
  gap: 10px;
}

.product-thumb-slider {
  padding: 0 40px;

  @include mobile {
    padding: 0 20px;
  }

  .slick-slider {
    .slick-arrow {
      opacity: 0.5;

      &:after {
        background-size: 24px auto;
      }

      &.slick-prev {
        left: -40px;
      }

      &.slick-next {
        right: -40px;
      }
    }
  }
}

.product-thumb-item {
  padding: 0 5px;

  .thumb-inner {
    border: 1px solid $section-border-color;
    display: inline-block;
    aspect-ratio: 1/1;
    overflow: hidden;
    text-align: center;
    @include display-flex(flex);
    @include align-item(center);
    @include justify-content(center);
    @include border-radius(4px);
    margin: 5px 0;
    cursor: pointer;
    max-width: 100px;
  }

  span {
    max-width: 100%;
    max-height: 100%;
    width: 100% !important;
    height: 100% !important;
    display: flex !important;
    @include align-item(center);
    @include justify-content(center);
  }

  img {
    max-width: 95%;
    max-height: 95%;
    display: inline-block;
    width: auto;
    height: auto;
  }
}

.product-thumb-item {
  &.img-selected {
    .thumb-inner {
      box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.2);
    }
  }
}

.product-video-section {
  margin-top: 60px;
}

.product-main-video {
  margin-bottom: 15px;

  iframe {
    aspect-ratio: 1/0.6;
    width: 100%;
    height: auto;
  }
}


.video-list {
  width: 100%;
  @include display-flex(flex);
  @include align-item(flex-start);
}

.video-list-item {
  @include display-flex(flex);
  @include align-item(flex-start);
  cursor: pointer;

  &:hover {
    color: $primary-color;
  }

  span {
    width: 120px;
    min-width: 120px;
    height: 80px;
    overflow: hidden;
    display: block;

    img {
      width: 100%;
      object-fit: cover;
      height: 100%;
    }
  }

  +.video-list-item {
    margin-left: 15px;
  }
}

/* ==================== PRODUCT DETAIL SECTION STYLE ==================== */

.product-detail-section {
  width: 65%;
  padding-left: 50px;

  @include laptop {
    width: 55%;
    padding-left: 12px;
  }

  @include tablet-small {
    width: 100%;
  }
}

.product-detail-section {
  .product-price-item {
    font-size: 22px;
    font-weight: $semi-bold;
  }

  .strike-price {
    font-size: 16px;
  }

  .product-price-wrap {
    margin-bottom: 30px;

    p {
      margin-bottom: 0;
    }
  }

  .bc-product__title {
    font-weight: $semi-bold;
    font-size: 22px;
    margin-bottom: 15px;

    @include mobile {
      font-size: 18px;
    }
  }
}

.product-brand-name {
  color: $grey-font-color;
  font-size: 14px;
  margin-bottom: 5px;
}

.product-review-link {
  margin-bottom: 0;
  margin-left: 10px;
}

.productView-info-list {
  margin-bottom: 30px;
}

.productView-info-item {
  +.productView-info-item {
    margin-top: 10px;
  }
}

.productView-info-name {
  font-weight: $semi-bold;
  margin-right: 10px;
}

.productView-option-list {
  margin-bottom: 30px;
  padding-bottom: 30px;
  border-bottom: 1px solid $section-border-color;
}

.productView-option-item {
  +.productView-option-item {
    margin-top: 25px;
  }

  .form-select {
    max-width: 250px;
  }
}

.form-label-option {
  font-weight: $bold;
  color: $body-font-color;
  display: block;
  margin-bottom: 8px;

  small {
    font-size: 12px;
    font-weight: normal;
    color: $grey-font-color;
    margin-left: 5px;
    float: none;
  }
}

.select-group {
  .form-select {
    width: auto;

    +.form-select {
      margin-left: 10px;
    }
  }
}

.style-swatch {
  display: inline-block;
  margin-right: 10px;
  margin-bottom: 10px;

  .form-label {
    padding: 10px 15px;
    border: 2px solid $black-border-color;
    line-height: 12px;

    &:before,
    &:after {
      display: none;
    }
  }

  .form-radio {
    &:checked {
      +.form-label {
        border-color: $primary-color;
        color: $primary-color;
      }
    }
  }
}

.product-qty-box {
  margin: 30px 0;

  .productView-info-name {
    margin-bottom: 5px;
    display: inline-block;
  }
}

.ad-notification {
  display: none;
}

/* ==================== PRODUCT TAB SECTION STYLE ==================== */

.product-tab-section {
  .tab-content {
    .product-description {
      * {
        font-family: $body-font !important;
        font-size: $body-font-size !important;
      }
    }
  }
}

.product-top-section {
  margin-bottom: 80px;
}

.product-tab-section {
  margin-bottom: 100px;

  @include tablet {
    margin-bottom: 80px;
  }

  @include mobile {
    margin-bottom: 50px;
  }
}

.product-description {

  h1,
  .h1,
  h2,
  .h2,
  h3,
  .h3,
  h4,
  .h4,
  h5,
  .h5,
  h6,
  .h6 {
    margin-bottom: 10px;
    margin-top: 20px;
  }

  *:empty:not(img) {
    display: none;
  }

  br {
    display: block !important;
  }
}

.review-list {
  @include display-flex(flex);
  @include flex-wrap(wrap);
  @include row;
}

.review-item {
  @include col;
  width: 50%;
  margin-top: 30px;

  &:first-child {
    margin-top: 0;
  }

  @include tablet {
    width: 100%;
  }

  h5 {
    font-weight: $semi-bold;
    font-size: 16px;
    margin-bottom: 10px;
    text-transform: uppercase;
  }

  .review-star {
    margin-bottom: 10px;
  }
}

.review-load-button {
  text-align: center;
  width: 100%;
  margin-top: 40px;

  button {
    text-align: center;
  }
}

.review-detail {
  color: $grey-font-color;

  p {
    margin-bottom: 0;
  }
}

.review-date {
  font-size: 12px;
  line-height: 1;
  margin-bottom: 5px;
}

.review-link-block {
  margin-bottom: 25px;
}

.review-star {
  span {
    width: 22px;
    height: 22px;
    display: inline-block;
    vertical-align: middle;

    svg {
      max-width: 100%;
      max-height: 100%;
      opacity: 0.2;
    }

    &.icon-fill {
      svg {
        fill: $primary-color;
        opacity: 1;
      }
    }

    +span {
      margin-left: 2px;
    }
  }
}

.product-detail-wrap {
  .product-list-block {
    .container {
      max-width: 100%;
      padding-left: 0;
      padding-right: 0;
    }
  }
}

// OPTION GRID

.loading-option-grid {
  min-height: 100px;

  .spinner-container {
    @include justify-content(flex-start);
  }

  .pog__row-item {
    .has-bg {
      width: 100%;
      height: 30px;
    }
  }
}

.table-option-grid {
  margin-bottom: 30px;
}

.pog__header {

  .table-cell {
    font-weight: 600;
    font-size: 16px;
    line-height: 20px;
    width: 14%;
    margin-left: 4.3%;
    text-align: center;
    text-transform: lowercase;

    &.table-cell-first {
      width: 14%;
      margin-left: 0;

      @include laptop {
        width: 18%;
      }

      @include mobile {
        width: 100%;
      }
    }
  }

  .pog__row-item {
    border-top: none;
  }
}

.pog__row-item {
  padding: 15px 0;
  border-top: 1px solid #e2e2e2;
  @include display-flex(flex);
  @include align-item(flex-end);
  @include flex-wrap(wrap);

  @include mobile {
    @include flex-wrap(wrap);
  }

  .table-cell {
    text-align: center;
    width: 10%;
    margin-left: 4.3%;

    @include laptop {
      width: 12%;
      margin-left: 3.3%;
    }

    @include mobile {
      width: 60px;
    }

    &:nth-child(2) {
      @include mobile {
        margin-left: 0;
      }
    }

  }

  .table-cell-first {
    width: 14%;
    text-align: left;
    margin-left: 0;
    font-weight: $semi-bold;

    @include laptop {
      font-size: 14px;
      width: 18%;
    }

    @include mobile {
      width: 100%;
      margin-bottom: 8px;
    }
  }

  .table-cell-input {
    input {
      outline: none;
      border: 1px solid #dcdcdc;
      @include border-radius(4px);
      background: none;
      width: 100%;
      max-width: 100%;
      text-align: center;
      margin: 0;
      height: 30px;
      line-height: 30px;
      display: inline-block;
      -webkit-appearance: none;
      -moz-appearance: textfield;
    }

    .table-cell-disable {
      border-color: #db3923;
    }

    .form-label {
      font-weight: 400;
      line-height: 16px;
      color: #9f9f9f;
      font-size: 12px;
      padding: 3px 2px 2px;
      margin-bottom: 0;
    }
  }
}

.instocknotify-wrapper {
  width: 100%;
  background: #F2F2F2;
  padding: 35px 40px;
  @include border-radius(6px);
  margin-top: 20px;

  @include tablet {
    padding: 30px;
  }

  @include mobile {
    padding: 20px;
  }
}

.instocknotify-form {
  @include justify-content(space-between);

  .form-input {
    margin-bottom: 10px;
  }
}

.instocknotify-icon {
  max-width: 90px;
  margin-right: 20px;

  @include tablet {
    display: none;
  }
}

.instocknotify-message {
  min-width: 160px;
  width: 160px;

  @include tablet {
    width: 100%;
  }

  p {
    margin-bottom: 0;
    color: #666666;
    font-weight: 500;
    line-height: 1.4;
  }
}

.instocknotify-form-inner {
  width: calc(100% - 300px);
  margin-left: 30px;

  @include tablet {
    width: 100%;
    margin-left: 0;
    margin-top: 20px;
  }
}

/* Chrome, Safari, Edge, Opera */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
input[type=number] {
  -moz-appearance: textfield;
}

//image slider
.image-loader {
  width: 510px;
  height: 510px;
  background-color: #fff;
}

.customer-badge-section {
  font-weight: 600;
  font-size: 14px;
  @include justify-content(center);

  @include mobile {
    font-size: 12px;
  }

  div {
    +div {
      margin-left: 8px;

      @include mobile {
        margin-left: 5px;
      }
    }
  }

  .customer-badge-icon {
    img {
      max-width: 20px;
    }
  }

  &.customer-vip {
    .customer-badge-name {
      color: #32b01d;
    }
  }

  &.customer-super-vip {
    .customer-badge-name {
      color: #8263dc;
    }
  }

  &.customer-vip-pro {
    .customer-badge-name {
      color: #d91d4a;
    }
  }

  &.customer-distributors {
    .customer-badge-name {
      color: #8263dc;
    }
  }

  &.customer-tcd {
    .customer-badge-name {
      color: #8263dc;
    }
  }

  &.customer-mvd {
    .customer-badge-name {
      color: #8263dc;
    }
  }
}

.price-wrapper {
  position: relative;
}

.customer-badge-id-overlay {
  font-size: 30px;
  opacity: .25;
  line-height: 40px;
  font-weight: 600;
  position: absolute;
  left: 50%;
  transform: translate(-50%, 0);

  @include mobile {
    font-size: 26px;
  }
}

.product-price-wrapper {
  >* {
    line-height: 40px;
  }
}

.product-price-wrapper {
  min-height: 40px;
}

.bc-product-single {
  .product-detail-section {
    .customer-badge-section {
      @include justify-content(flex-start);
    }

    .customer-badge-id-overlay {
      transform: translate(0, 0);
      left: auto;
    }
  }
}

.review-text-wrapper {
  margin-left: 20px;
  color: rgb(79, 116, 43);
  cursor: pointer;

  @include mobile {
    width: 100%;
    margin-left: 0;
    margin-top: 20px;
  }
}

.review-text-wrapper :hover {
  color: #000
}

.pdp-contact-form {
  margin-bottom: 20px;

  .pdp-contact-form-link {
    color: #09254a;
    text-decoration: underline;
    cursor: pointer;
    display: inline-block;
    font-size: 16px;
    font-weight: 600;
  }
}

.out-of-stock-btn {
  margin-bottom: 30px !important
}

// social share links

.social-share-links {
  @include display-flex(flex);
  @include align-item(center);

  button {
    margin: 0 10px 0 0 !important;
    padding: 0;
    border: 1px solid $primary-color !important;
    @include border-radius(50%);
    outline: none;
    width: 28px;
    height: 28px;
    @include display-flex(flex);
    @include align-item(center);
    @include justify-content(center);
    background: none;
    cursor: pointer;
  }
}

// 3d view

.product-main-image-wrapper {
  position: relative;

  .product-image-3d-action {
    width: 48px;
    cursor: pointer;
    height: 48px;
    background: $success-color;
    border-radius: 24px;
    padding: 6px;
    position: absolute;
    bottom: 15px;
    right: 15px;
    border: 2px solid #ffffff
  }
}

.customer-price-list {
  font-size: 12px;
  margin-top: -20px;

  .productView-info-item{
    + .productView-info-item{
      margin-top: 8px;
    }
  }

  .productView-info-name {
    font-weight: 800;
    margin-right: 6px;
    display: inline-block;
    min-width: 55px;

    &.price-distro {
      color: #8263dc;
    }

    &.price-vip {
      color: #32b01d;
    }
  }

  .productView-info-value{
    font-weight: 500;
  }

  .customer-price-list-inner{
    padding: 10px 15px;
    background: #fafafa;
    border-radius: 6px;
    border: 1px solid #eaeaea;
    display: inline-block;
  }
}
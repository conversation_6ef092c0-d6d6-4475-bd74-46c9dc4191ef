/* ==================== LOGIN PAGE STYLE ==================== */

.login-form-block,
.login-info-block {
    width: 50%;

    @include mobile {
        width: 100%;
    }
}

.login-form-block {
    @include mobile {
        margin-bottom: 50px;
    }

    .gcaptcha-wrapper {
        text-align: center;

        >div {
            display: inline-block;
        }
    }
}

.login-section {
    .container {
        max-width: 1100px;
    }

    .row {
        margin-left: -20px;
        margin-right: -20px;
    }

    .col {
        padding-left: 20px;
        padding-right: 20px;
    }

    .form-actions {
        text-align: center;

        button {
            margin-right: 20px;
        }
    }
}

.register-button {
    margin-left: 70px;
}

.new-customer-fact-list {
    li {
        margin-top: 10px;
    }
}

/* ==================== ACCOUNT LAYOUT STYLE ==================== */

.account-content {
    border: 1px solid $border-color;
    border-radius: 8px;
    // overflow: hidden;

    @include tablet {
        border-radius: 0;
        border: none;
    }
}

.page-sidebar-inner {
    padding: 25px;
    border-right: 1px solid $border-color;
    background: $section-bg;
    height: 100%;

    @include tablet {
        padding: 20px;
        border: none;
        border-radius: 8px;
    }
}

.account-page-wrapper {
    .page-sidebar {

        width: 350px;

        ul {
            list-style: none;
        }

        @include tablet {
            width: 100%;
            border-right: none;
        }

        +.page-content {

            width: calc(100% - 350px);

            @include tablet {
                width: 100%;
            }
        }
    }

    .page-content-inner {
        padding: 35px 35px 35px 10px;

        @include tablet {
            padding: 0;
            margin-top: 30px;
        }

        @include mobile {
            margin-top: 0;
        }
    }


    .breadcrumb-list {
        @include mobile {
            display: none;
        }
    }

    .page-heading-section {

        .page-title {
            font-size: 18px;

            @include mobile {
                font-size: 16px;
            }
        }
    }

    .page-sidebar-inner {
        @include tablet {
            @include display-flex(flex);
            @include justify-content(space-between);
            @include flex-wrap(wrap);
        }
    }
}

.account-page-content {
    width: 70%;
    padding-right: 100px;

    @include tablet-small {
        padding-right: 30px;
    }

    @include mobile {
        width: 100%;
        padding-right: 8px;
    }
}

.account-page-sidebar {
    width: 30%;

    @include mobile {
        width: 100%;
        margin-top: 30px;
    }

    .account-heading {
        margin-bottom: 10px;
    }
}

.definitionList {
    font-size: 13px;
    margin-bottom: 20px;

    dt {
        float: left;
        color: $grey-font-color;
        margin-right: 5px;
    }

    dd {
        margin-bottom: 8px;
    }
}

.sidebar-address {
    margin-left: 0;
    list-style: none;
    font-size: 13px;

    li {
        +li {
            margin-top: 8px;
        }
    }
}

.account-sidebar-block {
    +.account-sidebar-block {
        margin-top: 50px;

        @include tablet {
            margin-top: 40px;
        }

        @include mobile {
            margin-top: 30px;
        }
    }
}

.new-account-info {
    color: #2d2d2d;
    margin-bottom: 40px;

    @include mobile {
        margin-bottom: 20px;
    }
}

.new-account-service {
    margin: 0;
    list-style: none;
    @include display-flex(flex);
    @include align-item(flex-start);
    @include justify-content(center);
    @include flex-wrap(wrap);

    li {
        text-align: center;
        padding: 0 20px;

        @include mobile {
            padding: 0 10px;
        }

        @include mobile-small {
            width: 50%;
            margin-bottom: 30px;
        }

        span {
            width: 100px;
            display: inline-block;
            margin-bottom: 10px;

            @include mobile {
                width: 60px;
            }
        }

        p {
            margin-bottom: 0;
            font-size: 13px;
            font-weight: 500;
            line-height: 1.3;
            color: #2d2d2d;

            @include mobile {
                font-size: 12px;
            }
        }
    }
}

/* ==================== ACCOUNT NAVIGATION STYLE ==================== */

.account-nav-list {
    text-transform: uppercase;
    margin: 0;

    >li {
        &:first-child {
            margin-top: 0;
        }
    }

    li {
        cursor: pointer;
    }

    ul {
        margin-left: 20px;
        margin-bottom: 0;
        text-transform: none;
    }

    li {

        margin-top: 15px;

        a {
            display: block;

            &:hover {
                color: $black-color;
            }
        }

        &.active {

            >a,
            >span {
                color: $black-color;
                font-weight: $semi-bold;
            }
        }
    }
}

/* ==================== ORDER LIST PAGE STYLE ==================== */

.all-order-selecction {
    margin-bottom: 30px;
}

.account-heading {
    border-bottom: 1px solid $border-color;
    text-transform: uppercase;
    font-weight: $bold;
    font-size: 18px;
    padding-bottom: 10px;
    margin-bottom: 40px;

    @include tablet-small {
        font-size: 16px;
    }
}

.order-list {
    margin: 0;
    list-style: none;
}

.order-item-select {
    width: 50px;

    .form-checkbox {
        +.form-label {
            padding-left: 0;
            width: 15px;
            height: 15px;
        }
    }
}

.account-list {

    .account-listItem {
        list-style: none;
    }

    .account-orderStatus-label {
        float: right;
    }

    .account-product-details {
        display: flex;
    }
}


.account-listItem {
    +.account-listItem {
        margin-top: 30px;
        padding-top: 30px;
        border-top: 1px solid $border-color;

        @include mobile {
            margin-top: 20px;
            padding-top: 20px;
        }
    }
}

.account-product-figure {
    width: 100px;

    @include tablet-small {
        width: 80px;
    }

    img {
        width: 100%;
    }
}

.account-product-body {
    width: calc(100% - 100px);

    @include tablet-small {
        width: calc(100% - 80px);
    }
}

.account-product-title {
    font-size: 16px;
    font-weight: $semi-bold;
    margin-bottom: 0;

    @include tablet {
        font-size: 14px;
    }
}

.order-return-link {
    margin-bottom: 0;
    margin-top: 5px;
    font-size: 14px;
}

.account-product-description {
    margin-bottom: 20px;

    .customer-badge-id-overlay {
        transform: translate(0, 0);
        left: auto;
    }

    .customer-badge-section {
        margin-top: 10px;
        @include justify-content(flex-start);
    }
}

.account-right-tag {
    float: right;
    min-width: 100px;
    text-align: right;

    h6 {
        margin-bottom: 0;
        padding: 8px 12px;
        margin: 0;
        background: lighten($black-color, 50%);
        color: $white-color;
        @include border-radius(6px);
        font-size: 12px;
        line-height: 1;
        display: inline-block;
    }

    .customer-badge-id-overlay {
        transform: translate(0, 0);
        left: auto;
    }
}

.account-product-detail {
    width: 200px;
    font-size: 13px;

    @include mobile {
        width: 100%;
    }

    +.account-product-detail {
        @include mobile {
            margin-top: 20px;
        }
    }

    p {
        margin-bottom: 0;
    }
}

.order-detail-payment-link{
    .account-heading{
        span{
            color: $warning-color;
        }
    }
}

.order-shipping-title {
    h5 {
        font-size: 18px;
    }
}

.order-detail-total-section {
    padding: 20px 30px;
    background: $section-bg;
    margin-top: 30px;
    @include border-radius(4px);
    font-size: 16px;

    @include tablet {
        padding: 15px 20px;
        font-size: 15px;
    }

    @include mobile {
        font-size: 14px;
    }
}

.order-total-item {
    +.order-total-item {
        margin-top: 20px;
        padding-top: 20px;
        border-top: 1px solid $section-border-color;

        @include tablet {
            margin-top: 12px;
            padding-top: 12px;
        }
    }
}


// ACCOUNT SIDEBAR

.account-sidebar-toggle {
    margin-bottom: 10px;

    .account-sidebar-toggle-link {
        background: $primary-color;
        color: $white-color;
        padding: 15px 20px;
        margin: 0;
        @include border-radius(4px);
        font-size: 14px;
        position: relative;
        line-height: 1;

        &::after {
            position: absolute;
            width: 20px;
            height: 20px;
            content: " ";
            background-image: url("data:image/svg+xml,%3Csvg height='48' viewBox='0 0 48 48' width='48' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M14.83 16.42l9.17 9.17 9.17-9.17 2.83 2.83-12 12-12-12z' fill='%23ffffff'/%3E%3Cpath d='M0-.75h48v48h-48z' fill='none'/%3E%3C/svg%3E");
            background-position: center center;
            background-size: 20px auto;
            background-repeat: no-repeat;
            right: 15px;
            top: 50%;
            margin-top: -10px;
        }
    }

    &.active {
        &::after {
            transform: rotate(180deg);
        }
    }
}

.account-representative-nav {
    margin-top: 50px;

    @include tablet {
        margin-top: 0;
    }

    @include mobile {
        margin-top: 30px;
        width: 100%;
    }
}

.order-user-info-container {

    @include mobile {
        display: none;
    }

    &.show-sidebar {
        display: block;
    }
}

.representative-info-headig {
    font-size: 15px;
    margin-bottom: 6px;
    color: $success-color ;
    font-weight: 500;
}

.representative-info {
    font-size: 16px;
    line-height: 24px;
    @include display-flex(flex);

    .icon {
        width: 16px;
        height: 16px;
        margin-right: 10px;
    }
}

.rep-info-item {
    +.rep-info-item {
        margin-top: 10px;
        padding-top: 10px;
        border-top: 1px solid #e8e8e8;
    }
}

/* ==================== FORGOT PASSWORD STYLE ==================== */

.forgot-password-form,
.create-account-form {
    margin-top: 50px;
}

/* ==================== ADDRESS PAGE STYLE ==================== */

.address-list {
    margin-bottom: 0;
    list-style: none;
}

.address-item {
    width: 25%;
    margin-bottom: 20px;

    @include desktop-small {
        width: 33.33%;
    }

    @include tablet-small {
        width: 50%;
    }

    @include mobile-small {
        width: 100%;
    }

    .form-actions {
        .button {
            +.button {
                margin-left: 10px;
            }
        }
    }
}

.adddress-item-inner {
    padding: 25px;
    background: $section-bg;
    height: 100%;
}

.address-title {
    margin-bottom: 20px;
}

.address-detail {
    margin-left: 0;
    list-style: none;

    li {
        +li {
            margin-top: 5px;
        }
    }
}

.add-address-link {
    .adddress-item-inner {
        cursor: pointer;
        text-align: center;

        &:hover {
            color: $primary-color;
        }
    }

    .add-icon {
        position: relative;
        display: inline-block;
        width: 32px;
        height: 32px;

        svg {
            max-width: 100%;
            max-height: 100%;
        }
    }

    p {
        text-transform: uppercase;
        font-weight: $bold;
        margin: 5px 0 0 0;
        font-size: 16px;
    }
}

// account pagination

.account-pagination {
    margin-top: 60px;
    @include display-flex(flex);
    @include align-item(center);
    @include justify-content(center);

    .pagination-page-count {
        margin: 0 20px;
    }
}

// MESSAGE PAGE

.message-list {
    list-style: none;
    margin: 0;
    margin-bottom: 50px;
}

.message-listItem {
    +.message-listItem {
        margin-top: 30px;
        padding-top: 30px;
        border-top: 1px solid $border-color;
    }
}

.message-body {
    p {
        margin-top: 10px;
        margin-bottom: 0;
    }

    .message-status {
        font-weight: 500;
    }
}

/* ==================== BULK ORDER PAGE STYLE ==================== */

.bulk-order-catalogue-section {
    +.bulk-order-catalogue-section {
        margin-top: 40px;
    }
}

.bulk-order-catalogue-product {
    margin-bottom: 24px;
}

.catalogue-product-image {
    width: 100px;

    @include mobile {
        width: 80px;
    }

    img {
        max-width: 80px;
        max-height: 80px;

        @include mobile {
            max-width: 60px;
            max-height: 60px;
        }
    }
}

.catalogue-product-detail {
    max-width: calc(100% - 150px);

    @include mobile {
        max-width: calc(100% - 130px);
    }

    h6 {
        font-size: 16px;
        margin-bottom: 10px;

        @include tablet {
            font-size: 15px;
        }

        @include mobile {
            font-size: 14px;
        }
    }

    p {
        margin-bottom: 10px;
        font-size: 14px;
        line-height: 1.2;
    }
}

.catalogue-toggle-button {
    span {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        background: lighten($black-color, 90%);
        text-align: center;
        cursor: pointer;

        svg {
            max-width: 12px;
            max-height: 100%;
        }
    }
}

.bulk-orer-card {
    font-size: 14px;

    p {
        margin-bottom: 15px;

        @include mobile {
            margin-bottom: 10px;
        }

        &:last-child {
            margin-bottom: 0;
        }

        strong {
            margin-right: 5px;
            display: inline-block;
            vertical-align: middle;
        }
    }
}

.bulk-order-catalogue-table-section {
    &.hide-table {
        display: none;
    }

    @include tablet-small {
        overflow: auto;
    }

    &.bulk-order-detail-table-section {
        @include laptop {
            overflow: auto;
        }
    }
}

.bulk-order-table {
    width: 100%;
    text-align: left;
    font-size: 14px;

    th,
    td {
        padding: 10px;
    }

    th {
        border-bottom: 1px solid #eff2f5;
        font-weight: $medium;
        text-transform: uppercase;
    }

    tbody {
        td {
            border-bottom: 1px dashed #e4e6ef;
        }
    }

    a {
        text-decoration: underline;
    }

    .table-col-input-qty {
        width: 130px;
    }

    .table-col-flavor {
        width: 280px;
    }

    .table-col-upc {
        width: 200px;
    }

    .bulk-order-table-input {
        input {
            width: 50px;
            padding: 6px 10px;
            text-align: center;
        }
    }

    .table-col-date {
        width: 200px;
    }

    .table-col-po {
        width: 100px;
    }

    .table-col-product {
        min-width: 350px;
    }

    .table-col-order-id {
        width: 200px;
    }

    .table-col-order-value {
        width: 200px;
    }

    .table-col-customer-rep {
        width: 250px;
    }

    .table-col-status {
        width: 100px;
    }

    .table-col-req-qty {
        width: 130px;
    }

    &.product-catalogue-table {
        @include tablet-small {
            min-width: 800px;
        }
    }

    &.po-order-table {
        @include tablet-small {
            min-width: 800px;
        }
    }

    &.bulk-order-status-table {
        @include mobile {
            min-width: 700px;
        }
    }

    &.bulk-order-detail-table {
        @include laptop {
            width: 1000px;
        }
    }

    &.bulk-order-history-table {
        @include mobile {
            width: 600px;
        }
    }
}

.table-product-name {
    margin: 0;
    list-style: none;

    li {
        display: inline-block;
        vertical-align: top;
        margin-bottom: 5px;
        margin-right: 5px;
        background: $grey-bg;
        font-size: 13px;
        padding: 5px 8px;
        border-radius: 4px;
        line-height: 1;
        width: 160px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
}

.bulk-order-sticky-section {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    background: $white-color;
    padding: 30px 0;
    box-shadow: 0 0 10px 1px rgba(0, 0, 0, 0.1);
    border-top: 1px solid $border-color;
    z-index: 5;

    @include tablet-small {
        padding: 20px 0;
    }

    @include mobile {
        padding: 15px 0;
    }
}

.bulk-order-sticky-inner {
    padding: 0 136px;

    @include laptop {
        padding: 0 115px;
    }

    @include tablet {
        padding: 0 100px;
    }

    @include tablet-small {
        padding: 0 60px;
    }

    @include mobile {
        padding-left: 0;
    }
}

.bulk-order-total {
    @include mobile {
        @include justify-content(flex-start);
    }
}

.bulk-order-total-text {
    font-size: 18px;
    min-width: 60px;
}

.bulk-order-total-label {
    small {
        margin-left: 5px;
    }
}

.bulk-order-total {
    .bulk-order-total-text {
        // width: 132px;
        text-align: center;
        margin-left: 20px;
    }
}

.bulk-order-table-action {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid $border-color;

    @include mobile {
        margin-top: 15px;
        padding-top: 15px;
    }

    .button {
        +.button {
            margin-left: 20px;
        }
    }
}

.bulk-order-top-action {
    padding: 12px 0;
    border-top: 1px solid $border-color;
    border-bottom: 1px solid $border-color;
    margin-bottom: 32px;

    .button {
        &.button-small {
            @include mobile {
                padding: 5px 10px;
                font-size: 10px;
            }
        }
    }
}

.bulk-order-steps {

    ul {
        margin: 0;

        li {
            font-weight: $medium;
            cursor: pointer;

            @include mobile {
                font-size: 12px;
            }

            +li {
                margin-left: 48px;

                @include tablet-small {
                    margin-left: 25px;
                }

                @include mobile {
                    margin-left: 10px;
                }
            }

            span {
                display: inline-block;
                vertical-align: middle;
                color: #A0B7E3;
                background: #F6F8FA;
                width: 36px;
                height: 36px;
                @include border-radius(50%);
                text-align: center;
                line-height: 36px;
                margin-right: 10px;

                @include tablet-small {
                    margin-right: 6px;
                    width: 26px;
                    height: 26px;
                    line-height: 26px;
                    font-size: 12px;
                }

                @include mobile {
                    margin-right: 4px;
                }
            }

            &.active {
                span {
                    background: $primary-color;
                    color: $white-color;
                }
            }
        }

    }
}

.bulk-order-steps-action {
    .button {
        +.button {
            margin-left: 16px;
        }
    }
}

.bulk-order-template-title {
    .multi-select {
        width: 250px;
    }

    .page-title {
        padding: 0;
        border: none;

        @include mobile {
            width: 100%;
            margin-bottom: 15px;
        }
    }
}

.page-title-sticky {
    position: -webkit-sticky;
    position: sticky;
    top: 0;
    background: $white-color;
    padding-top: 15px;
    z-index: 2;

    .page-heading-section {
        margin-bottom: 20px;
    }
}

.bulk-order-filter {
    p {
        margin-bottom: 5px;
        font-size: 14px;
    }
}

// order tracking page

.track-order-link{
    margin-top: 10px;
    display: inline-block;
}

.order-detail-tracking-section {
    width: 100%;
    
    iframe {
        width: 100%;
        height: 1000px;

        @include mobile{
            height: 1500px;
        }
    }
}

.order-tracking-banner{
    margin-bottom: 10px;


    .row{
        display: flex;
        flex-wrap: wrap;
    }

    .col-6{
        width: 50%;
        margin-bottom: 24px;
    }
}

// Bulk order sorting

.table-sorting-icon-inner {
    font-size: 12px;
    margin-left: 5px;
    opacity: 0.5;
}

.order-search-header {
    position: relative;
    margin-bottom: 50px;

    .site-search-block {
        max-width: 500px;
        position: relative;

        @include mobile {
            max-width: 100%;
            width: 100%;
        }
    }

    .form-input {
        width: 100%;
        border: 2px solid #9ab8e7;
        border-radius: 5px;
        font-size: 16px;
        font-weight: 500;
        line-height: 20px;
        padding: 14px 50px 14px 15px;
    }

    .site-search-button {
        position: absolute;
        top: 50%;
        margin-top: -12px;
        right: 20px;
        width: 24px;
        height: 24px;
        background: none;
        outline: none;
        border: none;
        opacity: 0.6;

        svg {
            max-width: 100%;
            max-height: 100%;
        }
    }
}
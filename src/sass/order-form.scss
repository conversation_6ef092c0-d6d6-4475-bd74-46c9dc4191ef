.order-form-section {
    width: 900px;
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 100px;
    max-width: 100%;

    @include tablet-small {
        margin-bottom: 50px;
    }

    .col {
        width: 50%;

        @include mobile {
            width: 100%;
        }

        h6 {
            @include mobile {
                margin-bottom: 15px;
            }
        }

        .sample-download-link {
            font-size: 14px;
            text-decoration: underline;
            color: $primary-color;
            margin-left: 20px;

            @include mobile{
                font-size: 12px;
                margin-left: 10px;
            }

            .icon {
                width: 16px;
                height: 16px;
                margin-right: 5px;

                @include mobile{
                    width: 12px;
                    height: 12px;
                    margin-right: 2px;
                }

                svg {
                    vertical-align: top;
                }
            }
        }

        &.order-input-section {
            padding-right: 50px;

            @include tablet-small {
                padding-right: 30px;
            }

            @include mobile {
                padding-right: 12px;
            }
        }

        &.order-csv-section {
            padding-left: 50px;
            border-left: 1px solid $border-color;

            @include tablet-small {
                padding-left: 30px;
            }

            @include mobile {
                padding-left: 12px;
                border-left: none;
                margin-top: 20px;
                padding-top: 20px;
                border-top: 1px solid $border-color;
            }
        }
    }

    .bc-cart-item-error {
        margin-top: 5px;
        margin-bottom: 10px;
        font-size: 14px;
    }
}

.scanner-switch-wrapper{
    position: relative;
}

.scanner-checkbox-switch{
    position: absolute;
    right: 0;
    top: 5px;
    min-width: 158px;

    .form-label{
        text-decoration: underline;
        color: $body-font-color;
        font-weight: 500;
    }
}

.csv-import-section {
    input {
        padding: 3px;
        padding-right: 15px;
        width: 100%;
        @include border-radius(6px);
        border: 1px solid $border-color;
        cursor: pointer;
    }

    input[type=file]::file-selector-button {
        padding: 8px 20px;
        outline: none;
        border: none;
        background: $button-bg-hover;
        @include border-radius(4px);
        margin-right: 15px;
        font-family: $input-font;
        color: $white-color;
        cursor: pointer;

        &:hover {
            background: $button-bg;
        }
    }

    .csv-import-button {
        text-align: center;
    }
}

.order-form-page {
    .cart-top-action-section {
        position: relative;
        top: auto;
        right: auto;
    }
}

.bc-cart {
    &.bc-cart-order-form {
        .bc-cart-header {
            @include display-flex(flex);

            >div {
                @include tablet-small {
                    display: none;
                }

                &.cart-col-1,
                &.cart-col-2 {
                    display: block;
                }
            }
        }

        .cart-col-price {
            @include tablet-small {
                text-align: left;
                line-height: normal;
            }

            @include mobile{
                width: auto;
                margin-left: 40px;
            }

            @include mobile-small{
                margin-left: 0;
            }

            @include x-small{
                margin-left: 0;
            }

            .cart-price-wrapper {
                @include tablet-small {
                    line-height: normal;
                }
            }
        }
    }
}
@keyframes spinner {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.spinner-container {
  text-align: center;
  height: 100%;
  @include display-flex(flex);
  width: 100%;
  @include align-item(center);
  @include justify-content(center);
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid $button-bg-hover;
  border-radius: 50%;
  animation: spinner 2s linear infinite;
  display: inline-block;
  opacity: 0.5;
}

.button-loading-spinner {
  width: 15px;
  height: 15px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid $button-bg-hover;
  border-radius: 50%;
  animation: spinner 0.5s linear infinite;
  display: inline-block;
  opacity: 0.5;
  margin-left: 10px;
}

.form-increment {
  .spinner-container {
    width: 40px;

    .loading-spinner {
      width: 26px;
      height: 26px;
    }
  }
}

// button loader
.button-loader {
  display: inline-block;
  width: 185px;
  height: 58px;
  background: $button-bg;
  @include border-radius(4px);
  cursor: not-allowed;
  position: relative;
}

.button-loader-inner{
  position: absolute;
  top: 50%;
  left: 50%;
  margin-top: -13px;
  margin-left: -13px;
  width: 26px;
  height: 26px;

  &:after{
    content: " ";
    display: inline-block;
    width: 26px;
    height: 26px;
    border-radius: 50%;
    border: 2px solid #fff;
    border-color: #fff transparent #fff transparent;
    animation: lds-dual-ring 1.2s linear infinite;
  }
}

@keyframes lds-dual-ring {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
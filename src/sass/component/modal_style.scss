.ReactModal__Overlay {
    background-color: rgba(0, 0, 0, 0.5) !important;
}

.ReactModal__Content {
    padding: 0 !important;
    background: none !important;

    border: none !important;
    // inset: 0 !important;
    
    inset: auto !important;
    left: 50% !important;
    top: 50% !important;
    transform: translate(-50%, -50%);

    @include display-flex(flex);
    @include align-item(center);
    @include justify-content(center);

    max-width: 90% !important;
    max-height: 100% !important;
}

.modal-outer {
    padding: 0;
    max-width: 95%;

    @include mobile {
        width: 100%;
    }
}

.modal-inner {
    @include display-flex(flex);
    width: 100%;
    @include align-item(flex-start);
    @include justify-content(center);
}

.modal-body-outer {
    padding: 20px;
    width: 100%;
    background: $white-color;
    @include border-radius(5px);
    position: relative;

    @include mobile {
        padding-left: 10px;
        padding-right: 10px;
    }
}

.modal-body{
    max-height: calc(100vh - 200px);
    overflow-y: auto;
    overflow-x: hidden;
    padding: 0 10px;

    @include mobile{
        max-height: calc(100vh - 150px);
    }
}

.modal-close-button {
    position: absolute;
    right: 10px;
    top: 10px;
    width: 24px;
    height: 24px;
    background: none;
    border: none;
    outline: none;
    text-align: center;
    line-height: 24px;
    cursor: pointer;
    z-index: 2;

    svg {
        width: 24px;
        height: 24px;
    }
}

.modal-header {
    margin-bottom: 20px;
    padding-bottom: 10px;

    border-bottom: 1px solid $border-color;
}

.modal-title {
    margin: 0;
    text-align: center;
    font-weight: $bold;
    text-transform: uppercase;
    font-size: 18px;
}
.body-section {
    position: relative;
    z-index: 5;
    min-height: 400px;
}

/* =============== DROPDOWN STYLE =============== */

.has-dropdown {
    position: relative;

    &:hover {
        >.dropdown-list {
            display: block;
        }
    }
}

.has-arrow {
    >a {
        &:after {
            margin-left: 6px;
            width: 8px;
            height: 8px;
            content: " ";
            display: inline-block;
            background-position: center center;
            background-repeat: no-repeat;
            background-size: 100%;
            background-image: url("data:image/svg+xml,%3Csvg version='1.1' xmlns='http://www.w3.org/2000/svg' width='1024' height='1024' viewBox='0 0 1024 1024'%3E%3Ctitle%3E%3C/title%3E%3Cg id='icomoon-ignore'%3E%3C/g%3E%3Cpath d='M548.549 778.94l460.335-460.488c20.155-20.206 20.155-52.943 0-73.2-20.155-20.206-52.892-20.206-73.047 0l-423.81 423.963-423.81-423.912c-20.155-20.206-52.892-20.206-73.098 0-20.155 20.206-20.155 52.994 0 73.2l460.331 460.488c19.947 19.896 53.198 19.896 73.098-0.051z'%3E%3C/path%3E%3C/svg%3E");
        }
    }
}

.dropdown-list {
    background: #fff;
    border: 1px solid #f3f3f3;
    padding: 7px 10px;
    position: absolute;
    top: 100%;
    left: 50%;
    @include border-radius(2px);
    min-width: 220px;
    @include transform(translate(-50%, 0));
    display: none;
    @include box-shadow(0 1px 5px 2px rgba(0, 0, 0, .02));

    &.dropdown-small {
        min-width: 100px;
    }

    &.dropdown-mid {
        min-width: 230px;

        li{padding-top: 2px;
            margin-top: 2px;
            font-size: 14px;
            + li{
                border-top: 1px solid $border-color;
                
            }
        }
    }

    a {
        padding: 5px 0;
        display: block;

        &:hover {
            color: $primary-color;
        }
    }
}

/* ==================== SECTION TITLE ==================== */

.section-title {
    font-weight: 600;
    font-size: 24px;
    margin-bottom: 25px;

    @include tablet {
        font-size: 20px;
        margin-bottom: 20px;
    }

    @include mobile {
        font-size: 18px;
        margin-bottom: 12px;
    }

    p {
        font-size: 16px;
        line-height: 16px;
        margin-bottom: 0;
        margin-top: 8px;
    }
}

.page-heading-section {
    margin-bottom: 40px;
    position: relative;
}

/* ==================== PAGE TITLE ==================== */

.page-title {
    margin-bottom: 0;
    font-size: 22px;
    text-transform: uppercase;
    border-bottom: 1px solid $border-color;
    padding-bottom: 10px;
    font-weight: $semi-bold;

    &.title-small{
        font-size: 16px;
    }

    @include mobile{
        font-size: 20px;
    }

    &.remove-border {
        border-bottom: none;
    }

    &.page-title-mobile{
        @include mobile{
            border: none;
            font-size: 20px;
            padding-bottom: 0;
            margin-bottom: 20px;
        }
    }
}

.mobile-title-col{
    @include col;
    width: 100%;
}

/* ==================== SLICK SLIDER ==================== */

.slick-list {
    width: 100%;
}

.slick-slider {
    position: relative;
    width: 100%;

    .slick-dots {
        position: absolute;
        bottom: 30px;

        @include tablet {
            bottom: 15px;
        }

        li {
            width: auto;
            height: auto;
            padding: 0;
            margin: 0 3px;

            @include tablet {
                margin: 0 2px;
            }

            button {

                width: 12px;
                height: 12px;
                @include border-radius(50%);
                border: 2px solid $primary-color;
                @include transition(all 0.4s ease);

                @include tablet {
                    width: 8px;
                    height: 8px;
                }

                &:before {
                    display: none;
                }

                &:hover {
                    background-color: $primary-color;
                }
            }

            &.slick-active {
                button {
                    background-color: $primary-color;
                }
            }
        }
    }

    .slick-arrow {
        width: 40px;
        height: 60px;
        display: inline-block;
        background: none;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        z-index: 15;
        opacity: 0.1;
        @include transition(all 0.4s ease);

        @include tablet {
            width: 30px;
            height: 30px;
        }

        @include mobile {
            display: none;
        }

        &:before {
            display: none;
        }

        &:after {
            content: " ";
            display: inline-block;
            width: 100%;
            height: 100%;
            background-position: center center;
            background-repeat: no-repeat;
            background-size: 100%;
            background-image: url("data:image/svg+xml,%3Csvg version='1.1' xmlns='http://www.w3.org/2000/svg' width='1024' height='1024' viewBox='0 0 1024 1024'%3E%3Ctitle%3E%3C/title%3E%3Cg id='icomoon-ignore'%3E%3C/g%3E%3Cpath d='M548.549 778.94l460.335-460.488c20.155-20.206 20.155-52.943 0-73.2-20.155-20.206-52.892-20.206-73.047 0l-423.81 423.963-423.81-423.912c-20.155-20.206-52.892-20.206-73.098 0-20.155 20.206-20.155 52.994 0 73.2l460.331 460.488c19.947 19.896 53.198 19.896 73.098-0.051z'%3E%3C/path%3E%3C/svg%3E");
        }

        &.slick-next {
            right: 30px;
            left: auto;

            @include tablet {
                right: 15px;
            }

            &:after {
                @include transform(rotate(-90deg));
            }
        }

        &.slick-prev {
            left: 30px;
            right: auto;

            @include tablet {
                left: 15px;
            }

            &:after {
                @include transform(rotate(90deg));
            }
        }

        &:hover {
            opacity: 0.3;
        }
    }
}

/* ==================== SOCIAL ICONS ==================== */

.social-icon-list {
    margin-top: 40px;

    li {
        a {
            @include display-flex(flex);
            @include align-item(center);
            @include justify-content(center);
            @include border-radius(50%);
            width: 36px;
            height: 36px;
            background: $primary-color;
            cursor: pointer;
            border: 2px solid $primary-color;

            svg {
                fill: $white-color;
                width: 14px;
                height: 14px;
            }

            &:hover {
                background: darken($primary-color, 5%);
            }

            &.icon-facebook {
                background: #486ba4;
                border-color: #486ba4;

                &:hover {
                    background: lighten(#486ba4, 10%);
                }
            }

            &.icon-twitter {
                background: #4da6e9;
                border-color: #4da6e9;

                &:hover {
                    background: lighten(#4da6e9, 10%);
                }
            }

            &.icon-instagram {
                background: #ec0503;
                border-color: #ec0503;

                &:hover {
                    background: lighten(#ec0503, 10%);
                }
            }

            &.icon-tumblr {
                background: #314d69;
                border-color: #314d69;

                &:hover {
                    background: lighten(#314d69, 10%);
                }
            }

            &.icon-stumbleupon {
                background: #e84d22;
                border-color: #e84d22;

                &:hover {
                    background: lighten(#e84d22, 10%);
                }
            }

            &.icon-rss {
                background: #ea850a;
                border-color: #ea850a;

                &:hover {
                    background: lighten(#ea850a, 10%);
                }
            }

            &.icon-pinterest {
                background: #b7181b;
                border-color: #b7181b;

                &:hover {
                    background: lighten(#b7181b, 10%);
                }
            }

            &.icon-youtube {
                background: #de2c26;
                border-color: #de2c26;

                &:hover {
                    background: lighten(#de2c26, 10%);
                }
            }

            &.icon-linkedin {
                background: #0092cc;
                border-color: #0092cc;

                &:hover {
                    background: lighten(#0092cc, 10%);
                }
            }
        }

        +li {
            margin-left: 8px;
        }
    }
}

/* ==================== TAB SECTION STYLE ==================== */

.tab-section {
    ol {
        &.tab-list {
            list-style: none;
            margin: 0 0 30px 0;
            border: none;
            background: $section-bg;
            border-bottom: 1px solid $section-border-color;
            @include display-flex(flex);
            @include align-item(center);
        }
    }

    .tab-list-item {
        margin: 0;
        padding: 0;
        border: none;
        line-height: 20px;
        padding: 20px 35px;
        font-weight: 500;
        font-size: 16px;
        cursor: pointer;
        @include transition(all 0.4s ease);

        @include tablet{
            padding: 16px 25px;
            font-size: 14px;
        }

        @include mobile{
            padding: 12px 18px;
        }

        &:hover {
            background: lighten($primary-color, 10%);
            color: $white-color;
        }

        &.tab-list-active {
            background: $primary-color;
            color: $white-color;
        }
    }
}

/* ==================== TOGGLE SECTION STYLE ==================== */

.toggle-action {
    margin-bottom: 30px;
}

.toggle-title {
    padding: 20px 40px;
    background: $grey-bg;
    @include display-flex(flex);
    cursor: pointer;
    @include justify-content(space-between);
    text-transform: uppercase;
    font-weight: $bold;
    @include align-item(center);
    margin: 0;

    small {
        font-size: 12px;
    }
}

/* ==================== PANEL SECTION STYLE ==================== */

.panel {
    .panel-header {
        padding: 30px;
        background: $section-bg;

        @include tablet {
            padding: 25px;
        }
    }

    .panel-title {
        text-transform: uppercase;
        font-weight: $bold;
        font-size: 18px;
        margin-bottom: 0;
    }

    .panel-body {
        padding: 30px;
        padding-top: 0;
        background: $section-bg;

        @include tablet {
            padding: 25px;
            padding-top: 0;
        }
    }
}

/* ==================== BREADCRUMB STYLE ==================== */

.breadcrumb-list {
    margin: 0 0 30px 0;
    list-style: none;
    @include display-flex(flex);
    font-size: 13px;
    color: $primary-color;
    font-weight: $medium;
    @include align-item(center);

    a {
        color: $primary-color;

        &:hover {
            color: $grey-font-color;
        }
    }

    li {
        +li {
            margin-left: 8px;
            padding-left: 15px;
            position: relative;

            &:before {
                content: " ";
                position: absolute;
                left: 0;
                top: 0;
                width: 10px;
                height: 10px;
                background-size: auto 10px;
                background-repeat: no-repeat;
                background-position: center center;
                top: 50%;
                margin-top: -5px;
                background-image: url("data:image/svg+xml,%3Csvg version='1.1' xmlns='http://www.w3.org/2000/svg' width='576' height='1024' viewBox='0 0 576 1024'%3E%3Ctitle%3E%3C/title%3E%3Cg id='icomoon-ignore'%3E%3C/g%3E%3Cpath d='M554.936 475.452l-460.488-460.335c-20.206-20.155-52.943-20.155-73.2 0-20.206 20.155-20.206 52.892 0 73.047l423.963 423.81-423.912 423.81c-20.206 20.155-20.206 52.892 0 73.098 20.206 20.155 52.994 20.155 73.2 0l460.488-460.335c19.9-19.947 19.9-53.198-0.051-73.093z'%3E%3C/path%3E%3C/svg%3E");
            }
        }
    }
}

/* ==================== PAGINATION STYLE ==================== */

.pagination-list {
    text-align: center;
    margin-top: 50px;

    a {
        padding: 5px;
        display: inline-block;
        vertical-align: middle;
        margin: 0 6px;
        font-weight: $semi-bold;
        line-height: 15px;

        &:hover {
            color: $primary-color;
        }


        &.page-prev,
        &.page-next {
            position: relative;

            &:after {
                background-image: url("data:image/svg+xml,%3Csvg version='1.1' xmlns='http://www.w3.org/2000/svg' width='1024' height='1024' viewBox='0 0 1024 1024'%3E%3Ctitle%3E%3C/title%3E%3Cg id='icomoon-ignore'%3E%3C/g%3E%3Cpath d='M548.549 778.94l460.335-460.488c20.155-20.206 20.155-52.943 0-73.2-20.155-20.206-52.892-20.206-73.047 0l-423.81 423.963-423.81-423.912c-20.155-20.206-52.892-20.206-73.098 0-20.155 20.206-20.155 52.994 0 73.2l460.331 460.488c19.947 19.896 53.198 19.896 73.098-0.051z'%3E%3C/path%3E%3C/svg%3E");
                width: 12px;
                height: 14px;
                @include transform(rotate(-90deg));
                display: inline-block;
                vertical-align: middle;
                content: " ";
                background-repeat: no-repeat;
                background-size: 100%;
                background-position: center center;
            }
        }

        &.page-prev {
            margin-right: 50px;
            padding-left: 15px;

            &:after {
                position: absolute;
                left: 0;
                top: 50%;
                margin-top: -7px;
                @include transform(rotate(90deg));
            }
        }

        &.page-next {
            margin-left: 50px;
            padding-right: 15px;

            &:after {
                position: absolute;
                right: 0;
                top: 50%;
                margin-top: -7px;
            }
        }

        &[aria-current="page"] {
            color: $primary-color;
        }
    }
}

.pagination-container.pagination-bar {
    margin: 40px 0 0 0;
    padding: 0;
    list-style: none;

    .pagination-item {
        padding: 0;
        height: 36px;
        width: 36px;
        text-align: center;
        min-width: 36px;
        display: inline-block;
        line-height: 36px;
        font-weight: 500;
        @include border-radius(50%);

        @include laptop {
            height: 28px;
            width: 28px;
            min-width: 28px;
            line-height: 28px;
        }

        &:first-child {
            margin-left: 0;
        }

        &:last-child {
            margin-right: 0;
        }

        &.selected {
            background-color: $primary-color;
            color: $white-color;
        }

        &.disabled {
            opacity: 0.3;
        }

        .arrow {

            &.left,
            &.right {
                width: 36px;
                height: 36px;
                transform: none;
                cursor: pointer;

                @include laptop {
                    height: 28px;
                    width: 28px;
                }

                &:before {
                    display: none;
                    transform: none;
                }

                &:after {
                    background-position: center center;
                    background-size: 6px auto;
                    opacity: 0.8;
                    background-repeat: no-repeat;
                    content: " ";
                    width: 36px;
                    height: 36px;
                    display: inline-block;
                    cursor: pointer;

                    @include laptop {
                        height: 28px;
                        width: 28px;
                    }
                }

                &:hover {
                    background: none;
                }
            }

            &.left {
                &:after {
                    background-image: url(../assets/svg/ad-arrow-left.svg);
                }
            }

            &.right {
                &:after {
                    background-image: url(../assets/svg/ad-arrow-right.svg);
                }
            }
        }
    }
}

/* ==================== ALERT MESSAGE STYLE ==================== */

.alert-message {
    padding: 15px 20px;
    @include border-radius(8px);
    background: $section-bg;
    margin-bottom: 30px;

    &.style-info {
        background: $section-bg;
    }
}

.bc-alert {
    background-color: #168fc0;
    color: #fff;
    padding: 10px 20px;
    margin-bottom: 30px;
    font-size: $body-font-size;
    @include border-radius(6px);
    position: relative;
    font-size: 14px;
    font-weight: normal;

    .bc-alert-close-button{
        position: absolute;
        top: -10px;
        right: -10px;
        background-color: darken(#168fc0, 5%);
        width: 26px;
        height: 26px;
        @include border-radius(50%);
        border: 2px solid $white-color;
        outline: none;
        cursor: pointer;
        padding: 4px;
        svg{
            max-width: 100%;
            max-height: 100%;
            fill: $white-color;
        }
    }
}

.bc-alert--success {
    background-color: $success-color;

    .bc-alert-close-button{
        background-color: darken($success-color, 5%);
    }
}

.bc-alert--error {
    background-color: $warning-color;

    .bc-alert-close-button{
        background-color: darken($warning-color, 5%);
    }
}

.bc-alert-group--info {
    background-color: #cdcdcd;
    color: #34313f;
    font-size: 0.875em;

    .bc-alert-close-button{
        background-color: darken(#cdcdcd, 5%);
    }
}

.bc-product-form .bc-alert-group--info {
    margin-bottom: 0;
}

.bc-alert-group--error {
    opacity: 0;
    height: 0;
    overflow: hidden;
    visibility: hidden;
    transition: all 0.15s ease-in-out;
}

.bc-alert-group.bc-fade-in-alert-group {
    opacity: 1;
    visibility: visible;
    height: auto;
}

/* ==================== Toast Message Icon ==================== */

.Toastify__toast-container {
    padding: 0;

    .Toastify__toast {
        padding: 0;
        min-height: auto;
    }

    .Toastify__toast-body {
        padding: 0;
    }
}

.bc-toast-message {
    padding: 15px 20px 15px 20px;
    line-height: 1.5;
    border-left: 4px solid #8162BE;

    &.bc-toast-success {
        border-color: #38C838;
    }

    &.bc-toast-warning {
        border-color: #F28D00;
    }

    &.bc-toast-info {
        border-color: #F3B33F;
    }

    &.bc-toast-error {
        border-color: #F71F14;
    }

    svg {
        width: 22px;
        height: 22px;
        display: inline-block;
        vertical-align: middle;
        fill: #000000;
    }
}

// card

.card-body{
    border: 1px solid $border-color;
    padding: 25px;
    border-radius: 8px;
    margin-bottom: 35px;

    @include tablet{
        padding: 20px;
    }
    @include mobile{
        padding: 15px;
    }
}
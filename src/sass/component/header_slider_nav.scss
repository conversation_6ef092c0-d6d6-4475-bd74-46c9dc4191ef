.header-slider-navigation {
    width: 300px;
    height: 100vh;
    background-color: #fff;
    position: fixed;
    top: 0;
    left: -350px;
    z-index: 99;
    transition: 0.5s;
    overflow: auto;
}

@media screen and (min-width: 1260px){
    .header-slider-navigation::-webkit-scrollbar {
        width: 5px;
    }
}

.header-slider-navigation.active {
    left: 0;
}

.mwsidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    transition: 0.5s;
    opacity: 0;
    visibility: hidden;
}

.mwsidebar-overlay.active {
    opacity: 1;
    visibility: visible;
    z-index: 9999;
}

.nav-section{
    margin-bottom: 30px;
}

.nav-title {
    h4 {
        font-weight: 700;
        font-size: 16px;
        line-height: 19px;
        color: #09254a;
        text-transform: capitalize;
        padding: 14px 20px 14px 20px;
        margin-bottom: 0;
    }
}

.nav-list-bar {
    ul {
        li {
            border-bottom: 1px solid #e5e5e5;
            position: relative;

            > div{
                position: relative;

                button{
                    position: absolute;
                    top: 0;
                    right: 0;
                    width: 32px;
                    height: 32px;
                    background-color: #fff;
                    border: none;
                    cursor: pointer;
                    line-height: 32px;
                    text-align: center;
                    font-size: 20px;
                    color: #000000;
                    z-index: 1;
                }
            }

            ul{
                border-top: 1px solid #e5e5e5;

                li{
                    &:last-child{
                        border-bottom: none;
                    }
                }
            }

            a {
                font-weight: 500;
                font-size: 14px;
                line-height: 18px;
                color: #000;
                padding: 7px 20px;
                display: block;
            }



            &.account-links{
                a{
                    display: inline-block;
                    vertical-align: middle;

                    &:first-child{
                        padding-right: 5px;
                    }
                    &:last-child{
                        padding-left: 5px;
                    }
                }
            }
        }
    }
}

.header-slider-overlay{
    background: linear-gradient(0deg,rgba(0,0,0,.82),rgba(0,0,0,.82));
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    z-index: 10;
    display: none;

    &.active{
        display: block;
    }
}
/* ==================== FORM ELEMENT STYLE ==================== */

.form-input {
  outline: none;
  line-height: 18px;
  padding: 10px 15px;
  height: auto;
  @include border-radius(4px);
  border: 1px solid $input-border-color;
  font-size: 14px;
  font-family: $input-font;
  color: $input-color;
  width: 100%;

  &:focus {
    border-color: darken($input-border-color, 5%);
  }
}

.form-input::-webkit-input-placeholder {
  color: $input-color;
  opacity: 0.3;
}

.form-input::-moz-placeholder {
  color: $input-color;
  opacity: 0.3;
}

.form-input:-ms-input-placeholder {
  color: $input-color;
  opacity: 0.3;
}

.form-input:-moz-placeholder {
  color: $input-color;
  opacity: 0.3;
}

.form-field-group,
.form-row .form-field-group {
  border: 1px solid $border-color;
  @include border-radius(4px);
  padding: 20px;
}

.form-select {
  height: 40px;
  line-height: 40px;
  padding: 0 30px 0 15px;
  @include border-radius(4px);
  border: 1px solid $input-border-color;
  background-size: 18px;
  background-position: center right 10px;
  background-repeat: no-repeat;
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath d='M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z' fill='rgba(117, 117, 117, 0.999)' /%3E%3C/svg%3E");
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  width: 100%;
  font-family: $body-font;
  background-color: $white-color;

  &.form-select--small {
    height: 36px;
    line-height: 36px;
  }
}

.button {
  height: auto;
  line-height: 18px;
  padding: 11px 25px;
  margin: 0;
  font-family: $input-font;
  font-size: 14px;
  letter-spacing: 0.7px;
  @include border-radius(4px);
  outline: none;
  background: $button-bg;
  display: inline-block;
  text-align: center;
  text-transform: uppercase;
  @include transition(all 0.4s ease);
  border: none;
  color: $button-text-color;
  cursor: pointer;

  &[disabled] {
    opacity: 0.5;
    cursor: no-drop;
  }


  &:hover,
  &:focus {
    background: $button-bg-hover;
    border: none;
    color: $button-text-color;
  }

  &.button--secondary {
    background: $button-bg-hover;
    color: $button-text-color;

    &:hover,
    &:focus {
      background: $button-bg;
      border: none;
    }

  }

  &.button-small {
    padding: 8px 15px;
    font-size: 12px;
  }

  &.button-large {
    padding: 20px 35px;
    font-size: 16px;

    @include tablet {
      padding: 18px 30px;
      font-size: 14px;
    }
  }

  &.button-border {
    background: none;
    border: 2px solid $button-bg;
    color: $button-bg;
    font-weight: 600;

    &:hover {
      background: $button-bg;
      color: $white-color;
    }
  }
}

.form-label {
  font-size: 14px;
  margin-bottom: 10px;
  color: $grey-font-color;
  display: block;

  small {
    float: right;
    font-size: 12px;
  }
}

.form-inlineMessage {
  font-size: 12px;
  margin-top: 5px;
}

.form-field {
  margin-bottom: 35px;

  @include mobile {
    margin-bottom: 25px;
  }
}

.form-actions {
  margin-top: 30px;

  .button{
    + .button{
      margin-left: 12px;
    }
  }
}


.form-checkbox {
  border: 0;
  clip: rect(1px, 1px, 1px, 1px);
  height: 1px;
  overflow: hidden;
  padding: 0;
  position: absolute;
  width: 1px;

  +.form-label {
    display: inline-block;
    font-weight: 400;
    padding-left: 20px;
    position: relative;
    cursor: pointer;
    vertical-align: baseline;
    margin-bottom: 0;

    &:before {
      position: absolute;
      left: 0;
      top: 2px;
      width: 14px;
      height: 14px;
      border: 1px solid #8f8f8f;
      content: " ";
    }

    &:after {
      background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath d='M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z' fill='rgba(51, 51, 51, 0.999)' /%3E%3C/svg%3E");
      background-position: 50%;
      background-repeat: no-repeat;
      background-size: 100%;
      content: "";
      line-height: 1;
      text-align: center;
      left: 0;
      top: 2px;
      width: 14px;
      height: 14px;
      position: absolute;
      opacity: 0;
    }
  }

  &:checked {
    +.form-label {
      &:after {
        opacity: 1;
      }
    }
  }
}

// facet style

.form-label-facet {
  position: relative;
  padding-left: 20px;
  cursor: pointer;
  padding-right: 35px;

  .facet-count {
    position: absolute;
    right: 0;
    top: 2px;
    font-size: 10px;
  }

  .checkbox-icon {
    position: absolute;
    left: 0;
    top: 2px;
    width: 14px;
    height: 14px;
    border: 1px solid #8f8f8f;

    &:after {
      background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath d='M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z' fill='rgba(51, 51, 51, 0.999)' /%3E%3C/svg%3E");
      background-position: 50%;
      background-repeat: no-repeat;
      background-size: 100%;
      content: "";
      line-height: 1;
      text-align: center;
      left: 0;
      top: 0;
      width: 14px;
      height: 14px;
      position: absolute;
      opacity: 0;
    }
  }

  .form-checkbox-facet {
    border: 0;
    clip: rect(1px, 1px, 1px, 1px);
    height: 1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;

    &:checked {
      +.checkbox-icon {
        &:after {
          opacity: 1;
        }
      }
    }
  }
}

// radio style

.form-radio {
  border: 0;
  clip: rect(1px, 1px, 1px, 1px);
  height: 1px;
  overflow: hidden;
  padding: 0;
  position: absolute;
  width: 1px;

  +.form-label {
    display: inline-block;
    font-weight: 400;
    padding-left: 20px;
    position: relative;
    cursor: pointer;
    vertical-align: baseline;
    margin-bottom: 0;

    &:before {
      position: absolute;
      left: 0;
      top: 2px;
      width: 14px;
      height: 14px;
      border: 1px solid #8f8f8f;
      content: " ";
      @include border-radius(50%);
    }

    &:after {
      background: $black-color;
      @include border-radius(50%);
      content: "";
      line-height: 1;
      text-align: center;
      left: 4px;
      top: 6px;
      width: 6px;
      height: 6px;
      position: absolute;
      opacity: 0;
    }
  }

  &:checked {
    +.form-label {
      &:after {
        opacity: 0.8;
      }
    }
  }
}

.form-increment {
  @include display-flex(flex);
  @include align-item(center);
  border: 1px solid #dbdbdb;
  max-width: 124px;

  .form-input {
    width: 40px;
    padding: 11px 0;
    text-align: center;
    @include border-radius(0);
  }

  .button {
    width: 42px;
    height: 42px;
    padding: 0;
    @include justify-content(center);
    background: none;
    color: #333333;
    border: none;
    @include display-flex(flex);
    @include align-item(center);
    @include border-radius(0);
    padding: 0;

    svg {
      width: 14px;
      height: auto;
      opacity: 0.6;
    }

    &:hover {
      svg {
        opacity: 0.8;
      }
    }
  }

  .form-input {
    border: none;
  }

  .spinner {
    width: 70px;
    opacity: 0.4;
  }

  &.form-increment-style-2{
    border: none;
    .button{
      padding: 0;
      width: 32px;
      height: 26px;

      svg{
        width: 12px;
      }
    }
    .form-input{
      border: 1px solid #dbdbdb;
    }
  }
}

.form-error-message {
  margin: 5px 0 20px 0;
  display: block;
  color: #cc4749;
  font-size: 12px;
}

.form-oneline {
  @include display-flex(flex);
  @include flex-wrap(wrap);

  .form-input {
    flex: 1;
    margin-right: 20px;

    @include mobile {
      flex: auto;
      margin-bottom: 20px;
      margin-right: 0;
    }
  }
}

.form-mid {
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.form-row {
  &.form-row--half {
    @include row;
    @include display-flex(flex);
    @include flex-wrap(wrap);

    .form-field {
      @include col;
      width: 50%;

      @include mobile {
        width: 100%;
      }
    }
  }
}
.has-bg {
    position: relative;
    overflow: hidden;
    background: #ededed;

    &:after {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        transform: translateX(-100%);
        background-image: linear-gradient(90deg,
                rgba(#fff, 0) 0,
                rgba(#fff, 0.2) 20%,
                rgba(#fff, 0.5) 60%,
                rgba(#fff, 0));
        animation: shimmer 0.6s infinite;
        content: '';
    }

    @keyframes shimmer {
        100% {
            transform: translateX(100%);
        }
    }
}

.skeleton-product-listing{
    width: 100%;
    display: flex;
    flex-wrap: wrap;

    .skeleton-item{
        width: 20%;
        @include col; 
        margin-bottom: 24px;

        @include desktop{
            width: 25%;
        }
        @include tablet{
            width: 33.33%;
        }
        @include tablet-small{
            width: 50%;
        }
        @include mobile{
            width: 33.33%;
        }
        @include mobile-small{
            width: 50%;
        }
    }
    .skeleton-inner{
        border: 1px solid #e4e4e4;
        @include border-radius(5px);
        padding: 20px;
    }
}

.skeleton-large{
    height: 250px;
    width: 100%;
}
.skeleton-line{
    height: 20px;
    margin-top: 15px;
}
.skeleton-small{
    height: 20px;
    width: 50%;
    margin-top: 10px;
}
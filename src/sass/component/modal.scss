.App {
    text-align: center;
    padding-top: 2rem;
}

.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1040;
    width: 100vw;
    height: 100vh;
    background-color: #000;
    opacity: .5;
}

.modal-wrapper {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1050;
    width: 100%;
    height: 100%;
    overflow-x: scroll;
    overflow-y: scroll;
    outline: 0;
}

.modal {
    z-index: 100;
    background: white;
    position: relative;
    margin: 1.75rem auto;
    border-radius: 0px;
    max-width: 80vw;
    height: 80vh;
    padding: 2rem;

    @include mobile{
        max-width: 90vw;
        max-height: 90vh;
    }
}

.modal-close-button {
    background: transparent;
    font-size: 1.4rem;
    font-weight: 700;
    line-height: 1;
    color: #000;
    opacity: .3;
    cursor: pointer;
    border: none;
}

/* basic modal css */

.basic-modal {
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
}

.basic-modal-content {
    width: 500px;
    background-color: #fff;
}

.basic-modal-header,
.basic-modal-footer {
    padding: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-title {
    margin: 0;
    text-align: center;
    font-weight: $bold;
    text-transform: uppercase;
    font-size: 20px;
}

.basic-modal-body {
    padding: 20px;
    border-top: 1px solid #eee;
    border-bottom: 1px solid #eee;
    font-size: 1.3rem;
}

.basic-modal-button {
    padding: 10px 15px;
    width: 20%;
    background-color: green;
    color: #fff;
}

.basic-modal-icon {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 10px 0;
}

/* review modal */
.review-modal {
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 99;

}

.review-modal-content {
    width: 50%;
    background-color: #fff;

}

.review-modal-header {
    padding: 10px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.review-modal-footer {
    height: 50px;
}

.review-modal-title {

    font-size: 1.2rem;
    flex: 1;
    text-align: center;
}

.review-modal-body {
    padding: 20px;
    border-top: 1px solid #eee;
    border-bottom: 1px solid #eee;
    font-size: 1.3rem;

}

.review-modal-button {
    padding: 10px 15px;
    width: 30%;
    background-color: green;
    color: #fff;
}

.review-modal-icon {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 10px 0;
}

.modal-body-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-product-details {

    flex: 1;
}

.modal-review-form {
    flex: 1;

}

.review-form {
    margin-left: 10px;
}

.review-product-image {
    margin-left: 70px;
}


.review-product-image>img {
    height: 300px;
    width: 300px;
}

.review-product-wrap {
    display: flex;
    justify-content: center;
    flex-direction: column;
    height: 400px;
}

.review-product-name {
    font-size: 0.8rem;
    width: 400px;
    margin-top: 20px;
    text-align: center;
}


body {
    .modal-wrapper {
        overflow: hidden;
    }

    .modal {
        margin: 5vw auto 0 auto;
        padding: 30px;
        overflow: auto;
    }
}

.modal {
    .bc-product-single {

        .product-tab-section,
        .product-video-section {
            display: none;
        }
    }
}

.modal-header {
    position: relative;
    padding-bottom: 40px;

    .modal-close-button {
        width: 30px;
        height: 30px;
        position: absolute;
        top: 0;
        right: 0;
        @include display-flex(flex);
        @include align-item(center);
        @include justify-content(center);
    }
}


.save-cart-form{
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
    margin-top: 50px;

    textarea{
        height: 100px;
    }
}
.notice-banner {
  border: 5px solid #000;
  background: #fff;
  padding: 9px 20px;

  @include mobile {
    padding: 4px 20px;
    border-width: 2px;
  }

  p {
    margin: 0;
    font-weight: 700;
    font-size: 30px;
    line-height: 36px;
    text-transform: uppercase;
    color: #0f0d39;
    text-align: center;

    @include laptop {
      font-size: 24px;
      line-height: 30px;
    }

    @include tablet {
      font-size: 20px;
      line-height: 24px;
    }

    @include tablet-small {
      font-size: 14px;
      line-height: 20px;
    }

    @include mobile {
      font-size: 10px;
      line-height: 12px;
    }
  }
}

.header-full-width-banner span {
  display: block !important;
  width: 100%;
}

.header-full-width-banner{
  video{
    max-height: 75px;
  }
}

.header-section {
  position: relative;
  z-index: 10;
  border-bottom: 1px solid $section-border-color;

  ul {
    margin: 0;
    list-style: none;
  }

  p {
    margin-bottom: 0;
  }
}

.header-top-section {
  padding: 8px 0;
  background: $section-bg;
  position: relative;
  z-index: 5;
}

.header-contact-block,
.header-currency-block {
  width: 300px;
}

.pencil-banner-block {
  width: calc(100% - 600px);
}

.header-contact-block {
  i {
    margin-right: 8px;
  }

  svg {
    width: 18px;
    height: 18px;
    display: inline-block;
    vertical-align: middle;
  }
}

.header-middle-section {
  padding: 25px 0;
  z-index: 5;
  position: relative;

  @include tablet {
    padding: 18px 0;
  }

  @include tablet-small {
    padding: 15px 0;
  }

  @include mobile {
    padding: 10px 0;
  }
}

.hemburger-link {
  .icon {
    width: 27px;
    height: 20px;
    cursor: pointer;

    @include tablet {
      width: 20px;
    }
  }
}

.header-logo {
  margin-left: 20px;

  @include mobile {
    margin-left: 15px;
  }

  img {
    max-height: 60px;

    @include tablet {
      max-height: 44px;
    }

    @include tablet-small {
      max-height: 40px;
    }
    @include mobile {
      max-height: 36px;
    }
  }
}

.header-logo-block {
  width: 340px;

  @include tablet {
    width: 270px;
  }
}

.header-plain-section {
  .header-logo-block {
    width: 100%;
  }

  .header-logo {
    margin-left: 0;
  }
}

.currency-dropdown {
  > li {
    > a {
      line-height: 24px;
      display: inline-block;
    }
  }
}

.currency-dropdown {
  .dropdown-list {
    left: auto;
    right: 0;
    @include transform(none);
  }
}

.header-right-nav {
  > li {
    padding: 10px 0;

    + li {
      margin-left: 12px;

      @include laptop {
        margin-left: 10px;
      }
    }

    > a,
    > button {
      padding: 0 5px;
      display: inline-block;
      vertical-align: middle;
    }

    svg {
      width: 22px;
      height: 22px;
      display: inline-block;
      vertical-align: middle;

      @include tablet-small {
        width: 20px;
        height: 20px;
      }
    }

    &.header-chat-link {
      button {
        border: none;
        background: none;
        cursor: pointer;
      }
    }

    &.header-cart-icon-black,
    &.header-search-link {
      svg {
        path {
          stroke: #09254a;
        }
      }
    }
  }
}

.header-search-link {
  .icon-close {
    display: none;
  }
}

.header-toggle-active {
  .header-search-link {
    .icon-close {
      display: block;
    }
    .icon-search {
      display: none;
    }
  }
}

.header-cart-link {
  position: relative;
}

.header-cart-menu {
  a {
    position: relative;
    @include display-flex(flex);
    @include align-item(center);
    color: $white-color;
    font-weight: 400;
    font-size: 16px;
    line-height: 19px;

    @include laptop {
      font-size: 14px;
    }

    @include tablet {
      font-size: 14px;
    }
  }

  strong {
    font-weight: 400;
    margin-right: 3px;
  }

  svg {
    width: 24px;
    height: 20px;
    stroke: $white-color;
    margin-right: 10px;

    @include laptop {
      width: 20px;
      height: 20px;
      margin-right: 8px;
    }

    @include tablet {
      width: 20px;
      height: 20px;
      margin-right: 8px;
    }
  }
}

.cart-count {
  background: $primary-color;
  width: 22px;
  height: 22px;
  font-size: 10px;
  @include border-radius(50%);
  position: absolute;
  color: $white-color;
  display: inline-block;
  @include align-item(center);
  @include justify-content(center);
  @include display-flex(flex);
  top: 0;
  right: -10px;
}

.nav-primary-list {
  > li {
    > a {
      font-size: 16px;
      padding-top: 15px;
      padding-bottom: 15px;
      display: block;
      font-weight: 500;
    }

    > ul {
      .has-arrow {
        > a {
          padding-right: 20px;

          &:after {
            @include transform(rotate(-90deg));
            position: absolute;
            right: 5px;
            top: 50%;
            margin-top: -4px;
          }
        }
      }

      ul {
        top: 0;
        left: 100%;
        @include transform(none);
      }
    }
  }

  ul {
    li {
      &:hover {
        > a {
          color: $primary-color;
        }
      }
    }
  }
}

// style MW

.header-section {
  margin-bottom: 30px;

  @include tablet {
    margin-bottom: 20px;
  }

  @include mobile {
    margin-bottom: 15px;
  }
}

.header-pencil-banner {
  padding: 10px 0;
  font-size: 14px;

  @include mobile {
    display: none;
  }

  .header-logout-link {
    text-decoration: underline;
    margin-left: 10px;
    padding-left: 10px;
    border-left: 1px solid $border-color;
  }
}

.header-primary-nav-section {
  background: $primary-color;

  @include tablet {
    display: none;
  }
}

.header-call-link {
  @include tablet {
    font-size: 14px;
  }

  @include tablet-small {
    display: none;
  }

  .icon {
    width: 24px;
    height: 24px;
    margin-right: 10px;

    @include laptop {
      width: 22px;
      height: 22px;
      margin-right: 5px;
    }
  }
}

.promo-text {
  max-width: 160px;

  @include tablet {
    display: none;
  }

  p {
    line-height: 22px;
  }
}

.header-search-block {
  @include tablet-small {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    padding: 15px;
    margin: 0;
    background-color: rgba(255, 255, 255, 0.8);
  }
  @include mobile {
    padding: 10px;
  }
}

.header-toggle-active {
  .header-search-block {
    @include tablet-small {
      display: block;
    }
  }
}

.search-form {
  width: 600px;

  @include desktop {
    width: 460px;
  }

  @include laptop {
    width: 350px;
  }

  @include tablet-small {
    // width: 100%;
    // position: absolute;
    // left: 0;
    // top: 100%;
    // margin-top: 15px;
    width: 100%;
    max-width: 100%;
    padding: 0;
  }

  .form-input {
    width: 100%;
    border: 2px solid #9ab8e7;
    @include border-radius(5px);
    font-size: 16px;
    font-weight: 500;
    line-height: 20px;
    padding: 14px 50px 14px 15px;

    @include tablet {
      font-size: 15px;
      padding: 12px 50px 12px 15px;
    }

    @include tablet-small {
      font-size: 14px;
      padding: 10px 40px 10px 15px;
    }
  }

  .form-input::-webkit-input-placeholder {
    opacity: 0.7;
  }

  .form-input::-moz-placeholder {
    opacity: 0.7;
  }

  .form-input:-ms-input-placeholder {
    opacity: 0.7;
  }

  .form-input:-moz-placeholder {
    opacity: 0.7;
  }

  form {
    position: relative;
  }

  .search-button,
  .search-action {
    background: none;
    width: 24px;
    height: 24px;
    border: none;
    position: absolute;
    top: 50%;
    margin-top: -12px;
    right: 20px;
    cursor: pointer;

    @include tablet-small {
      right: 15px;
      width: 22px;
      height: 22px;
    }

    svg {
      max-width: 100%;
      max-height: 100%;
    }

    .loading-spinner {
      width: 24px;
      height: 24px;
    }
  }

  .search-action-close {
    svg {
      fill: #9ab8e7;
    }
  }
}

.search-message {
  font-weight: $medium;
  padding: 7px 15px;
  @include border-radius(3px);
  background: $grey-bg;
}

.search-preview-block {
  position: absolute;
  left: 0;
  width: 100%;
  top: 110%;
  background: $white-color;
  @include border-radius(5px);
  padding: 10px;
  border: 2px solid #9ab8e7;
  max-height: 400px;
  overflow: auto;

  &:empty {
    display: none;
  }

  ul {
    margin-top: 10px;
    margin-bottom: 10px;
  }
}

.search-item {
  > a {
    display: block;
    width: 100%;
  }
}

.search-item-inner {
  padding: 8px 0;
}

.search-figure {
  width: 70px;
  min-width: 70px;
  height: 70px;
  padding: 1px;
  border: 1px solid $border-color;
  @include display-flex(flex);
  @include align-item(center);
  @include justify-content(center);

  img {
    max-width: 100%;
    max-height: 100%;
  }
}

.search-p-name {
  width: calc(100% - 70px);
  padding-left: 20px;
  line-height: 1.4;
  font-size: 13px;
  font-weight: $medium;
}

.search-tag-list {
  li {
    display: inline-block;
    vertical-align: middle;
    margin-right: 15px;
    cursor: pointer;

    &:hover {
      color: $primary-color;
      text-decoration: underline;
    }
  }
}

.active-loyalty-tab {
  color: #00629f !important;
  text-decoration: underline;
}

.header-primary-nav-section {
  .nav-primary-list {
    > li {
      + li {
        margin-left: 32px;

        @include desktop {
          margin-left: 20px;
        }

        @include desktop-small {
          margin-left: 16px;
        }

        @include laptop {
          margin-left: 11px;
        }
      }

      > a {
        font-size: 16px;
        display: block;
        color: $white-color;
        font-weight: normal;
        padding-left: 30px;
        position: relative;

        @include desktop-small {
          padding-left: 25px;
          font-size: 15px;
        }

        @include laptop {
          font-size: 15px;
          padding-left: 18px;
        }

        &:before {
          position: absolute;
          top: 50%;
          left: 0;
          margin-top: -10px;
          content: " ";
          width: 20px;
          height: 20px;
          background-size: auto 20px;
          background-position: center left;
          background-repeat: no-repeat;

          @include desktop-small {
            width: 16px;
            height: 16px;
            background-size: auto 16px;
            margin-top: -8px;
          }

          @include laptop {
            width: 16px;
            height: 16px;
            background-size: auto 15px;
            margin-top: -8px;
          }
        }

        &:after {
          display: none;
        }

        &.new-in-stock {
          &:before {
            background-image: url("../assets/svg/navigation/icon-inStock.svg");
          }
        }

        &.nic-pouches{
          &:before {
            background-image: url("../assets/svg/navigation/icon-nic-pouches.svg");
          }
        }

        &.new-dropship,
        &.new-drop-ship {
          &:before {
            background-image: url("../assets/svg/navigation/icon-dropship.svg");
          }
        }

        &.clearance-disposables {
          &:before {
            background-image: url("../assets/svg/navigation/icon-clearance.svg");
          }
        }

        &.devices {
          &:before {
            background-image: url("../assets/svg/navigation/icon-devices.svg");
          }
        }

        &.promos {
          &:before {
            background-image: url("../assets/svg/navigation/icon-promos.svg");
          }
        }

        &.e-liquids {
          &:before {
            background-image: url("../assets/svg/navigation/icon-liquids.svg");
          }
        }

        &.pod-systems {
          &:before {
            background-image: url("../assets/svg/navigation/icon-pod-systems.svg");
          }
        }

        &.glass {
          &:before {
            background-image: url("../assets/svg/navigation/icon-glass.png");
          }
        }

        &.disposables {
          &:before {
            background-image: url("../assets/svg/navigation/icon-disposables.svg");
          }
        }

        &.cbd {
          &:before {
            background-image: url("../assets/svg/navigation/icon-cbd.png");
          }
        }

        &.brands {
          &:before {
            background-image: url("../assets/svg/navigation/icon-brands.svg");
          }
        }

        &.contact-us {
          &:before {
            background-image: url("../assets/svg/navigation/icon-contact.svg");
          }
        }
      }

      &:hover {
        > a {
          color: $white-color;
        }
      }
    }
  }

  .dropdown-list {
    min-width: 250px;
    padding: 10px 15px;
    border: 1px solid #e0e0e0;
    border-top: none;
    left: 0;
    transform: none;
    @include border-radius(0 0 3px 3px);

    a {
      padding: 7px 0;
      line-height: 1.2;
      color: #333333;
      font-size: 14px;
      font-weight: 500;
      text-transform: capitalize;
    }
  }

  .nav-primary-list {
    > li {
      > a.brands {
        + .dropdown-list {
          width: 1000px;
          left: auto;
          right: 0;
          column-count: 5;

          @include desktop-small {
            width: 900px;
            column-count: 4;
          }

          li {
            padding-right: 15px;
          }
        }
      }
    }
  }
}

.custom-expiry-message{
  margin: 15px 0 20px 0;
  p{
    margin-bottom: 0;
  }
  .custom-expiry-message-inner{
    background: #f8285a;
    color: $white-color;
    border: 1px solid #f8285a;
    border-radius: 4px;
    padding: 10px 20px;

    a{
      color: $white-color;
      text-decoration: underline;
    }
    strong{
      font-weight: normal;
      text-decoration: underline;
    }
  }
  .custom-expiry-message-item{
    font-size: 13px;
    text-align: center;
    line-height: 1.5;

    @include mobile{
      font-size: 12px;
    }

    + .custom-expiry-message-item{
      margin-top: 8px;
    }
  }
}
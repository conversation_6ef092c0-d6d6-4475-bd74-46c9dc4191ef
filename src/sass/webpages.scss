// common webpage style

.web-page-inner-wrapper:after,
.web-page-inner-wrapper:before {
    display: table;
    content: " ";
}

.web-page-inner-wrapper:after {
    clear: both;
}

.web-page-style {

    overflow: hidden;

    *[style*="font-family: times\ new\ roman, times;"] {
        font-family: $body-font !important;
    }

    *[style*="font-family: 'times new roman', times;"] {
        font-family: $body-font !important;
        line-height: 1.5;
        font-size: $body-font-size !important;

        @media screen and (max-width: 767px) {
            font-size: 14px !important;
        }
    }

    // font-family
    p[style*="font-family: helvetica;"],
    span[style*="font-family: helvetica;"] {
        font-family: $body-font !important;
    }

    // font-large
    p[style*="font-size: large;"],
    span[style*="font-size: large;"] {
        font-family: 16PX !important;

        @media screen and (max-width: 767px) {
            font-size: 14px !important;
        }

        * {
            font-size: 16px !important;

            @media screen and (max-width: 767px) {
                font-size: 14px !important;
            }
        }
    }

    // font-XXlarge
    p[style*="font-size: xx-large;"],
    span[style*="font-size: xx-large;"] {
        font-size: 20px !important;
        font-weight: $semi-bold;

        @media screen and (max-width: 767px) {
            font-size: 14px !important;
        }

        * {
            font-size: 20px !important;
            font-weight: $semi-bold;

            @media screen and (max-width: 767px) {
                font-size: 14px !important;
            }
        }
    }

    // font-x-large
    p[style*="font-size: x-large;"],
    span[style*="font-size: x-large;"] {
        font-size: 18px !important;
        font-weight: 600;

        @media screen and (max-width: 767px) {
            font-size: 14px !important;
        }

        * {
            font-size: 18px !important;
            font-weight: $semi-bold;

            @media screen and (max-width: 767px) {
                font-size: 14px !important;
            }
        }
    }

    p[style*="font-size: medium;"],
    span[style*="font-size: medium;"] {
        @media screen and (max-width: 767px) {
            font-size: 14px !important;
        }
    }
}

.web-page-style {
    img {
        height: auto !important;
    }

    hr {
        border: 1px solid $border-color;
        width: 100%;
        margin: 20px 0;
    }

    ul,
    ol {
        li {
            +li {
                margin-top: 10px;
            }
        }
    }

    iframe {
        margin-bottom: 20px;
    }

    h1,
    .h1 {
        font-size: 30px;

        @include tablet {
            font-size: 28px;
        }

        @include mobile {
            font-size: 23px;
        }
    }

    h2,
    .h2 {
        font-size: 26px;

        @include tablet {
            font-size: 24px;
        }

        @include mobile {
            font-size: 22px;
        }
    }

    h3,
    .h3 {
        font-size: 24px;

        @include tablet {
            font-size: 22px;
        }

        @include mobile {
            font-size: 21px;
        }
    }

    h4,
    .h4 {
        font-size: 22px;

        @include tablet {
            font-size: 20px;
        }

        @include mobile {
            font-size: 20px;
        }
    }

    h5,
    .h5 {
        font-size: 20px;

        @include tablet {
            font-size: 18px;
        }

        @include mobile {
            font-size: 18px;
        }
    }

    h6,
    .h6 {
        font-size: 18px;

        @include tablet {
            font-size: 16px;
        }

        @include mobile {
            font-size: 16px;
        }
    }

    .newsletterSection {
        .form-fieldset {
            border: 1px solid $border-color;
            padding: 20px;
        }

        .form-field {
            margin: 0;
        }

        .form-input {
            margin-bottom: 10px;
        }
    }
}


// about us page

.group {
    @include display-flex(flex);
    @include flex-wrap(wrap);
    @include row;

    .left {
        width: 50%;
        @include col;

        @include tablet-small {
            width: 100%;
            margin-bottom: 30px;
        }
    }

    .right {
        width: 50%;
        @include col;

        @include tablet-small {
            width: 100%;
        }
    }

    iframe {
        width: 100%;
        height: 300px;
    }

}

.col-group {
    @include display-flex(flex);
    @include flex-wrap(wrap);
    @include row;

    >div {
        @include col;
        width: 33.33%;

        @include mobile {
            width: 100%;
            margin-top: 20px;
        }
    }

    .sidebarBlock-heading {
        color: $white-color;
        background: $primary-color;
        padding: 10px 12px;
        line-height: 1;
        @include border-radius(4px);
        font-size: 16px;
        text-transform: uppercase;
        margin-bottom: 10px;
    }
}

.video-ratio {
    iframe {
        aspect-ratio: 1/0.55;
        height: auto;
    }
}

// team page

@import "webpages/team-member";

// contact us form

#zohoSupportWebToCase {
    margin-top: 30px;

    #zsWebToCase_493246000000626005 {
        overflow: auto;
    }

    table {
        width: 100%;

        td {
            padding: 15px 20px !important;
            border: 1px solid $border-color;
            border-top: none;
            border-left: none;

            &:first-child {
                border-left: 1px solid $border-color;
            }
        }

        tr {
            &:first-child {
                td {
                    border-top: 1px solid $border-color;
                }
            }
        }

        input[type="text"] {
            @extend .form-input;
            margin: 5px 0;
        }

        textarea {
            @extend .form-input;
            height: 200px;
            margin: 5px 0;
        }

        input[type="submit"],
        input[type="button"] {
            @extend .button;
        }
    }
}

// shipping information

table {

    &.dcf-table {
        font-size: 14px;
        border-spacing: 0;
        margin-bottom: 30px;

        td,
        th {
            padding: 15px;
            margin: 0;
            text-align: left;
        }

        th {
            border-left: 1px solid $border-color;
            border-top: 1px solid $border-color;

            &:last-child {
                border-right: 1px solid $border-color;
            }
        }

        tr {
            td {
                border-top: 1px solid $border-color;
                border-left: 1px solid $border-color;

                &:last-child {
                    border-right: 1px solid $border-color;
                }
            }

            &:last-child {
                td {
                    border-bottom: 1px solid $border-color;
                }
            }

            &:nth-child(even) {
                background: $grey-bg;
            }

        }

        &.dcf-table-responsive {
            @include tablet-small {
                thead {
                    display: none;
                }
            }

            tr {
                @include tablet-small {
                    display: block;
                }
            }

            td {
                @include tablet-small {
                    column-gap: 3.16vw;
                    display: -ms-grid;
                    display: grid;
                    -ms-grid-columns: 1fr 2fr;
                    grid-template-columns: 1fr 2fr;
                    text-align: left !important;
                }


                &:before {
                    @include tablet-small {
                        content: attr(data-label);
                        float: left;
                        font-weight: 700;
                        // padding-right: 1.78em;
                    }
                }
            }
        }
    }
}

// event page

.left-events {
    float: left;
    width: 400px;
    padding-right: 30px;

    @include tablet {
        width: 350px;
    }

    @include mobile {
        width: 100%;
        padding-right: 0;
    }

    p {
        margin-bottom: 20px;
    }
}

.right-events {
    float: left;
    width: calc(100% - 400px);

    @include tablet {
        width: calc(100% - 350px);
    }

    @include mobile {
        width: 100%;
        margin-top: 30px;
    }

    h1 {
        font-size: 20px;
        margin-bottom: 10px;
        margin-top: 30px;

        @include tablet {
            font-size: 18px;
        }
    }
}

// WHOLESALE VAPING & E-LIQUID / E-CIG SUPPLY

.flavor-panel {
    ul {
        list-style: none;
        @include align-item(flex-start);
    }

    .col {
        margin: 0 0 30px 0;
        width: 25%;

        @include tablet {
            width: 33.33%;
        }

        @include tablet-small {
            width: 50%;
        }

        @include mobile-small {
            width: 100%;
        }

        h4 {
            font-size: 18px;
            text-align: center;
            margin-bottom: 0;
            margin-top: 10px;
            font-weight: $semi-bold;

            @include tablet-small {
                font-size: 16px;
            }
        }
    }

    &.flavor-panel-small {
        .col {
            width: 16.66%;

            @include tablet {
                width: 20%;
            }

            @include tablet-small {
                width: 25%;
            }

            @include mobile-small {
                width: 50%;
            }
        }
    }
}

// excise information page

.region-group-box {
    padding: 0 !important;

    .region-group-header {
        margin: 0 0 15px 0 !important;
    }

    .items {
        &.row {
            padding: 10px 0 !important;
            border-top: 1px solid $border-color;
            margin: 0 !important;

            >div {
                margin: 0 !important;
                padding: 0 !important;
                padding-right: 15px !important;
            }
        }
    }
}
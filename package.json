{"name": "midwestgoods", "description": "", "version": "0.0.1", "author": "", "dependencies": {"@bigcommerce/checkout-sdk": "^1.364.2", "@fortawesome/fontawesome-free": "^6.4.2", "@fortawesome/fontawesome-svg-core": "^6.4.2", "@fortawesome/free-brands-svg-icons": "^6.4.2", "@fortawesome/free-regular-svg-icons": "^6.4.2", "@fortawesome/free-solid-svg-icons": "^6.4.2", "@fortawesome/react-fontawesome": "^0.2.0", "@hookform/resolvers": "^3.3.1", "@netlify/functions": "^1.4.0", "@react-three/drei": "^7.27.5", "@react-three/fiber": "^7.0.29", "@reduxjs/toolkit": "^1.8.6", "bulma": "^0.9.0", "clsx": "^2.1.1", "compression": "^1.7.4", "cookieconsent": "3", "cross-env": "^7.0.3", "file-saver": "^2.0.5", "formik": "^2.2.9", "gatsby": "^4.24.3", "gatsby-plugin-html-attributes": "^1.0.5", "gatsby-plugin-image": "^2.0.0", "gatsby-plugin-layout": "^4.13.1", "gatsby-plugin-lucky-orange": "^1.0.3", "gatsby-plugin-manifest": "^5.7.0", "gatsby-plugin-netlify": "^5.1.1", "gatsby-plugin-netlify-cms": "^6.0.0", "gatsby-plugin-robots-txt": "^1.8.0", "gatsby-plugin-sass": "^5.0.0", "gatsby-plugin-sharp": "^4.0.0", "gatsby-plugin-sitemap": "^6.11.0", "gatsby-remark-images": "^6.0.0", "gatsby-remark-relative-images": "^0.3.0", "gatsby-source-filesystem": "^4.0.0", "gatsby-transformer-remark": "^5.0.0", "gatsby-transformer-sharp": "^4.0.0", "html-react-parser": "^5.1.7", "micro": "^9.3.4", "node-fetch": "^2.6.1", "papaparse": "^5.3.2", "prop-types": "^15.6.0", "query-string": "^8.1.0", "react": "^17.0.0", "react-dom": "^17.0.0", "react-google-recaptcha": "^3.1.0", "react-helmet": "^6.1.0", "react-hook-form": "^7.45.0", "react-inner-image-zoom": "^3.0.2", "react-lazy-load-image-component": "^1.6.0", "react-modal": "^3.16.1", "react-multi-select-component": "^4.3.4", "react-redux": "^8.0.4", "react-share": "^5.0.2", "react-simple-share": "^0.1.2", "react-slick": "^0.29.0", "react-toastify": "^9.0.7", "redux-persist": "^6.0.0", "sass": "^1.43.2", "slick-carousel": "^1.8.1", "swr": "^1.3.0", "three": "^0.135.0", "universal-cookie": "^4.0.4", "xlsx": "^0.18.5", "xlsx-populate": "^1.21.0", "yup": "^1.2.0"}, "keywords": ["gatsby", "ecommerce", "bigcommerce"], "license": "MIT", "main": "n/a", "scripts": {"clean": "gatsby clean", "start:app": "npm run develop", "start": "run-p start:**", "build:app": "npm run clean && gatsby build", "build": "gatsby clean && gatsby build --log-pages", "develop": "npm run clean && gatsby develop", "serve": "gatsby serve", "format": "prettier --trailing-comma es5 --no-semi --single-quote --write \"{gatsby-*.js,src/**/*.js}\"", "test": "echo \"Error: no test specified\" && exit 1"}, "devDependencies": {"concurrently": "^7.3.0", "dotenv": "^16.0.1", "gatsby-plugin-favicons": "^2.0.0", "gatsby-plugin-webpack-bundle-analyser-v2": "^1.1.31", "http-proxy-middleware": "^2.0.6", "npm-run-all": "^4.1.5", "prettier": "^2.7.1"}, "resolutions": {"webpack": "^5.66.0", "terser-webpack-plugin": "^5.3.3", "webpack-assets-manifest": "^5.1.0"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}
import React from 'react'
import { Provider } from 'react-redux'
import store from './src/redux/store'

export const wrapRootElement = ({ element }) => {
  return <Provider store={store}>{element}</Provider>
}

export function wrapPageElement({ element, props }) {
  const Layout = element.type.Layout ?? React.Fragment
  return <Layout {...props}>{element}</Layout>
}

// g-tag manager
export const onRenderBody = ({ setPreBodyComponents }) => {
  setPreBodyComponents([
    <script
      key="gatsby-plugin-google-gtag"
      async
      src="https://www.googletagmanager.com/gtag/js?id=G-NJTHJ14WHB"
    />,
    <script
      key="gatsby-plugin-google-gtag-config"
      dangerouslySetInnerHTML={{
        __html: `
          window.dataLayer = window.dataLayer || [];
          function gtag(){dataLayer.push(arguments);}
          gtag('js', new Date());

          gtag('config', 'G-NJTHJ14WHB');
        `,
      }}
    />,
    // <script async defer src="https://tools.luckyorange.com/core/lo.js?site-id=5aa77634"></script>
  ])
}
